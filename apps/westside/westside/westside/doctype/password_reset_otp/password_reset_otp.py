# Copyright (c) 2025, faircode and contributors
# For license information, please see license.txt

import base64
from frappe.model.document import Document
import frappe
import random
from frappe.utils import now_datetime
from datetime import timedelta


class PasswordResetOTP(Document):
	pass



@frappe.whitelist(allow_guest=True)
def generate_otp():
    try:
        user_id = frappe.form_dict.get("user_id")
        if not user_id:
            return {
                "status_code": 400,
                "message": "User ID is required."
            }

        if not frappe.db.exists("User", user_id):
            return {
                "status_code": 404,
                "message": "Invalid User ID."
            }

        otp = "".join(random.choices("**********", k=6))
        expiry_on = now_datetime() + timedelta(minutes=10)

        # Check if OTP row exists for this user_id
        existing_otp = frappe.db.get_value(
            "Password Reset OTP",
            {"user_id": user_id},
            ["name"]
        )

        if existing_otp:
            # Update existing OTP record
            otp_doc = frappe.get_doc("Password Reset OTP", existing_otp)
            otp_doc.otp = otp
            otp_doc.expiry_on = expiry_on
            otp_doc.save(ignore_permissions=True)
        else:
            # Create new OTP record
            otp_doc = frappe.get_doc({
                "doctype": "Password Reset OTP",
                "user_id": user_id,
                "otp": otp,
                "expiry_on": expiry_on
            }).insert(ignore_permissions=True)
            
			# Send OTP to user
        user_email = frappe.db.get_value("User", user_id, "email")
        if user_email:
            try:
                frappe.sendmail(
                    recipients=[user_email],
                    subject="Your OTP for Password Reset",
                    message=f"""
						Hello {user_id},<br><br>
						Your OTP for password reset is: <b>{otp}</b><br>
						It will expire in 10 minutes.<br><br>
						If you did not request this, please ignore this email.<br><br>
						Thanks,<br>Your Team <br><br>
						<small style="color:#999">Ref ID: {now_datetime().strftime("%Y%m%d%H%M%S")}</small>
					""",
                    now=True,
            		delayed=False
                )
            except Exception as email_error:
                frappe.log_error(frappe.get_traceback(), "Send OTP Email Error")
                return {
                    "status_code": 500,
                    "message": f"OTP generated but failed to send email: {str(email_error)}"
                }

        return {
            "status_code": 200,
            "message": "OTP generated successfully."
        }

    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Generate OTP Error")
        return {
            "status_code": 500,
            "message": str(e)
        }
    
def send_otp_email(user_id, otp):
	user_email = frappe.db.get_value("User", user_id, "email")
	if user_email:
		try:
			frappe.sendmail(
				recipients=[user_email],
				subject="Your OTP for Password Reset",
				message=f"""
					Hello {user_id},<br><br>
					Your OTP for password reset is: <b>{otp}</b><br>
					It will expire in 10 minutes.<br><br>
					If you did not request this, please ignore this email.
					<br><br>Thanks,<br>Your Team
				""",
				reference_doctype="User",
				reference_name=user_id
			)
		except Exception as email_error:
			frappe.log_error(frappe.get_traceback(), "Send OTP Email Error")
			return {
				"status_code": 500,
				"message": f"OTP generated but failed to send email: {str(email_error)}"
			}	
    

@frappe.whitelist(allow_guest=True)
def verify_otp():
	try:
		user_id = frappe.form_dict.get("user_id")
		otp = frappe.form_dict.get("otp")
		if not user_id or not otp:
			return {
				"status_code": 400,
				"message": "OTP is required."
			}

		otp_doc = frappe.get_doc("Password Reset OTP", {"user_id": user_id})
		if otp_doc.otp != otp:
			return {
				"status_code": 400,
				"message": "Invalid OTP."
			}	
		if otp_doc.expiry_on < now_datetime():
			return {
				"status_code": 400,
				"message": "OTP expired."
			}
		return {
			"status_code": 200,
			"message": "OTP verified successfully...!"
		}
	except Exception as e:
		frappe.log_error(frappe.get_traceback(), "Verify OTP Error")
		return {
			"status_code": 500,
			"message": str(e)
		}

def xor_decrypt(input_str: str, key: str) -> str:
    output = ""
    for i in range(len(input_str)):
        output += chr(ord(input_str[i]) ^ ord(key[i % len(key)]))
    return output	

@frappe.whitelist(allow_guest=True)
def reset_password():
	try:
		user_id = frappe.form_dict.get("user_id")
		encoded_password = frappe.form_dict.get("new_password")

		if not encoded_password:
			return {"status_code": 400, "message": "Password is required"}

		# Decode Base64
		decoded_bytes = base64.b64decode(encoded_password).decode("utf-8")
		custom_key = "D7A933CB5792D523FA1D6591CF152"
		new_password = xor_decrypt(decoded_bytes, custom_key)
		if not user_id or not new_password:
			return {
				"status_code": 400,
				"message": "User ID and new password are required."
			}
		user_doc = frappe.get_doc("User", user_id)
		user_doc.new_password = new_password
		user_doc.save(ignore_permissions=True)
		return {
			"status_code": 200,
			"message": "Password reset successfully."
		}
	except Exception as e:
		frappe.log_error(frappe.get_traceback(), "Reset Password Error")
		return {
			"status_code": 500,
			"message": str(e)
		}
		