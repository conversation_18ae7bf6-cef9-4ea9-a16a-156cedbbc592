{"actions": [], "allow_rename": 1, "autoname": "VEN-.#####", "creation": "2025-01-28 12:28:15.618541", "doctype": "DocType", "engine": "InnoDB", "field_order": ["section_break_1bag", "first_name", "last_name", "shipper", "vendor_name", "column_break_tdix", "parent_company", "company_name", "ein", "user_id", "section_break_urzi", "city", "zip", "column_break_nvlo", "state", "country", "section_break_zfne", "phone", "email_id", "vendor_code", "my_role", "column_break_sjbo", "contact", "vendor_address", "is_active", "pass_key"], "fields": [{"fieldname": "vendor_name", "fieldtype": "Data", "in_list_view": 1, "label": "Vendor Name", "reqd": 1}, {"fieldname": "parent_company", "fieldtype": "Data", "in_list_view": 1, "label": "Parent Company"}, {"fieldname": "city", "fieldtype": "Data", "label": "City"}, {"fieldname": "state", "fieldtype": "Data", "label": "State"}, {"fieldname": "zip", "fieldtype": "Data", "label": "Zip"}, {"fieldname": "country", "fieldtype": "Link", "label": "Country", "options": "Country", "reqd": 1}, {"fieldname": "phone", "fieldtype": "Data", "label": "Phone", "reqd": 1}, {"fieldname": "contact", "fieldtype": "Data", "label": "Contact", "reqd": 1}, {"fieldname": "email_id", "fieldtype": "Data", "label": "Email ID", "reqd": 1}, {"fieldname": "ein", "fieldtype": "Data", "in_list_view": 1, "label": "EIN "}, {"fieldname": "section_break_1bag", "fieldtype": "Section Break"}, {"fieldname": "column_break_tdix", "fieldtype": "Column Break"}, {"fieldname": "section_break_urzi", "fieldtype": "Section Break", "label": "ADDRESS"}, {"fieldname": "column_break_nvlo", "fieldtype": "Column Break"}, {"fieldname": "section_break_zfne", "fieldtype": "Section Break", "label": "CONTACT"}, {"fieldname": "column_break_sjbo", "fieldtype": "Column Break"}, {"fieldname": "shipper", "fieldtype": "Link", "in_list_view": 1, "label": "Shipper", "options": "Shipper"}, {"fieldname": "vendor_address", "fieldtype": "Small Text", "label": "<PERSON><PERSON>or Address"}, {"fieldname": "first_name", "fieldtype": "Data", "label": "First Name"}, {"fieldname": "last_name", "fieldtype": "Data", "label": "Last Name"}, {"default": "1", "fieldname": "is_active", "fieldtype": "Check", "label": "Is_active"}, {"fieldname": "company_name", "fieldtype": "Data", "label": "Company Name"}, {"fieldname": "user_id", "fieldtype": "Link", "label": "User Id", "options": "User", "read_only": 1}, {"fieldname": "pass_key", "fieldtype": "Table", "label": "Pass Key", "options": "Vendor Pass Key"}, {"default": "0", "fieldname": "my_role", "fieldtype": "Check", "label": "My Role"}, {"fieldname": "vendor_code", "fieldtype": "Data", "label": "Vendor Code"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-10-03 12:12:23.218509", "modified_by": "Administrator", "module": "Westside", "name": "<PERSON><PERSON><PERSON>", "naming_rule": "Expression", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": []}