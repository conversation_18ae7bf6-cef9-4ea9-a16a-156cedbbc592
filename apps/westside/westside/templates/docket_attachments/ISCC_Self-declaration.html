<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>ISCC Self-Declaration</title>
</head>
<body>
    <div style="background-color: white; border: 1px solid #ddd; padding: 15px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
        <h4 style="text-align: center; color: #333;">ISCC Self-Declaration for Points of Origin Generating Waste and Residues</h4>

        <form>
            <table style="font-size: 15px;  width: 100%; border-collapse: collapse;">
                <tr>
                    <td colspan="2" style="border: 1px solid #ddd; padding: 8px; font-weight: 800;">
                      Information about the Point of Origin:
                    </td>
                </tr>
                      
                <tr>
                    <td style="border: 1px solid #ddd; padding: 8px; width: 30%;">Name</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">{{ doc.docket.shipper_name if doc.docket and doc.docket.shipper_name  else " " }}</td>
                </tr>
                <tr>
                    <td style="border: 1px solid #ddd; padding: 8px; width: 30%;">Street address</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">{{ doc.docket.shipper_doc.street_address if doc.docket.shipper_doc and doc.docket.shipper_doc.street_address else " " }}</td>
                </tr>
                <tr>
                    <td style="border: 1px solid #ddd; padding: 8px; width: 30%;">Postcode, location</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">{{ doc.docket.shipper_doc.postal_code if doc.docket.shipper_doc and doc.docket.shipper_doc.postal_code  else " " }} {{ doc.docket.shipper_doc.location_name if doc.docket.shipper_doc and doc.docket.shipper_doc.location_name  else " " }}</td>
                </tr>
                <tr>
                    <td style="border: 1px solid #ddd; padding: 8px;  width: 30%;">Country</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">{{ doc.docket.shipper_doc.country if doc.docket.shipper_doc and doc.docket.shipper_doc.country  else " " }}</td>
                </tr>
                <tr>
                    <td style="border: 1px solid #ddd; padding: 8px;">Phone number</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">{{ doc.docket.shipper_doc.phone if doc.docket.shipper_doc and doc.docket.shipper_doc.phone  else " " }}</td>
                </tr>
                </table>
                
                <table style="width: 100%; border-collapse: collapse;">
                <tr>
                    <td colspan="2" style="font-size: 14px; border: 1px solid #ddd; padding: 8px;">
                        <div style="margin-bottom: 2px;">The delivered material consists of the following waste or residues:</div>
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <h4 style="margin: 0;">END OF LIFE TYRES</h4> 
                            <p style="margin: 0; margin-left: 8px;">{{ doc.docket.blno or " " }}</p>
                        </div>
                        <div style="font-size: 12px; color: #555; margin-top: 5px;">
                            <i>Note: List each waste or residue delivered. Identify each clearly, and give the waste codes (if applicable) according to the relevant national waste ordinance - if you are entitled to do so (and if applicable).</i>
                        </div>
                    </td>
                </tr>
                </table>
                <table style="width: 100%; border-collapse: collapse;">
                <tr>
                    <td style="font-size: 15px; border: 1px solid #ddd; padding: 8px; width:95%">The amount of waste and residues generated by the Point of Origin is five (5) or more metric tons per month</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">
                        <label style="position: relative; display: inline-block;">
                          <input 
                            type="checkbox"  checked  style="opacity: 0; position: absolute;">
                          <span 
                            style="display: inline-block; width: 20px; height: 20px; border: 1px solid #ddd; background: white;
                              text-align: center; line-height: 18px; font-size: 18px; color: rgb(0, 0, 0);"
                            >✓</span>
                        </label>
                      </td>
                </tr>
                </table>
                <table style="width: 100%; border-collapse: collapse;">
                <tr>
                    <td style="font-size: 15px; border: 1px solid #ddd; padding: 8px; width:35%">Recipient of the waste or residue (Collecting Point)</td>
                    <td style="font-size: 10px; border: 1px solid #ddd; padding: 8px;">
                        {{ doc.docket.customer if doc.docket.customer else " " }}  
                        {{ doc.docket.consignee if doc.docket.consignee else " "}}
                    </td>
                </tr>
            </table>

            <div style="background-color: #f9f9f9; padding: 15px; border: 1px solid #ddd; margin-bottom: 20px;">
                <h4 style="margin-top: 0;">By signing this self-declaration, the signatory acknowledges and confirms the following:</h4>
                <ol style="padding-left: 20px; font-size: 12px;">
                    <li>The material supplied under this self-declaration meets the definition of “waste” or “residue”. <br/>
                        A <b>waste</b> is any substance or object which the holder discards or intends or is required to discard, excluding substances that have been intentionally modified or contaminated in order to meet this definition.<br/>
                        A <b>residue</b> is a substance that is not the end product that a production process directly seeks to produce; it is not a primary aim of the production process and the process has not been deliberately modified to produce it.</li>
                    <li>In case of residues that are directly generated by agriculture, aquaculture, fisheries and forestry, the material fulfils the land related sustainability requirements laid down in Art. 29 of Directive (EU) 2018/2001 (RED II).</li>
                    <li>The material supplied consists only of biomass defined as the biodegradable fraction of products, waste and residues from biological origin from agriculture (including vegetal and animal substances), forestry and related industries including fisheries and aquaculture, as well as the biodegradable fraction of industrial and municipal waste.</li>
                    <li>Documentation of quantities supplied is available.</li>
                    <li>Applicable national legislation regarding waste prevention and management (e.g. for transport, supervision, etc.) are complied with. If veterinary certificates exist, these are to be kept together with the commercial documents.</li>
                    <li>The supplied material is exclusively generated by the signing point of origin.</li>
                    <li>Auditors from certification bodies or from ISCC, both possibly accompanied by a representative of the Collecting Point, can examine, with or without prior announcement, on-site or by contacting the signatory (e.g. via telephone), whether the relevant requirements of ISCC EU are complied with and the statements made in this self-declaration are correct. Auditors of certification bodies may be accompanied by inspectors who monitor their activities.</li>
                    <li>The information on this self-declaration may be forwarded by the Collecting Point to and reviewed by the certification body of the Collecting Point and by ISCC. The certification body and ISCC keep all data provided in this self-declaration confidential and will not disclose them to third parties unless there are legal obligations, official or judicial orders or requirements of the European Commission applying to ISCC EU to disclose them to third parties.</li>
                    <li>If audits of certification bodies or ISCC reveal that relevant ISCC requirements are not complied with or declarations made in this self-declaration are not correct, and if the Point of Origin is thereupon excluded as supplier of ISCC certified material, ISCC is entitled to <b>publish the exclusion</b> of the Point of Origin on the ISCC website.</li>
                </ol>
            </div>

            <table style="width: 100%; border-collapse: collapse;">
                 <tr>
                    <td style="border: 1px solid #ddd; padding: 8px; text-align: center; align-content: center;">{{ doc.docket.shipper_doc.location_name if doc.docket.shipper_doc and doc.docket.shipper_doc.location_name  else " " }}, {{ doc.current_date }}</td>
                    <td style="border: 1px solid #ddd; padding: 8px; text-align: center; align-content: center;">Chaitanya Uppalapati, president</td>
                    <td style="border: 1px solid #ddd; padding: 8px; text-align: center; vertical-align: middle; align-content: center">
                        <img src="assets/westside/images/signature.png" alt="Signature"
                             style="height: 48px; width: auto; display: inline-block;" />
                    </td>
                </tr>
                <tr>
                    <th style="font-size: 14px; border: 1px solid #ddd; padding: 8px;">Place, date</th>
                    <th style="font-size: 14px; border: 1px solid #ddd; padding: 8px;">Full name and function of signatory</th>
                    <th style="font-size: 14px; border: 1px solid #ddd; padding: 8px;">Signature</th>
                </tr>
               
            </table>
        </form>

        <img 
            src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSjSvd1G3hndLaGLINPcji55aBlR_dOgK4Mkw&s" 
            alt="ISCC Logo" 
            style="width: 100px; margin-top: 20px;" 
            />
    </div>
</body>
</html>
