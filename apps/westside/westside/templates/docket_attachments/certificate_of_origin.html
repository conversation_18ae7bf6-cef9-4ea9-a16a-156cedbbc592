<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Certificate of Origin</title>
  <style>
    @page {
      size: A4;
      margin: 0;
      margin-top: 15mm;
      margin-bottom: 25mm;
    }

    body {
      margin: 0;
      padding: 0;
      width: 100%;
      background-color: #fff;
      font-family: 'Lucida Sans', Verdana, sans-serif;
      font-size: 1.4rem;
    }

    .page {
      width: 360mm;
      min-height: calc(567mm - 40mm);
      box-sizing: border-box;
      background-color: #fff;
      position: relative;
      display: flex;
      flex-direction: column;
      padding: 5mm;
      margin: 0 auto;
      page-break-after: always;
    }
    .page:last-child {
      page-break-after: auto;
    }

    .footer {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      text-align: center;
      border: 1px solid #059669;
      background-color: #94c92c;
      padding: 10px;
      width: 100%;
      box-sizing: border-box;
      font-family: Cambria, Cochin, Georgia, Times, 'Times New Roman', serif;
      font-size: 18px;
      line-height: 1.25;
      color: #dc2626;
      z-index: 1000;
    }
    .footer1 a {
      color: #1e40af;
      text-decoration: underline;
    }

    .footer1 {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      text-align: center;
      border: 1px solid #059669;
      background-color: #94c92c;
      padding: 10px;
      width: 100%;
      box-sizing: border-box;
      font-family: Cambria, Cochin, Georgia, Times, 'Times New Roman', serif;
      font-size: 18px;
      line-height: 1.25;
      color: #dc2626;
      z-index: 1000;
    }
    .footer a {
      color: #1e40af;
      text-decoration: underline;
    }

    .header {
      margin-bottom: 1.25rem;
      display: flex;
      justify-content: flex-end;
    }
    .logo {
      width: 18rem;
      height: 5rem;
    }
    .divider {
      border-top: 1px solid #d1d5db;
      margin: 1.25rem 0;
    }
    .title {
      text-align: center;
      font-weight: 900;
      text-decoration: underline;
      margin: 2rem 0;
      font-size: 2.5rem;
    }
    .section {
      margin-bottom: 2rem;
    }
    .row {
      display: flex;
      margin-bottom: 0.25rem;
    }
    .label {
      width: 25%;
      font-weight: 600;
    }
    .value {
      width: 75%;
      font-weight: 600;
    }
    .half-container {
      display: flex;
    }
    .half {
      width: 55%;
    }
    .half-right {
      width: 45%;
      font-weight: 600;
      padding-left: 0.4rem;
    }
    .pre-line {
      white-space: pre-line;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      border: 1px solid black;
      page-break-inside: auto;
    }
    thead {
      background-color: #f3f4f6;
      display: table-header-group;
    }
    tbody tr {
      page-break-inside: avoid;
      page-break-after: auto;
    }
    th, td {
      border: 1px solid black;
      padding: 0.5rem;
      text-align: center;
      vertical-align: top;
    }
  </style>
</head>
<body>

  <!-- === PAGE 1: Static content + first equipment page === -->
  <div class="page">
    <div class="header">
      <img src="assets/westside/images/logo.png" alt="Westside Exports Logo" class="logo" />
    </div>

    <div class="divider"></div>

    <h1 class="title">CERTIFICATE OF ORIGIN</h1>

    <div class="section">
      <div class="half-container">
        <div class="half">
          <div class="row">
            <div style="width:41%;font-weight:600;">SHIPPED BY</div>
            <div style="width:60%;font-weight:600;">: {{ doc.docket.shipper_name or " " }}</div>
          </div>

          <div class="row">
            <div style="width:41%;font-weight:600;">LOADING PORT</div>
            <div style="width:33%;font-weight:600;">: {{ doc.docket.origin or " " }}</div>
          </div>

          <div class="row">
            <div style="width:41%;font-weight:600;">DESTINATION</div>
            <div style="width:33%;font-weight:600;">: {{ doc.docket.destination or " " }}</div>
          </div>

          <div class="row spacer">
            <div style="width:41%;font-weight:600;">DELIVERY TERMS</div>
            <div style="width:33%;font-weight:600;">: {{ doc.docket.terms or " " }}</div>
          </div>

          <div class="row">
            <div style="width:41%;font-weight:600;">B/L No.</div>
            <div style="width:45%;font-weight:600;">: {{ doc.docket.blno or " " }}</div>
          </div>
        </div>
        <div class="half-right">
          {{ doc.docket.customer if doc.docket.customer else "" }}<br>
          {{ doc.docket.consignee if doc.docket.consignee else "" }}<br>
        </div>
      </div>
    </div>

    <div class="section">
      <div class="row spacer">
        <div class="label">ORIGIN OF GOODS</div>
        <div class="value">: {{ doc.docket.origin_of_goods or " " }}</div>
      </div>

      <div class="row spacer">
        <div class="label">DESCRIPTION OF GOODS</div>
        <div class="value pre-line" style="font-weight:400">: {{ doc.docket.material or " " }}</div>
      </div>

      <div class="row spacer">
        <div class="label">HS CODE</div>
        <div class="value pre-line">: {{ doc.docket.hs_code or " " }}</div>
      </div>
    </div>

    <div class="table-container" id="tableContainer">
      <table>
        <thead>
          <tr>
            <th>Sno</th>
            <th>Container #</th>
            <th>Seal #</th>
            <th>Net Weight MT</th>
            <th>Package Type</th>
          </tr>
        </thead>
        <tbody>
          {% if doc.equipments_pages != [] %}
            {% for equipment in doc.equipments_pages[0] %}
            <tr>
              <td>{{ loop.index }}</td>
              <td>{{ equipment.equipment_name or ' ' }}</td>
              <td>{{ equipment.shipper_seal_number or '' }}</td>
              <td>{{
                ('{:.2f}'.format(equipment.cargo.net_weight / 1000) 
                 if equipment.cargo and equipment.cargo.net_weight else ' ')
                }}</td>
              <td>{{doc.docket.packing_count if doc.docket.packing_count else ''}} &nbsp; {{doc.docket.packing_type if doc.docket.packing_type else ''}}</td>
            </tr>
            {% endfor %}
          {% endif %}
        </tbody>
      </table>
    </div>

    <div class="footer">
      Westside Exports LLC |
      <span style="color: #1e40af;">
        Tel: 001-************, 001-************
      </span> |
      4017 VALLONIA DR CARY NC 27519<br />
      <span style="color: black;">
        Email: <a href="mailto:<EMAIL>"><EMAIL></a>
      </span> |
      Website: <a href="https://www.westsidex.com">www.westsidex.com</a>
    </div>
  </div>

  <!-- === PAGE 2 and onwards: Only equipment tables === -->
  {% if doc.equipments_pages[1:] != [] %}
    {% for page in doc.equipments_pages[1:] %}
    <div class="page">
      <div class="table-container">
        <table>
          <thead>
            <tr>
              <th>Sno</th>
              <th>Container #</th>
              <th>Seal #</th>
              <th>Net Weight MT</th>
              <th>Package Type</th>
            </tr>
          </thead>
          <tbody>
            {% for equipment in page %}
            <tr>
              <td>{{ loop.index + 22 }}</td>
              <td>{{ equipment.equipment_name or ' ' }}</td>
              <td>{{ equipment.shipper_seal_number or '' }}</td>
              <td>{{
                ('{:.2f}'.format(equipment.cargo.net_weight / 1000) 
                 if equipment.cargo and equipment.cargo.net_weight else ' ')
                }}</td>
              <td>{{doc.docket.packing_count if doc.docket.packing_count else ''}} &nbsp; {{doc.docket.packing_type if doc.docket.packing_type else ''}}</td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>

      <div class="footer">
        Westside Exports LLC |
        <span style="color: #1e40af;">
          Tel: 001-************, 001-************
        </span> |
        4017 VALLONIA DR CARY NC 27519<br />
        <span style="color: black;">
          Email: <a href="mailto:<EMAIL>"><EMAIL></a>
        </span> |
        Website: <a href="https://www.westsidex.com">www.westsidex.com</a>
      </div>
    </div>
    {% endfor %}
  {% endif %}

</body>
</html>
