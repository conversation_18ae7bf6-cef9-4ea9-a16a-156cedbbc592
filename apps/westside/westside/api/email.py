import frappe


@frappe.whitelist()
def send_docket_email_async(email_id, subject, message, attachments, cc=None):
    try:
        frappe.sendmail(
            recipients=[email_id],
            subject=subject,
            message=message,
            cc=cc,
            attachments=attachments,
            now=True,
            delayed=False
        )
    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Async Email Sending Error")

