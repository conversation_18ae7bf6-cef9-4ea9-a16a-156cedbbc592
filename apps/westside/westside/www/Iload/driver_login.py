import frappe, random
from westside.www.Authentication.auth_decorators import role_required




@frappe.whitelist(allow_guest=True)
def driver_login_with_passkey(passkey):
    """
    Driver login using a passkey.
    Validates if passkey exists in <PERSON><PERSON><PERSON>'s child table 'Passkey'.
    """

    if not passkey:
        return {"status_code": 400, "message": "Passkey is required."}

    # Check in Passkey child table
    passkey_doc = frappe.db.get_value(
        "Vendor Pass Key",
        {"passkey": passkey},
        ["parent", "name"], 
        as_dict=True
    )

    if not passkey_doc:
        return {"status_code": 401, "message": "Invalid passkey."}

    vendor_id = passkey_doc.get("parent")

    return {
        "status_code": 200,
        "message": "Driver authenticated successfully.",
        "vendor_id": vendor_id,
        "passkey_row_id": passkey
    }

