import json
import frappe
from frappe.utils.file_manager import save_file
from westside.www.Authentication.auth_decorators import role_required


def get_assigned_containers(booking_request_name):
    if not booking_request_name:
        return {
            "status_code": 400,
            "message": "Booking Request Id is required."
        }

    try:
        booking_request = frappe.get_doc('Booking Request', booking_request_name)
        
        # get the number of containers requested to inttra
        container_list = frappe.get_all(
            "Booking Container",
            filters={"parent": booking_request.name},
            fields=["number_of_containers"]
        )
        no_of_containers = sum(int(container["number_of_containers"]) for container in container_list)

        result = {
            'name': booking_request.name,
            'no_of_containers': no_of_containers,
        }
    
        equipments = frappe.get_all(
            'Equipments', 
            filters={'booking_request': booking_request.name, "is_active": True}, 
            fields=[
                'name', 'equipment_name', 'code_value', 'shipper_seal_number', 'carrier_seal_number',
                'description', 'supplier_type', 'service_type','weight_value', 'weight_type', 'job',
                'tare_weight', 'cargo_weight', 'gross_weight', 'max_weight', 'cubic_capacity', "creation"
            ], 
            order_by='creation desc'
        )

        # Count only the equipments that have a non-empty 'job' field
        job_count = len([e for e in equipments if e.get('job')])
        result['number_of_assigned_containers'] = job_count
        
        return result
    
    except Exception as e:
        return {
            "status_code": 500,
            "message": f"Error fetching assigned containers: {str(e)}"
        }



@frappe.whitelist(allow_guest=True)
def get_job_by_booking_number(carrier_booking_number: str, vendor_id: str):
    """
    Fetch Job details for a given Vendor and Carrier Booking Number,
    along with container assignment info.
    """

    if not carrier_booking_number or not vendor_id:
        return {"status_code": 400, "message": "Both carrier_booking_number and vendor_id are required."}

    # Find booking request linked to this carrier_booking_number
    booking_doc = frappe.get_value("Booking Request", {"carrier_booking_number": carrier_booking_number}, "name")
    if not booking_doc:
        return {"status_code": 404, "message": "No Booking Request found for given carrier_booking_number."}

    # Look up Job by vendor and carrier booking number
    job = frappe.get_value(
        "Job",
        {"vendor_name": vendor_id, "carrier_booking_number": carrier_booking_number},
        ["name", "vendor_name", "carrier_booking_number", "status"],
        as_dict=True
    )

    if not job:
        return {
            "status_code": 404,
            "message": "No Job found for given Vendor and Carrier Booking Number."
        }

    # Get container stats
    container_stats = get_assigned_containers(booking_doc)

    return {
        "status_code": 200,
        "message": "Job found successfully.",
        "job": job,
        "container_stats": container_stats
    }




@frappe.whitelist(allow_guest=True)
def upsert_equipment():
    """
    Create or update a single Equipment for a given Job.
    Equipment data is taken from request body (JSON).
    """
    try:
        
        try:
            request_data = json.loads(frappe.form_dict.get("data") or "{}")
        except Exception:
            request_data = {}

         # ---- Job Id ----
        job_id = frappe.local.request.args.get("job_id")

        if not job_id:
            return {"status_code": 400, "message": "Job ID is required."}

        if not frappe.db.exists("Job", job_id):
            return {"status_code": 404, "message": "Job not found"}

        job_doc = frappe.get_doc("Job", job_id)

        if job_doc.status == "Cancelled":
            return {"status_code": 400, "message": "This job is cancelled. Can't update."}

        # ---- Get equipment_data from request body ----
        equipment_data = request_data
       
        if not equipment_data:
            return {"status_code": 400, "message": "equipment_data is required in body."}

        booking_id = job_doc.booking_id
        equipment_name = equipment_data.get("equipment_name")

        equipment_doc = None
        is_existing = False
      
        # Case 1: Update existing equipment by ID
        if equipment_name and frappe.db.exists(
                "Equipments",
                {"equipment_name": equipment_name, "booking_request": booking_id, "is_active": True}
            ):
                equipment_doc = frappe.get_doc("Equipments", {"equipment_name": equipment_name, "booking_request": booking_id, "is_active": True})
                is_existing = True
                equipment_doc.job = job_doc.name
        # Case 2: Create new equipment
        else:
            if equipment_name and frappe.db.exists(
                "Equipments",
                {"equipment_name": equipment_name, "booking_request": booking_id, "is_active": True}
            ):
                return {"status_code": 400, "message": f"Equipment '{equipment_name}' already exists for this booking."}

            equipment_doc = frappe.new_doc("Equipments")
            equipment_doc.booking_request = booking_id
            equipment_doc.job = job_doc.name
            is_existing = False

        # Allowed update fields
        allowed_update_fields = [
            "equipment_name",
            "shipper_seal_number",
            "tare_weight",
            "cargo_weight",
            "gross_weight",
            "max_weight",
            "cubic_capacity",
            "driver"
        ]

        for key in allowed_update_fields:
            if key in equipment_data:
                setattr(equipment_doc, key, equipment_data[key])
        
        equipment_doc.save(ignore_permissions=True)
        frappe.db.commit()
                
        
        # ---- Handle Job Images ----
        site_url = frappe.utils.get_url()
        equipment_images = frappe.request.files.getlist("equipment_images") if frappe.request.files else []

        if equipment_images:

            equipment_doc.set("container_goods_image", [])   # clear existing images first
            # Uploaded files
            for img in equipment_images:
                file_doc = save_file(
                    img.filename,
                    img.stream.read(),
                    "Equipments",
                    equipment_doc.name,
                    is_private=0
                )
                equipment_doc.append("container_goods_image", {
                    "container_image": site_url + file_doc.file_url,
                    "file_name": img.filename
                })

        equipment_doc.save(ignore_permissions=True)
        frappe.db.commit()

        return {
            "status_code": 200 if is_existing else 201,
            "message": "Equipment updated successfully" if is_existing else "Equipment created successfully",
            "equipment_id": equipment_doc.name,
            "job_id": job_doc.name
        }

    except frappe.ValidationError as e:
        frappe.db.rollback()
        return {"status_code": 400, "message": f"Validation Error: {str(e)}"}
    except Exception as e:
        frappe.db.rollback()
        frappe.log_error(frappe.get_traceback(), "Error in upsert_equipment")
        return {"status_code": 500, "message": f"Internal Server Error: {str(e)}"}



@frappe.whitelist(allow_guest=True)
def equipment_update_history(driver_id=None, job_id=None):
    """
    History of update Equipment for a given Job by the driver.
    """

    if not driver_id:
        return {"status_code": 400, "message": "Driver ID is required."}

    if not job_id:
        return {"status_code": 400, "message": "Job ID is required."}

    if not frappe.db.exists("Job", job_id):
        return {"status_code": 404, "message": "Job not found"}
    
    # Check in Passkey child table
    passkey_doc = frappe.db.get_value(
        "Vendor Pass Key",
        {"passkey": driver_id},
        ["parent", "name"], 
        as_dict=True
    )

    if not passkey_doc:
        return {"status_code": 401, "message": "Invalid Driver Id."}


    job_doc = frappe.get_doc("Job", job_id)

    if job_doc.status == "Cancelled":
        return {"status_code": 400, "message": "This job is cancelled. Can't update."}

    booking_id = job_doc.booking_id

    equipments = frappe.get_all(
        "Equipments",
        filters={
            "booking_request": booking_id,
            "job": job_doc.name,
            "is_active": True
        },
        fields=["name", "equipment_name", "booking_request", "job", "is_active", "driver", "cubic_capacity", "max_weight", "shipper_seal_number"]
    )

    # Include container images for each equipment
    equipments_with_images = []
    for eq in equipments:
        eq_doc = frappe.get_doc("Equipments", eq.name)

        images = []
        for img in eq_doc.get("container_goods_image") or []:
            images.append({
                "container_image": img.container_image,
                "file_name": img.file_name
            })
        eq_data = eq.copy()
        eq_data["container_goods_image"] = images
        equipments_with_images.append(eq_data)


    return {
        "status_code": 200,
        "message": "Equipments Fetched successfully",
        "job_id": job_doc.name,
        "equipments": equipments_with_images
    }

  