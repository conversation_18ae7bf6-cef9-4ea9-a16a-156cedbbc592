import frappe
import json
from frappe import _
from datetime import datetime
from westside.www.Authentication.auth_decorators import role_required



@frappe.whitelist()
@role_required(["Customer"])
def get_all_docket(search=None, status=None, page=1, page_size=10):
    try:
        try:
            page = int(page)
            page_size = int(page_size)
        except ValueError:
            frappe.response["http_status_code"] = 400
            frappe.response["message"] = {"error": "Invalid page or page_size value. Must be an integer."}
            return
                                                                                                                            
        if page < 1 or page_size < 1:
            frappe.response["http_status_code"] = 400
            frappe.response["message"] = {"error": "Page and page_size must be greater than zero."}
            return
    
        customer = frappe.session.user # current user
        customer_id = frappe.get_value("Customer DB", {"email_id": customer}, "name")

        filters = {}
        filters["customer_id"] = customer_id
        if status:
            filters["status"] = status,

        offset = (page - 1) * page_size

        or_filters = []
        if search:
            search_term = f"%{search}%"
            or_filters.append(["customer_name", "like", search_term])
            or_filters.append(["name", "like", search_term])
            
        docket_list = frappe.get_all(
            "Docket DB",
            filters=filters,
            or_filters=or_filters, 
            fields=[
                "name", "booking", "customer", "customer_name", "revision_number", "shipper", "blno", 'carrier_booking_number',
                "shipping_date", "origin", "origin_of_goods", "status", "destination", "package_count", "package_type"
            ],
            limit_start=offset,
            limit_page_length=page_size
        )

        response_data = []
        for docket in docket_list:

            bill_of_lading_doc = None
            if frappe.db.exists("Bill of Lading", {"bol_number": docket.blno}):
                bill_of_lading_doc = frappe.get_doc("Bill of Lading", {"bol_number": docket.blno})


            port_of_origin = None
            place_of_carrier_reciept = None
            ETA = None
            sail_date = bill_of_lading_doc.main_transport_sail_date if bill_of_lading_doc and bill_of_lading_doc.main_transport_sail_date else None
                
            if docket.carrier_booking_number:

                booking_doc = None
                if frappe.db.exists("Booking Request", {"carrier_booking_number": docket.carrier_booking_number}):
                    booking_doc = frappe.get_doc("Booking Request", {"carrier_booking_number": docket.carrier_booking_number})

                if booking_doc:

                    # place_of_carrier_reciept
                    if booking_doc.place_of_carrier_receipt:
                        place_of_carrier_reciept = frappe.get_value(
                                "UNLOCODE Locations",
                                booking_doc.place_of_carrier_receipt,
                                ["name", "locode", "country_code", "country", "location_name"],
                                as_dict=True
                            )

                    # ETA
                    carriages = frappe.get_all("Booking Main Carriage", filters={"parent": booking_doc}, fields=["eta", "etd"])
                    if carriages:
                        # Sort by etd, latest first
                        carriages_sorted = sorted(carriages, key=lambda x: x.etd or datetime.min)
                        latest = carriages_sorted[-1]
                        ETA = latest.eta.strftime("%d-%b-%Y") if latest and latest.eta else None

                 

            invoice_total = None
            if docket.customer_invoice_id:
                try:
                    invoice_doc = frappe.get_doc("Invoice DB", docket.customer_invoice_id) 
                    invoice_total = invoice_doc.total_amount
                except:
                    frappe.log_error(f"Invoice not found: {docket.customer_invoice_id}", "Invoice Lookup Failed")
                    pass
            
            origin = None
            if docket.origin:
                origin = frappe.get_value(
                    "UNLOCODE Locations", 
                    docket.origin,
                    ["name", "locode", "country_code", "country", "location_name"],
                    as_dict=True
                )

            try:
                docket_data = {
                    "id": docket.name,
                    "booking_id": docket.booking,
                    "carrier_booking_number": docket.carrier_booking_number,
                    "customer_id": docket.customer_id,
                    "customer_name": docket.customer_name,
                    "revision_number": docket.revision_number,
                    "shipper": docket.shipper,
                    "bol_number": docket.blno,
                    "shipping_date": docket.shipping_date,
                    "port_of_origin" : docket.origin,
                    "place_of_receipt": docket.destination,
                    "sail_date": sail_date,
                    "shipping_line_no": docket.blno, 
                    "carrier": bill_of_lading_doc.carrier if bill_of_lading_doc and bill_of_lading_doc.carrier else "", 
                    "container_qty": bill_of_lading_doc.total_equipment if bill_of_lading_doc and bill_of_lading_doc.total_equipment else "", 
                    "weight": docket.weight,
                    "invoice_no": docket.invoice,
                    "invoice_total": invoice_total,
                    "status": docket.status, 
                    "revision": docket.revision_number,
                    "ETA": ETA,
                    "package_count": docket.package_count,
                    "package_type": docket.package_type
                }
                response_data.append(docket_data)

            except Exception as docket_error:
                frappe.log_error(f"Error processing docket {docket.name}: {str(docket_error)}", "Docket Processing Error")

        total_count = len(frappe.get_all(
                            "Docket DB",
                            filters=filters,
                            or_filters=or_filters,
                            fields=["name"]
                        ))
        total_pages = (total_count + page_size - 1) // page_size

        frappe.response["message"] = {
            "total_count": total_count,
            "page": page,
            "page_size": page_size,
            "total_pages": total_pages,
            "docket": response_data
        }

    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Get All Docket API Error")
        frappe.response["http_status_code"] = 500
        frappe.response["message"] = {"error": str(e)}



@frappe.whitelist()
@role_required(["Customer"])
def get_docket_details(docket_id):
    try:
        if not docket_id:
            return {
                "status": 400,
                "message": "Docket ID is required"
            }

        docket = frappe.get_doc("Docket DB", docket_id)

        customer = frappe.session.user # current user
        customer_id = frappe.get_value("Customer DB", {"email_id": customer}, "name")
        
        if docket.customer_id != customer_id:
            return {"status": 400, "message": "You don't have permission to access this docket."}

        customer_doc = frappe.get_doc("Customer DB", docket.customer_id)
        customer = {}
        customer["customer_id"] = customer_doc.name
        customer["first_name"] = customer_doc.first_name
        customer["last_name"] = customer_doc.last_name
        customer["customer_name"] = customer_doc.customer_name
        customer["parent_company"] = customer_doc.parent_company
        customer["phone"] = customer_doc.phone
        customer["email_id"] = customer_doc.email_id

        if docket.hs_code:
            hs_code_data = frappe.db.get_value(
                "HS Code", 
                docket.hs_code, 
                ["name", "hs_code", "hs_code_description"],
                as_dict=True
            )
        
        if docket.origin:
            origin_data = frappe.db.get_value(
                "UNLOCODE Locations", 
                docket.origin, 
                ["name", "locode", "country_code", "country", "location_name"],
                as_dict=True
            )
             
        if docket.destination:
            destination_data = frappe.db.get_value(
                "UNLOCODE Locations", 
                docket.destination, 
                ["name", "locode", "country_code", "country", "location_name"],
                as_dict=True
            )

        if docket.origin_of_goods:
            origin_of_goods_data = frappe.db.get_value(
                "UNLOCODE Locations", 
                docket.origin_of_goods, 
                ["name", "locode", "country_code", "country", "location_name"],
                as_dict=True
            )
        

        docket_data = {
            "docket_id": docket.name,
            "booking": docket.booking,
            "carrier_booking_number": docket.carrier_booking_number,
            "revision_number": len(docket.docket_revisions),
            "blno": docket.blno,
            "origin": docket.origin,
            "status": docket.status
        }

        if docket.status == "Sent":
            docket.status = "Acknowledged"

            if docket.docket_revisions:
                # lastest revision
                latest_revision = max(docket.docket_revisions, key=lambda x: int(x.revision_number))
                latest_revision.status = "Acknowledged"
               
        docket.save(ignore_permissions=True)
        frappe.db.commit()

        return {
            "status": 200,
            "message": "Docket details fetched successfully",
            "data": docket_data
        }

    except frappe.DoesNotExistError:
        return { 
            "status": 404, 
            "message": "Docket not found" 
        }
        
    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Get Docket Detail API Error")
        return {
            "status": 500,
            "message": {"error": str(e)}
        }
