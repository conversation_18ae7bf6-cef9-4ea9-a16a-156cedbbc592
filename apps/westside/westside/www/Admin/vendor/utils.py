import random
import frappe



def generate_vendor_code(vendor_name: str) -> str:
    """
    Generate a unique vendor code from the vendor name.
    Format: First letter of first 2 words + random 2-digit number
    Example: "Emanuel Tyre" -> "ET23"
    """
    if not vendor_name:
        return ""

    # Take first two words
    words = vendor_name.strip().split()
    if len(words) == 1:
        initials = words[0][0].upper()
    else:
        initials = words[0][0].upper() + words[1][0].upper()

    # Keep trying until unique code found
    while True:
        rand_num = random.randint(10, 99)  # 2-digit
        code = f"{initials}{rand_num}"

        # Check if code already exists in Vendor doctype
        exists = frappe.db.exists("Vendor", {"vendor_code": code})
        if not exists:
            return code





# Driver Code
def generate_unique_passkey(short_name):
    """Generate a unique 3-digit key not used in any Vendor Passkey child table"""
    while True:
        if short_name != "":
            new_key = f"{short_name}-{random.randint(100, 999)}"
        else:
            new_key = f"{random.randint(100, 999)}"

        exists = frappe.db.exists("Vendor Pass Key", {"passkey": new_key})
        if not exists:
            return new_key

