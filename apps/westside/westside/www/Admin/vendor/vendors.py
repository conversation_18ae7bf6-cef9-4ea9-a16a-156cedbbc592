import json
import random
import frappe
from frappe import _
from westside.www.Authentication.auth_decorators import role_required
from westside.www.Admin.vendor.utils import generate_vendor_code, generate_unique_passkey


@frappe.whitelist()
@role_required(["Admin"])
def get_all_countries():
    try:
        # Step 1: Get distinct country codes from Customer DB
        used_country_codes = frappe.db.get_list(
            "Vendor",
            fields=["distinct country"],
            filters={"country": ["!=", ""], "is_active": True},
            pluck="country"
        )

        if not used_country_codes:
            return {
                'status': 200,
                'message': 'No countries found with vendor',
                'countries': []
            }

        # Step 2: Fetch country details for those codes
        countries = frappe.get_all(
            "Country",
            filters={"name": ["in", used_country_codes]},
            fields=["name", "country_name", "code"]
        )

        return {
            'status': 200,
            'message': 'Countries with vendor fetched successfully',
            'countries': countries
        }

    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "get_all_countries Error")
        return {
            'status': 500,
            'message': f'Internal Server Error: {str(e)}'
        }
    


    
@frappe.whitelist()
@role_required(["Admin"])
def get_all_vendors(search=None, company=None, country=None, page=1, page_size=10):
    try:
        try:
            page = int(page)
            page_size = int(page_size)
        except ValueError:
            frappe.response["http_status_code"] = 400
            frappe.response["message"] = {"error": "Invalid page or page_size value. Must be an integer."}
            return

        if page < 1 or page_size < 1:
            frappe.response["http_status_code"] = 400
            frappe.response["message"] = {"error": "Page and page_size must be greater than zero."}
            return

        filters = {}
        filters["is_active"] = True
        if company:
            filters["parent_company"] = company
        if country:
            filters["country"] = country

        offset = (page - 1) * page_size

        or_filters = []
        if search:
            search_term = f"%{search}%"
            or_filters.append(["vendor_name", "like", search_term])
            or_filters.append(["parent_company", "like", search_term])
            or_filters.append(["city", "like", search_term])
            or_filters.append(["state", "like", search_term])
            or_filters.append(["email_id", "like", search_term])
        
        vendor_list = frappe.get_all(
            "Vendor",
            filters=filters,
            or_filters=or_filters, 
            fields=[
                "name", "vendor_name", "parent_company", "city", 
                "state", "zip", "country", "phone", "email_id", "contact"
            ],
            limit_start=offset,
            limit_page_length=page_size,
            order_by='creation desc'
        )

        response_data = []
        for vendor in vendor_list:
            try:
                vendor_country_name = {}
                if vendor.country:
                    country_code = vendor.country.lower()
                    country_doc = frappe.db.get_value("Country", vendor.country, ["name", "country_name", "code"], as_dict=True)
                    if country_doc:
                        vendor_country_name = country_doc


                vendor_data = {
                    "id": vendor.name,
                    "vendor_name": vendor.vendor_name,
                    "parent_company": vendor.parent_company,
                    "city": vendor.city,
                    "state": vendor.state,
                    "zip": vendor.zip,
                    "country": vendor_country_name,
                    "phone": vendor.phone,
                    "email": vendor.email_id,
                    "contact": vendor.contact
                }
                response_data.append(vendor_data)

            except Exception as vendor_error:
                frappe.log_error(f"Error processing vendor {vendor.name}: {str(vendor_error)}", "Vendor Processing Error")

        total_count = len(frappe.get_all(
                            "Vendor",
                            filters=filters,
                            or_filters=or_filters,
                            fields=["name"]
                        ))
        total_pages = (total_count + page_size - 1) // page_size

        frappe.response["message"] = {
            "total_count": total_count,
            "page": page,
            "page_size": page_size,
            "total_pages": total_pages,
            "vendors": response_data
        }

    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Get All Vendors API Error")
        frappe.response["http_status_code"] = 500
        frappe.response["message"] = {"error": str(e)}



@frappe.whitelist()
# @role_required(["Admin"])
def create_vendor():
    try:
        if frappe.request.data:
            data = json.loads(frappe.request.data)
        else:
            frappe.response['http_status_code'] = 400
            frappe.response["message"] = {"error": "Invalid JSON payload."}
            return 

        first_name = data.get("first_name")
        last_name = data.get("last_name")
        parent_company = data.get("parent_company")
        city = data.get("city")
        state = data.get("state")
        zip_code = data.get("zip")
        country = data.get("country")
        phone = data.get("phone")
        email_id = data.get("email_id")
        contact = data.get("contact")
        vendor_address = data.get("vendor_address")

        # Check if vendor already exists
        if frappe.db.exists("Vendor", {"vendor_name": first_name + " " + last_name}):
            frappe.response["http_status_code"] = 400
            frappe.response["message"] = {"error": "Vendor with this name already exists."}
            return

        # Validate customer_country (code)
        country_name = ""
        if country:
            country_doc = frappe.db.get_value("Country", {"code": country.lower()})
            if not country_doc:
                return {
                    "status_code": 404,
                    "message": f"Invalid country code: '{country.lower()}'."
                }
            country_name = country_doc 


        vendor_name = first_name + " " + last_name

        if vendor_name == " ":
            return {"status_code": 404, "message": "First name and last name is required"}

        vendor_code = generate_vendor_code(vendor_name) # To create 
            

        # create vendor doc
        vendor = frappe.get_doc({
            "doctype": "Vendor",
            "first_name": first_name,
            "last_name": last_name,
            "vendor_name": first_name + " " + last_name,
            "parent_company": parent_company,
            "phone": phone,
            "vendor_code": vendor_code,
            "city": city,
            "state": state,
            "zip": zip_code,
            "country": country_name,
            "email_id": email_id,
            "contact": contact,
            "vendor_address" : vendor_address
        })
        vendor.insert(ignore_permissions=True)

        frappe.response["message"] = {
            "success": True,
            "vendor_id": vendor.name,
            "vendor_name": vendor.vendor_name,
            "parent_company": vendor.parent_company,
            "vendor_code": vendor_code,
            "city": vendor.city,
            "state": vendor.state,
            "zip": vendor.zip,
            "country": vendor.country,
            "phone": vendor.phone,
            "email_id": vendor.email_id,
            "contact": contact, 
            "vendor_address": vendor_address
        }

    except json.JSONDecodeError:
        frappe.response["http_status_code"] = 400
        frappe.response["message"] = {"error": "Invalid JSON format."}

    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Create Vendor API Error")
        frappe.response["http_status_code"] = 500
        frappe.response["message"] = {"error": str(e)}



# soft delete --> vendor
@frappe.whitelist()
def delete_vendor(vendor_id):
    try:
        vendor = frappe.get_doc("Vendor", vendor_id)
        vendor.is_active = False
        vendor.save(ignore_permissions=True)

        return {
            "status": 200,
            "message": "Vendor deleted successfully",
            "vendor_id": vendor_id,
            "vendor_name": vendor.vendor_name
        }

    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Delete Vendor API Error")
        return {
            "status": 500,
            "message": {"error": str(e)}
        }




@frappe.whitelist()
@role_required(["Admin"])
def get_vendor_details(vendor_id):
    try:
        if not vendor_id:
            return {'status': 400, 'message': 'Vendor id is required.'}
        
        try:
            vendor_doc = frappe.get_doc("Vendor", vendor_id)
        except Exception as e:
            return {'status': 404, 'message': 'Vendor not found'}

        if vendor_doc.is_active == False:
            return {'status': 404, 'message': 'Vendor not found'}

        vendor_country_name = {}

        if vendor_doc.country:
            country_code = vendor_doc.country.lower()
            country_doc = frappe.db.get_value("Country", vendor_doc.country, ["name", "country_name", "code"], as_dict=True)
            if country_doc:
                vendor_country_name = country_doc
        
        # Collect all passkeys from child table
        passkeys = [row.passkey for row in vendor_doc.get("pass_key")]

        vendor_data = {
            "id": vendor_doc.name,
            "first_name": vendor_doc.first_name,
            "last_name": vendor_doc.last_name,
            "vendor_name": vendor_doc.vendor_name,
            "vendor_code": vendor_doc.vendor_code,
            "parent_company": vendor_doc.parent_company,
            "phone": vendor_doc.phone,
            "city": vendor_doc.city,
            "state": vendor_doc.state,
            "zip": vendor_doc.zip,
            "country": vendor_country_name,
            "email_id": vendor_doc.email_id,
            "contact": vendor_doc.contact,
            "vendor_address" : vendor_doc.vendor_address,
            "passkeys": passkeys 
        }

        return {'status': 200, 'message': 'Vendor details fetched successfully', 'data': vendor_data}
    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Get Vendor Details API Error")
        return {'status': 500, "message": f'Internal Server Error: {str(e)}'}



@frappe.whitelist()
@role_required(["Admin"])
def update_vendor(vendor_id):
    """
    API to update Vendor document.
    Expects JSON body as form data with keys matching Vendor fields.
    """

    try:
        if not vendor_id:
            return {'status': 400, 'message': 'Vendor ID is required.'}

        try:
            data = frappe.local.form_dict
            updated_data = json.loads(data.get("data", "{}"))
        except Exception:
            return {'status': 400, 'message': 'Invalid or missing JSON payload in `data` field.'}

        if not updated_data:
            return {'status': 400, 'message': 'No data provided for update.'}

        try:
            vendor_doc = frappe.get_doc("Vendor", vendor_id)
        except Exception:
            return {'status': 404, 'message': 'Vendor not found.'}

        # country code is valid
        if "country" in updated_data:
            country_code = updated_data["country"]

            country_doc = frappe.db.get_value("Country", {"code": country_code.lower()}, ["name"])
            if not country_doc:
                return {
                    "status_code": 404,
                    "message": f"Invalid country code: '{country_code.lower()}'."
                }
            vendor_doc.country = country_doc 

        # Allowed fields to update
        allowed_fields = [
            "first_name", "last_name", "vendor_name", "parent_company", "phone",
            "city", "state", "zip", "email_id", "contact", "vendor_address"
        ]

        for field, value in updated_data.items():
            if field in allowed_fields:
                vendor_doc.set(field, value)

        # Automatically set vendor_name from first and last name
        first = updated_data.get("first_name", vendor_doc.first_name or "")
        last = updated_data.get("last_name", vendor_doc.last_name or "")
        vendor_doc.vendor_name = f"{first} {last}".strip()

        vendor_doc.save(ignore_permissions=True)
        frappe.db.commit()

        return {'status': 200, 'message': 'Vendor updated successfully.'}

    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Update Vendor Details API Error")
        return {'status': 500, 'message': f'Internal Server Error: {str(e)}'}



@frappe.whitelist()
@role_required(["Admin"])
def get_parent_companies():
    try:
        parent_companies = frappe.get_all(
            "Vendor",
            filters={"is_active": True},
            fields=["parent_company"]
        )
        
        company_list = [d["parent_company"] for d in parent_companies if d["parent_company"]]

        return {"data" :set(company_list)}

    except Exception as e:
        frappe.log_error(f"Error in get_parent_companies: {str(e)}")
        frappe.throw("Something went wrong while fetching companies.")




# Driver Code --> In Child table  ( Need to update with admin previlage- not added now )
@frappe.whitelist()
@role_required(["Vendor", "Admin"])
def add_passkey(vendor_id = None):
    """
    Add a Passkey entry to a Vendor's child table
    after regenerating a unique key.
    """
    vendor_user = frappe.session.user
    vendor_doc = frappe.get_doc("Vendor", {"email_id": vendor_user})

    if not vendor_doc:
        return {"status_code": 400, "message": "Vendor not found!"}
    
    vendor_code = vendor_doc.vendor_code if vendor_doc.vendor_code else ""

    # Generate a new unique key
    new_key = generate_unique_passkey(vendor_code)

    # Append child row in Passkey table
    vendor_doc.append("pass_key", {
        "passkey": new_key
    })

    # Save changes
    vendor_doc.save(ignore_permissions=True)
    frappe.db.commit()

    return {
        "status_code": 200,
        "message": f"Passkey added to Vendor {vendor_doc.name}",
        "vendor_id": vendor_doc.name,
        "passkey": new_key
    }



# Remove driver pass key.  ( Need to update with admin previlage- not added now )
@frappe.whitelist()
@role_required(["Vendor", "Admin"])
def remove_passkey(passkey, vendor_id = None):
    """
    Remove a specific passkey from the Vendor's child table.
    """
    vendor_user = frappe.session.user
    vendor_doc = frappe.get_doc("Vendor", {"email_id": vendor_user})

    if not vendor_doc:
        return {"status_code": 400, "message": "Vendor not found!"}

    # Find child row with the given passkey
    row_to_remove = None
    for row in vendor_doc.pass_key:
        if row.passkey == passkey:
            row_to_remove = row
            break

    if not row_to_remove:
        return {"status_code": 404, "message": "Passkey not found!"}

    # Remove the row
    vendor_doc.remove(row_to_remove)

    # Save changes
    vendor_doc.save(ignore_permissions=True)
    frappe.db.commit()

    return {
        "status_code": 200,
        "message": f"Passkey {passkey} removed from Vendor {vendor_doc.name}",
        "vendor_id": vendor_doc.name
    }




@frappe.whitelist()
@role_required(["Vendor", "Admin"])    #  ( Need to update with admin previlage- not added now )
def regenerate_all_passkeys(vendor_id = None):
    """
    Regenerate all passkeys for the logged-in Vendor.
    Updates each row in the child table with a new unique key.
    """
    vendor_user = frappe.session.user
    vendor_doc = frappe.get_doc("Vendor", {"email_id": vendor_user})

    if not vendor_doc:
        return {"status_code": 400, "message": "Vendor not found!"}

    vendor_code = vendor_doc.vendor_code if vendor_doc.vendor_code else ""

    updated_keys = []

    for row in vendor_doc.pass_key:
        # Generate a new unique passkey for each row
        new_key = generate_unique_passkey(vendor_code)
        row.passkey = new_key
        updated_keys.append(new_key)

    # Save all updates
    vendor_doc.save(ignore_permissions=True)
    frappe.db.commit()

    return {
        "status_code": 200,
        "message": f"Regenerated {len(updated_keys)} passkeys for Vendor {vendor_doc.name}",
        "vendor_id": vendor_doc.name,
        "passkeys": updated_keys
    }
