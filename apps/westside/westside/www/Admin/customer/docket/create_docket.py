import frappe       
from frappe import _      
import json
from westside.www.Authentication.auth_decorators import role_required



@frappe.whitelist()
@role_required(["Admin"])
def prepare_docket_creation_data(carrier_booking_number):
    try:
        if not carrier_booking_number:
            return {"status": 400, "message": "Carrier Booking Number is required"}
        
        booking = None
        if frappe.db.exists("Booking Request", {"carrier_booking_number": carrier_booking_number}):
            booking = frappe.get_doc("Booking Request", {"carrier_booking_number": carrier_booking_number})

        # Bill of lading
        try:
            bill_of_lading_doc = frappe.get_doc("Bill of Lading", {"carrier_booking_number": carrier_booking_number})
        except:
            return {"status": 400, "message": "Bill of ladding not found for this booking."}

        if not bill_of_lading_doc:
            return {"status": 400, "message": "Bill of ladding not found."}

        
        # Shipping Instruction
        si_doc = None
        customer_doc = None
        shipping_instructions = frappe.get_list(
                "Shipping Instructions",
                filters={"carrier_booking_number": carrier_booking_number},
                fields=["name", "consignee"],
                limit=1
            )

        if shipping_instructions:
            si_doc = frappe.get_doc("Shipping Instructions", shipping_instructions[0]['name'])
            customer_doc = frappe.get_doc("Customer DB", si_doc.consignee)
        

        # Equipment --> to get package type and package count from cargo
        equipments = frappe.get_all('Equipments', 
                filters={'carrier_booking_number': carrier_booking_number, "is_active": True},                     
                fields=[
                    'name', 'equipment_name', 'container_type_id', 'description'
                ])
        
        package_type = None
        package_count = None
        if equipments is not []:
            eq = equipments[0]

            cargo = frappe.get_value(
                'Cargo',
                filters={'parent': eq.name, 'parenttype': 'Equipments'},
                fieldname=['container_number', 'package_count', 'package_counttype_outermost'],
                as_dict=True 
            ) 
            package_type = eq.description
          
            if cargo and cargo.package_count is not None:
                package_count = int(cargo.package_count)
        
        data = {
            "name": bill_of_lading_doc.name,
            "carrier_booking_number": bill_of_lading_doc.carrier_booking_number,
            "bol_number": bill_of_lading_doc.bol_number,
            "shipment_id": bill_of_lading_doc.shipment_id,
            "contract_number": bill_of_lading_doc.contract_number,
            "shipped_on_board_date": bill_of_lading_doc.shipped_on_board_date,
            "port_of_load": bill_of_lading_doc.port_of_load_location,
            "port_of_discharge": bill_of_lading_doc.port_of_discharge_location,
            "carrier": bill_of_lading_doc.carrier,
            "weight": bill_of_lading_doc.total_gross_weight,
            "hs_code": bill_of_lading_doc.hs_code,
            "material": bill_of_lading_doc.hs_code_description,
            "package_type" : package_type,
            "package_count" : package_count
        }

        # Specific fields from child tables
        data["references"] = [
            {
                "reference_type": ref.reference_type,
                "text": ref.text
            } for ref in bill_of_lading_doc.references
        ]

        data["parties"] = [
            {
                "partner_role": party.partner_role,
                "partner_name": party.partner_name,
                "partner_identifier": party.partner_identifier,
                "contact_name": party.contact_name,
                "telephone": party.telephone,
                "email": party.email,
                "address_line": party.address_line,
                "partner_table_name": party.partner_table_name

            } for party in bill_of_lading_doc.parties
        ]

        shipper = None
        shipper_telephone = None
        shipper_contact = None
        shipper_address = None
        shipper_name = None
        shipper_id = None

        consignee_address = None
        consignee_name = None
        consignee_contact = None
        consignee_telephone = None
        consignee_email = None
        consignee_id = None
        
        for party in data.get("parties", []):
            if party.get("partner_role") == "Shipper":
                shipper_telephone = party.get("telephone")
                shipper_name = party.get("partner_name")
                shipper_contact = party.get("contact_name")
                shipper_address = party.get("address_line")
                shipper_id = party.get("partner_table_name")
            
            if party.get("partner_role") == "Consignee":
                consignee_telephone = party.get("telephone")
                consignee_name = party.get("partner_name")
                consignee_contact = party.get("contact_name")
                consignee_address = party.get("address_line")
                consignee_email = party.get("email")
                consignee_id = party.get("partner_table_name")


        # Origin : bill_of_lading_doc.port_of_load
        origin_of_goods = None
        if bill_of_lading_doc.port_of_load:
            origin = frappe.get_value(
                "UNLOCODE Locations", 
                {"locode": bill_of_lading_doc.port_of_load},
                ["name", "locode", "country_code", "country", "location_name"],
                as_dict=True
            )
            origin_of_goods = origin["country"] if origin and origin.get("country") else None
        
        
        containers_result = None
        if equipments:
            total_equipment = bill_of_lading_doc.total_equipment if bill_of_lading_doc.total_equipment else None
            
            if equipments[0].container_type_id:
                container_type_doc = frappe.get_doc("Container Type", equipments[0].container_type_id)
                containers_result = f'{total_equipment}x{container_type_doc.typecode}'
        
        return {
            "status": "success",
            "data": {
                "booking_id": booking.name if booking and booking.name else None,
                "carrier_booking_number": bill_of_lading_doc.carrier_booking_number,
                "customer": consignee_name,
                "consignee_address": consignee_address,
                "consignee_id": consignee_id,
                "shipper": shipper_address,
                "shipper_name": shipper_name,
                "shipper_id": shipper_id,
                "hs_code" :  data["hs_code"],
                "material": data["material"],
                "shipping_date": data["shipped_on_board_date"],
                "origin": data["port_of_load"],
                "destination": data["port_of_discharge"],
                "bill_no" :  data['bol_number'],
                "destination_contact" : consignee_contact,
                "telephone" : consignee_telephone,
                "origin_of_goods" : origin_of_goods,
                "weight": data["weight"],
                "contact": shipper_contact,
                "containers":containers_result if containers_result else None,
                "shipline": data["carrier"],
                "package_type" : data['package_type'],
                "package_count" : data['package_count']
            }
        }
    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "get_booking_request_error")
        frappe.throw(_("An unexpected error occurred: ") + str(e))


 


@frappe.whitelist()
@role_required(["Admin"])
def create_docket():
    try:  
        carrier_booking_number = frappe.request.args.get("carrier_booking_number")

        if not carrier_booking_number:
            return {'status': 400, 'message': 'Missing Carrier booking number.'}
        
        booking = None
        if frappe.db.exists("Booking Request", {"carrier_booking_number": carrier_booking_number}):
            booking = frappe.get_doc("Booking Request", {"carrier_booking_number": carrier_booking_number})
    
        si_doc = None
        shipping_instructions = frappe.get_list(
                "Shipping Instructions",
                filters={"carrier_booking_number": carrier_booking_number},
                fields=["name", "consignee"],
                limit=1
            )
        if shipping_instructions:
            si_doc = frappe.get_doc("Shipping Instructions", shipping_instructions[0]['name', 'consignee'])       

        if frappe.request.data:
            data = json.loads(frappe.request.data)
        else:
            frappe.response['http_status_code'] = 400
            frappe.response["message"] = {"error": "Invalid JSON payload."}
            return 
        
        # Check for existing Docket with same booking and customer
        existing_docket = frappe.db.exists(
            "Docket DB",
            {"carrier_booking_number": carrier_booking_number})

        if existing_docket:
            return {
                "status": 500,
                "message": f"Docket already exists with Carrier booking number '{carrier_booking_number}' and Customer '{data.get("customer")}'.",
                "existing_docket_id": existing_docket
            }
                   
        booking = booking.name if booking and booking.name else None
        carrier_booking_number = carrier_booking_number
        customer = data.get("customer")
        customer_id = data.get("consignee_id") if data.get("consignee_id") else (si_doc.consignee if si_doc and si_doc.consignee else None) # Customer id is taking from Shipping Instruction.
        consignee = data.get("consignee")
        shipper = data.get("shipper")
        shipper_name = data.get("shipper_name")
        shipper_id = data.get("shipper_id")
        hs_code = data.get("hs_code")
        material = data.get("material")
        shipping_date = data.get("shipping_date")
        invoice = data.get("invoice")
        origin = data.get("origin")
        destination = data.get("destination")
        blno = data.get("blno")
        destination_contact = data.get("destination_contact")
        telephone = data.get("telephone")
        terms = data.get("terms")
        origin_of_goods = data.get("origin_of_goods")
        weight = data.get("weight")
        contact = data.get("contact")
        containers = data.get("containers")
        shipline = data.get("shipline")
        country_of_import_export = data.get("country_of_import_export")
        package_type = data.get("package_type")
        package_count = data.get("package_count")

        if not customer:
            frappe.response["http_status_code"] = 400
            frappe.response["message"] = {"error": "customer name is required."}
            return

        # create customer doc
        docket = frappe.get_doc({
            "doctype": "Docket DB",
            "booking": booking,
            "carrier_booking_number": carrier_booking_number,
            "customer": customer,
            "customer_id": customer_id,
            "shipper_name": shipper_name,
            "shipper": shipper,
            "shipper_id": shipper_id,
            "consignee": consignee,
            "material": material,
            "shipping_date": shipping_date,
            "invoice": invoice,
            "hs_code": hs_code,
            "origin": origin,
            "destination": destination,
            "blno": blno,
            "destination_contact": destination_contact,
            "telephone": telephone,
            "terms": terms,
            "origin_of_goods": origin_of_goods,
            "weight": weight,
            "contact": contact,
            "containers": containers,
            "shipline": shipline,
            "status": "New",
            "country_of_import_export": country_of_import_export,
            "package_type" : package_type,
            "package_count" : package_count
        })

        docket.insert(ignore_permissions=True)
        docket.save(ignore_permissions=True)
        frappe.db.commit()
        
        return {
            "status": 200,
            "message": "Docket created successfully",
            "docket_id": docket.name
        }
        
    except Exception as e:
        frappe.db.rollback()
        frappe.log_error(frappe.get_traceback(), "Create Docket API Error")
        return {
            "status": 500,
            "message": {"error": str(e)}
        }



@frappe.whitelist()
@role_required(["Admin"])
def get_containers(carrier_booking_number=None):
    
    if not carrier_booking_number:
        return {"status_code": 422, "message": "Carrier Booking Number is required."}

    try:
        result = {}
        equipments = frappe.get_all('Equipments', 
                filters={'carrier_booking_number': carrier_booking_number, "is_active": True}, 
                fields=[
                    'name', 'equipment_name', 'code_value', 'shipper_seal_number',
                    'supplier_type', 'service_type'
                ], 
                order_by='creation desc'
            )

        equipments_data = []
        total_net_weight = 0  

        for eq in equipments:
            # Get the Cargo for this equipment
            cargo = frappe.get_value(
                'Cargo',
                filters={'parent': eq.name, 'parenttype': 'Equipments'},
                fieldname=[
                    'container_number', 'package_count', 'package_counttype_outermost', 'hs_code',
                    'cargo_description', 'cargo_gross_weight', 'net_weight', 'net_weight_unit'
                ],
                as_dict=True 
            )
            
            # Add cargo info to equipment
            net_weight = cargo.net_weight if cargo and cargo.net_weight else None
            if net_weight:
                total_net_weight += net_weight

            eq["cargo"] = cargo or {}
            eq["net_weight"] = net_weight
            eq["cargo_description"] = cargo.cargo_description if cargo and cargo.cargo_description else ""
            eq["package_count"] = cargo.package_count if cargo and cargo.package_count else ""
            eq["package_type"] = cargo.package_counttype_outermost if cargo and cargo.package_counttype_outermost else ""

            equipments_data.append(eq)

        # Final result
        result['number_of_assigned_containers'] = len(equipments_data)
        result['equipments'] = equipments_data
        result['net_weight'] = total_net_weight

        site_url = frappe.utils.get_url()

        return {
            "status_code": 200,
            "message": "Equipments fetched successfully",
            "data": result
        }

    except frappe.DoesNotExistError:
        return {
            "status_code": 404,
            "message": "Equipments not found"
        }
    except Exception as e:
        frappe.log_error(f"Error in get_assigned_containers: {str(e)}")
        return {
            "status_code": 500,
            "message": f"Internal Server Error: {str(e)}"
        }




@frappe.whitelist()
@role_required(["Admin"])
def edit_container_details(equipment_id, container_number=None, seal_number=None, net_weight=None):
    try:
        if not equipment_id:
            return {"status_code": 422, "message": "Equipment ID is required."}

        equipment = frappe.get_doc("Equipments", equipment_id)

        if seal_number is not None:
            equipment.shipper_seal_number = seal_number

        if container_number is not None:
            equipment.equipment_name = container_number


        # Get cargo row ( assuming one child row per equipment )
        cargo_row = None
        for row in equipment.cargo:
            cargo_row = row
            break

        if cargo_row:
            if net_weight is not None:
                cargo_row.net_weight = net_weight

            equipment.save(ignore_permissions=True)
            frappe.db.commit()

            return {
                "status_code": 200,
                "message": "Container details updated successfully."
            }
        else:
            return {
                "status_code": 404,
                "message": "No cargo entry found for this equipment."
            }
            
    except frappe.DoesNotExistError:
        return {
            "status_code": 404,
            "message": "Equipment not found"
        }
        
    except Exception as e:
        frappe.db.rollback()
        frappe.log_error(frappe.get_traceback(), "Edit Container Details Error")
        return {
            "status_code": 500,
            "message": f"Internal Server Error: {str(e)}"
        }
