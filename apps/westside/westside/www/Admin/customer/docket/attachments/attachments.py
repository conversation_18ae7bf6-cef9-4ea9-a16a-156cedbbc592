import os
import json
import frappe
from frappe import _
from frappe.utils.pdf import get_pdf
from datetime import datetime
from frappe.utils import nowdate, formatdate, now_datetime
from jinja2 import Environment, FileSystemLoader, StrictUndefined
from westside.www.Authentication.auth_decorators import role_required



@frappe.whitelist()
@role_required(["Admin"])
def create_docket_revision(booking_id, docket_id):
    try:
        docket = frappe.get_doc("Docket DB", docket_id)
        carrier_booking_number = docket.carrier_booking_number if docket.carrier_booking_number else None
        
        if not carrier_booking_number: 
            return {"status": 400, "message": "Carrier booking number not found."} 
        
        booking = None
        if frappe.db.exists("Booking Request", {"carrier_booking_number": carrier_booking_number}):
            booking = frappe.get_doc("Booking Request", {"carrier_booking_number": carrier_booking_number})
        
        existing_revisions = docket.docket_revisions or []
        if existing_revisions:
            last_revision = max(int(d.revision_number) for d in existing_revisions if d.revision_number)
            next_revision = last_revision + 1
        else:
            next_revision = 1

        # Add new Docket Revision as child table row
        new_revision = docket.append("docket_revisions", {})
        new_revision.revision_number = next_revision
        new_revision.status = "Open"
 
        # Setup Jinja2
        template_path = frappe.get_app_path('westside', 'templates')
        env = Environment(
            loader=FileSystemLoader(template_path),
            undefined=StrictUndefined,
            trim_blocks=True,
            lstrip_blocks=True
        )
         
        # Data collection fot PDF generation.
        docket_data = frappe.db.get_value(
            "Docket DB",
            docket_id,
            ["name", "status", "customer", "customer_name", "shipper",  "shipper_name", 
            "material", "consignee", "shipping_date", "invoice", "hs_code", "origin", "blno",
            "telephone", "origin_of_goods", "contact", "destination", "destination_contact", "terms",
            "weight", "containers", "shipline", "packing_list", "country_of_import_export", "package_count", "package_type", "category"],
            as_dict=True
        )

        bill_of_lading_doc = frappe.get_doc("Bill of Lading", {"carrier_booking_number": carrier_booking_number})
        
        if not bill_of_lading_doc:
            return {"status": 400, "message": "Bill of ladding not found."}
            
        bill_of_lading_data = {
            "name": bill_of_lading_doc.name,
            "carrier_booking_number": bill_of_lading_doc.carrier_booking_number
        }
        
        bill_of_lading_data["parties"] = [
            {
                "partner_role": party.partner_role,
                "partner_name": party.partner_name,
                "partner_identifier": party.partner_identifier,
                "contact_name": party.contact_name,
                "telephone": party.telephone,
                "email": party.email,
                "address_line": party.address_line,

            } for party in bill_of_lading_doc.parties
        ]
        
        # Shipper
        shipper_doc = {
            "name": None,
            "country": None,
            "postal_code": None,
            "phone": None,
            "street_address": None,
            "location_name": None
        }

        if docket.shipper_id:
            shipper_doc = frappe.db.get_value(
                "Shipper",
                docket.shipper_id,
                ["name", "country", "postal_code", "phone", "street_address", "location_name"],
                as_dict=True
            )


        carrier_name = None
        for party in bill_of_lading_data.get("parties", []):
            # shipper 
            if party.get("partner_role") == "Shipper":
                docket_data['shipper_detail'] = {
                        "shipper_name": party.get("partner_name"),
                        "phone": party.get("telephone"),
                        "contact_name": party.get("contact_name"),
                        "address": party.get("address_line")
                    }
                
                if not docket.shipper_id:
                    shipper_doc = {
                        "name": party.get("partner_name"),
                        "country": party.get("country"),
                        "postal_code": party.get("postal_code"),
                        "phone": party.get("telephone"),
                        "street_address": party.get("address_line").split("\n")[0] if party.get("address_line") and party.get("address_line").split("\n")[0] else None,
                        "location_name": party.get("address_line").split("\n")[1] if party.get("address_line") and party.get("address_line").split("\n")[1] else None,
                    }
                    
            # customer 
            if party.get("partner_role") == "Consignee":
                consignee_name = party.get("partner_name")
                consignee_address = party.get("address_line")
                consignee_contact = party.get("contact_name")
                consignee_telephone = party.get("telephone")

            # carrier
            if party.get("partner_role") == "Carrier":
                carrier_name = party.get("partner_name")
               

        docket_data['carrier'] = carrier_name

        docket_data['shipper_doc'] = shipper_doc

        equipments_data = []
                            
        equipments = frappe.get_all('Equipments', 
                filters={'carrier_booking_number': carrier_booking_number, "is_active": True},                     
                fields=[
                    'name', 'equipment_name', 'code_value', 'shipper_seal_number', 'carrier_seal_number',
                    'description', 'supplier_type', 'service_type','weight_value', 'weight_type', 'job',
                    'tare_weight', 'cargo_weight', 'gross_weight'
                ])

        total_quantity = 0
        for eq in equipments:
            cargo = frappe.get_value(
                'Cargo',
                filters={'parent': eq.name, 'parenttype': 'Equipments'},
                fieldname=['container_number', 'package_count', 'package_counttype_outermost', 'hs_code', 'cargo_description', 'cargo_gross_weight', 'net_weight', 
                'net_weight_unit'],
                as_dict=True 
            ) 
            if cargo and cargo.net_weight:
                total_quantity += int(cargo.net_weight)

            eq["cargo"] = cargo
            equipments_data.append(eq)
            docket_data['cargo_description'] = cargo.cargo_description if cargo and cargo.cargo_description else ''
            
        docket_data['total_qty'] = total_quantity
        docket_data['packing_type'] = docket_data.package_type
        docket_data['packing_count'] = docket_data.package_count
        docket_data['category'] = docket_data.category  # invoice category

        # Split into paginated data: first 10, then 25, then 25...
        page_limits = [22, 40, 40, 40]
        equipments_pages = []

        start = 0
        for limit in page_limits:
            chunk = equipments_data[start:start+limit]
            if chunk:
                equipments_pages.append(chunk)
            start += limit

        # In case more items are left beyond the third page
        while start < len(equipments_data):
            chunk = equipments_data[start:start+25]  # You can use 25 for all further pages
            equipments_pages.append(chunk)
            start += 25
        
        current_date = datetime.strptime(nowdate(), "%Y-%m-%d").strftime("%m/%d/%Y")

        pdf_data = {
            "docket": docket_data,
            "equipments": equipments,
            "equipments_pages": equipments_pages,
            "current_date": current_date
            }
        
        customer_doc = None
        if docket.customer_id:
            customer_doc = frappe.get_doc("Customer DB", docket.customer_id)
        
        if customer_doc is None:
            return {"status": 400, "message": "Customer not found."} 


        if customer_doc.flag_for_custom_docs == True:
            templates = [
                ("packing_list.html", "packing_list"),
                ("certificate_of_origin.html", "certificate_of_origin"),
                ("form9.html", "form9"),
                ("form6.html", "form6"),
                ("ISCC_Self-declaration.html", "iscc_self_declaration"),
                ("ISCCPLUS_Self-declaration.html", "isccplus_self_declaration"),
                ("bill_of_ladding.html", "bill_of_ladding"),
                ("invoice.html", "invoice"),
            ]
        else:
            templates = [
                ("packing_list.html", "packing_list"),
                ("certificate_of_origin.html", "certificate_of_origin"),
                ("form9.html", "form9"),
                ("form6.html", "form6"),
                ("bill_of_ladding.html", "bill_of_ladding"),
                ("invoice.html", "invoice"),
            ]

        attachments = []  # for send email to customer.
        for template_name, fieldname in templates:
            
            # Special handling for 'bill_of_ladding'
            if fieldname == "bill_of_ladding":
                # Get the Bill of Lading document
                if bill_of_lading_doc and hasattr(bill_of_lading_doc, "bol_attachments"):

                    attachments = bill_of_lading_doc.get("bol_attachments")  
                    
                    if attachments:
                        # Sort by creation date or modified timestamp to get the latest one
                        latest_attachment = sorted(attachments, key=lambda x: x.creation, reverse=True)[0]
                    
                        site_url = frappe.utils.get_url()
                        file_url = latest_attachment.file_url
                        file_name = latest_attachment.file_name

                        if hasattr(new_revision, fieldname):
                            setattr(new_revision, fieldname, file_url)
                continue 
            
            # Special handling for 'Invoice'
            if fieldname == "invoice":
                try:
                    invoice_doc = frappe.get_doc(
                        "Invoice DB",
                        {
                            "carrier_booking_number": carrier_booking_number,
                            "docket_id": docket.name
                        }
                    )
                except frappe.DoesNotExistError:
                    # No invoice for this (booking, docket) pair → skip
                    continue
                    
                if invoice_doc and invoice_doc.invoice_pdf_link:

                    if hasattr(new_revision, fieldname):
                        setattr(new_revision, fieldname, invoice_doc.invoice_pdf_link)
                        
                continue 

            # Normal flow for other templates
            template = env.get_template(f"docket_attachments/{template_name}")
            html = template.render({"doc": pdf_data})
            pdf_content = get_pdf(html)

            file_name = f"{carrier_booking_number}_{fieldname}_rev{next_revision}.pdf"
            file_path = os.path.join(frappe.get_site_path("public", "files"), file_name)

            # Write file
            with open(file_path, "wb") as f:
                f.write(pdf_content)

            file_url = f"/files/{file_name}"    

            # Attach the PDF to Docket DB
            file_doc = frappe.get_doc({
                "doctype": "File",
                "file_name": file_name,
                "file_url": file_url,
                "is_private": 0,
                "attached_to_doctype": "Docket DB",
                "attached_to_name": docket.name,
            })
            file_doc.insert(ignore_permissions=True)
            
            attachments.append({
                "fname": file_doc.file_name,
                "fcontent": file_doc.get_content()
            })

            # Store file URL in appropriate field if exists
            if hasattr(new_revision, fieldname):
                setattr(new_revision, fieldname, file_url)
        
        if new_revision:
            new_revision.show_to_customer = False
            new_revision.created_on = now_datetime()


        docket.status = "Open"
        # Update revision number in main Docket   
        docket.revision_number = next_revision
        docket.save(ignore_permissions=True)
        frappe.db.commit()

        return {
            "status": 200,
            "message": f"Docket Revision rev{next_revision} created successfully.",
            "revision_number": next_revision
        }

    except Exception as e:
        frappe.db.rollback()
        frappe.log_error(frappe.get_traceback(), "Create Docket Revision Error")
        return {
            "status": 500,
            "message": f"Error creating Docket Revision: {str(e)}"
        }  



 
@frappe.whitelist()
@role_required(["Admin"])
def regenerate_docket_revision(booking_id, docket_id):
    try:
        docket = frappe.get_doc("Docket DB", docket_id)

        carrier_booking_number = docket.carrier_booking_number if docket.carrier_booking_number else None
        
        if not carrier_booking_number:
            return {"status": 400, "message": "Carrier booking number not found."} 
        
        booking = None
        if frappe.db.exists("Booking Request", {"carrier_booking_number": carrier_booking_number}):
            booking = frappe.get_doc("Booking Request", {"carrier_booking_number": carrier_booking_number})


        existing_revisions = docket.docket_revisions or []

        if not existing_revisions:
            return {"status": 404, "message": "No existing docket revision found to regenerate."}
 
       # Get latest revision number
        latest_revision_number = max(
            int(d.revision_number) for d in docket.docket_revisions if d.revision_number
        )
     
        # Find the child row object with the latest revision number
        latest_revision = next(
            (d for d in docket.docket_revisions if int(d.revision_number) == latest_revision_number),
            None
        )
    
        # Remove the child row
        if latest_revision:
            docket.set("docket_revisions", [
                d for d in docket.docket_revisions if int(d.revision_number) != latest_revision_number
            ])

            docket.revision_number = int(docket.revision_number) - 1
            docket.save(ignore_permissions=True)
            frappe.db.commit()
        else:
            return {"status": 404, "message": "Could not locate latest revision to remove."}

        try: 
            # Now create new revision with same number
            result = create_docket_revision(booking_id, docket_id)
        except:
            return {"status": 404, "message": "Error creating docket revision."}

        return {
            "status": result.get("status"),
            "message": f"Revision regenerated successfully as rev{result.get('revision_number')}.",
            "revision_number": result.get("revision_number")
        }
    except Exception as e:
        frappe.db.rollback()
        frappe.log_error(frappe.get_traceback(), "Regenerate Docket Revision Error")
        return {
            "status": 500,
            "message": f"Error creating Docket Revision: {str(e)}"
        }  


@frappe.whitelist()
@role_required(["Admin"])
def send_docket(docket_id, subject=None, message=None, cc=None, attachments=None):
    try:
        if not docket_id:
            return {'status': 400, 'message': 'DocketId is required.'}

        docket = frappe.get_doc("Docket DB", docket_id)
        if not docket:
            return {'status': 404, 'message': 'Docket not found'}

        carrier_booking_number = docket.carrier_booking_number if docket.carrier_booking_number else None
        
        if not carrier_booking_number:
            return {"status": 400, "message": "Carrier booking number not found."} 
        
        booking = None
        if frappe.db.exists("Booking Request", {"carrier_booking_number": carrier_booking_number}):
            booking = frappe.get_doc("Booking Request", {"carrier_booking_number": carrier_booking_number})


        if docket.docket_revisions:
            # lastest revision
            latest_revision = max(docket.docket_revisions, key=lambda x: int(x.revision_number))

            customer_doc = frappe.get_doc("Customer DB", docket.customer_id)

            customer_flag_for_custom_docs = customer_doc.flag_for_custom_docs

            # Build file attachments from latest revision
            if customer_flag_for_custom_docs:
                attachment_fields = [
                    "packing_list",
                    "certificate_of_origin",
                    "form9",
                    "form6",
                    "invoice",
                    "bill_of_ladding",
                    "isccplus_self_declaration",
                    "iscc_self_declaration"
                ]
            else:
                attachment_fields = [
                    "packing_list",
                    "certificate_of_origin",
                    "form9",
                    "form6",
                    "invoice",
                    "bill_of_ladding"
                ]

            
            # Check if all required files exist in latest_revision
            missing_files = []
            for field in attachment_fields:
                if field == "bill_of_ladding":  # As of now Bill of lading pdf is not mandatory.
                    continue

                if not getattr(latest_revision, field, None):
                    missing_files.append(field)

            if missing_files:
                return {"status": 400, "message":f"The following required files are missing: {', '.join(missing_files)}"}
            else:
                
                latest_revision.show_to_customer = True
                docket.status = "Sent"
                latest_revision.status = "Sent"
                docket.save(ignore_permissions=True)
                frappe.db.commit()

                site_url = frappe.utils.get_url()

                attachments = []
                # Attach revision files
                for field in attachment_fields:
                    file_url = getattr(latest_revision, field, None)
                    if file_url:
                        filename = file_url.split("/")[-1]
                        local_path = frappe.get_site_path("public", "files", filename)
                        try:
                            with open(local_path, "rb") as f:
                                content = f.read()
                                attachments.append({
                                    "fname": filename,
                                    "fcontent": content
                                })
                        except Exception as e:
                            frappe.log_error(frappe.get_traceback(), f"File Read Error: {filename}")
                
                additional_files = frappe.get_all(
                    "Additional Revision Attachments",
                    filters={"docket_revision": latest_revision.name, "docket": docket.name},
                    fields=["attachments", "file_name"]
                )

                for add_file in additional_files:
                    if add_file.attachments:
                        filename = add_file.attachments.split("/")[-1]
                        local_path = frappe.get_site_path("public", "files", filename)
                        try:
                            with open(local_path, "rb") as f:
                                content = f.read()
                                attachments.append({
                                    "fname": filename,
                                    "fcontent": content
                                })
                        except Exception:
                            frappe.log_error(frappe.get_traceback(), f"File Read Error: {filename}")

                # 🔹 Custom display names for system attachments
                display_name_map = {
                    "bill_of_ladding": "Bill Of Lading",
                    "isccplus_self_declaration": "ISCC Plus Self Declaration",
                    "iscc_self_declaration": "ISCC Self Declaration"
                }

                # 🔹 Build attachment list HTML (system + additional)
                attachment_list_html = "<ul>"
                for field in attachment_fields:
                    display_name = display_name_map.get(
                        field, field.replace("_", " ").title()
                    )
                    attachment_list_html += f"<li>{display_name}</li>"

                for add_file in additional_files:
                    attachment_list_html += f"<li>{add_file.file_name}</li>"

                attachment_list_html += "</ul>"
                        
              
                # Send email to customer
                if customer_doc.notification_email:

                    subject = f"Shipment:{carrier_booking_number}  Bol: {docket.blno}  Docket: {docket.name}"

                    site_url = frappe.utils.get_url()

                    link = f"{site_url}/dashboard/customer/dockets-Details-View/{docket.name}"

                    message = (
                        f"Dear {customer_doc.customer_name},<br><br>"
                        f"We are pleased to inform you that a new docket revision <strong>rev{docket.revision_number}</strong> "
                        f"has been created for your booking <strong>{carrier_booking_number}</strong>.<br><br>"
                        f"Please find the relevant documents attached with this email for your review.<br><br>"
                        f"Documents attached: <br>{attachment_list_html}"
                        f"If you have any questions or require further clarification, feel free to reach out.<br><br>"
                        f"<br><br>Please review your documents, login to your customer portal account by clicking <a href='{link}' style='color: #1D4ED8;'>here</a> and approve/reject the docket. <br><br><br>"

                        # Signature block
                        f"Thanks,<br><br>"
                        f"<strong>Docs Team</strong><br>"
                        f"Westside Exports LLC<br>"
                        f"4017 Vallonia Dr<br>"
                        f"Cary, NC 27519<br>"
                        f"Email: <a href='mailto:<EMAIL>'><EMAIL></a>"
                    )

                    frappe.enqueue(
                        "westside.api.email.send_docket_email_async",
                        queue="default",
                        timeout=300,
                        email_id=str(customer_doc.notification_email),
                        cc=cc,
                        subject=subject,
                        message=message,
                        attachments=attachments
                    )

                return {"status": 200, "message": "Docket revision has sent successfully", "docket_id": docket.name}
        else:
            return {'status': 400, 'message': 'No revisions found to send.'}

    except frappe.DoesNotExistError:
        return { "status": 404, "message": "Docket not found"}

    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Send docket API Error")
        return {"status": 500, "message": {"error": str(e)}}



@frappe.whitelist()
@role_required(["Admin"])
def get_all_docket_revisions(docket_id):
    try:
        if not docket_id:
            return {'status': 400, 'message': 'DocketId is required.'}

        docket = frappe.get_doc("Docket DB", docket_id)
        if not docket:
            return {'status': 404, 'message': 'Docket not found'}

        customer_flag_for_custom_docs = frappe.get_value("Customer DB", {"name": docket.customer_id}, "flag_for_custom_docs")

        if docket.docket_revisions:
            # Sort all revisions by revision_number (ascending)
            sorted_revisions = sorted(
                docket.docket_revisions,
                key=lambda x: int(x.revision_number),
                reverse=True
            )

            # Get the latest revision
            latest_revision = sorted_revisions[-1]
            # latest_revision.show_to_customer = 1  # set to True

            # Check if invoice is already generated for latest revision
            invoice_already_generated = bool(latest_revision.invoice)

            docket.save(ignore_permissions=True)
            frappe.db.commit()

            site_url = frappe.utils.get_url()

            revisions_data = []

            for row in sorted_revisions:
                # Fetch additional attachments for this revision
                additional_files = frappe.get_all(
                    "Additional Revision Attachments",
                    filters={"docket_revision": row.name},
                    fields=["name", "file_name", "attachments"]
                )

                # Convert to full URLs
                attachments_data = [
                    {
                        "name": f.get("name"),
                        "file_name": f.get("file_name"),
                        "url": site_url + f.get("attachments") if f.get("attachments") else "",
                    }
                    for f in additional_files
                ]

                # Common revision data
                revision_dict = {
                    "revision_number": row.revision_number,
                    "revision_name": row.name,
                    "packing_list": site_url + row.packing_list if row.packing_list else "",
                    "certificate_of_origin": site_url + row.certificate_of_origin if row.certificate_of_origin else "",
                    "form9": site_url + row.form9 if row.form9 else "",
                    "form6": site_url + row.form6 if row.form6 else "",
                    "bill_of_ladding": site_url + row.bill_of_ladding if row.bill_of_ladding else "",
                    "invoice":  site_url + row.invoice if row.invoice else "",
                    "status": row.status,
                    "show_to_customer": row.show_to_customer,
                    "creation": row.created_on,
                    "customer_response_on": row.customer_response_on,
                    "is_invoice_created": row.is_invoice_created,
                    "is_custom_docs_needed": customer_flag_for_custom_docs,
                    "additional_attachments": attachments_data
                }

                # Conditionally include ISCC documents
                if customer_flag_for_custom_docs:
                    revision_dict.update({
                        "isccplus_self_declaration": site_url + row.isccplus_self_declaration if row.isccplus_self_declaration else "",
                        "iscc_self_declaration": site_url + row.iscc_self_declaration if row.iscc_self_declaration else "",
                    })
     
                revisions_data.append(revision_dict)
    
            return {
                "status": 200,
                "message": "Docket revision fetched successfully",
                "docket_id": docket.name,
                "invoice_already_generated": invoice_already_generated,
                "revisions": revisions_data
            }

        else:
            return {'status': 400, 'message': 'No revisions found.'}

    except frappe.DoesNotExistError:
        return {"status": 404, "message": "Docket revision not found"}

    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Docket revisions not found API Error")
        return {"status": 500, "message": {"error": str(e)}}