import frappe
import json
from frappe import _
from datetime import datetime
from westside.www.Authentication.auth_decorators import role_required



@frappe.whitelist()
@role_required(["Admin"])
def get_all_docket(search=None, status=None, page=1, page_size=10):
    try:
        try:
            page = int(page)
            page_size = int(page_size)
        except ValueError:
            frappe.response["http_status_code"] = 400
            frappe.response["message"] = {"error": "Invalid page or page_size value. Must be an integer."}
            return

        if page < 1 or page_size < 1:
            frappe.response["http_status_code"] = 400
            frappe.response["message"] = {"error": "Page and page_size must be greater than zero."}
            return

        filters = {}
        if status:
            filters["status"] = status

        offset = (page - 1) * page_size

        or_filters = []
        if search:
            search_term = f"%{search}%"
            or_filters.append(["customer_name", "like", search_term])
            or_filters.append(["name", "like", search_term])
           
        docket_list = frappe.get_all(
            "Docket DB",
            filters=filters,
            or_filters=or_filters, 
            fields=[
                "name", "booking", "customer", "customer_name", "revision_number", "shipper", "blno", 'carrier_booking_number',
                "shipping_date", "origin", "origin_of_goods", "status", "origin", "destination", 'invoice', 'weight', 'customer_invoice_id',
                "package_count", "package_type", "category"
            ],
            limit_start=offset,
            limit_page_length=page_size
        )

        response_data = []
        for docket in docket_list:
            
            bill_of_lading_doc = None
            if frappe.db.exists("Bill of Lading", {"bol_number": docket.blno}):
                bill_of_lading_doc = frappe.get_doc("Bill of Lading", {"bol_number": docket.blno})

            port_of_origin = None
            place_of_carrier_reciept = None
            ETA = None

            if docket.carrier_booking_number:

                booking_doc = None
                if frappe.db.exists("Booking Request", {"carrier_booking_number": docket.carrier_booking_number}):
                    booking_doc = frappe.get_doc("Booking Request", {"carrier_booking_number": docket.carrier_booking_number})

                if booking_doc:

                    carriages = frappe.get_all("Booking Main Carriage", filters={"parent": booking_doc}, fields=["eta", "etd"])

                    if carriages:
                        # Sort by etd, latest first
                        carriages_sorted = sorted(carriages, key=lambda x: x.etd or datetime.min)
                        latest = carriages_sorted[-1]
                        ETA = latest.eta.strftime("%d-%b-%Y") if latest and latest.eta else None


            invoice_total = None
            if docket.customer_invoice_id:
                try:
                    invoice_doc = frappe.get_doc("Invoice DB", docket.customer_invoice_id) 
                    invoice_total = invoice_doc.total_amount
                except:
                    frappe.log_error(f"Invoice not found: {docket.customer_invoice_id}", "Invoice Lookup Failed")
                    pass
            try:
                docket_data = {
                    "id": docket.name,
                    "booking_id": docket.booking if docket.booking else None,
                    "carrier_booking_number": docket.carrier_booking_number,
                    "customer_id": docket.customer,
                    "customer_name": docket.customer_name,
                    "revision_number": docket.revision_number,
                    "shipper": docket.shipper,
                    "bol_number": docket.blno,
                    "shipping_date": docket.shipping_date,
                    "port_of_origin": docket.origin, 
                    "place_of_receipt": docket.destination,
                    "status": docket.status, 
                    "revision": docket.revision_number,
                    "ETA": ETA,
                    "carrier": bill_of_lading_doc.carrier if bill_of_lading_doc and bill_of_lading_doc.carrier else "", 
                    "container_qty": bill_of_lading_doc.total_equipment if bill_of_lading_doc and bill_of_lading_doc.total_equipment else "", 
                    "weight": f"{float(docket.weight):.2f}" if docket.weight not in [None, "", "null"] else None,
                    "invoice_no": docket.invoice,
                    "invoice_total": invoice_total,
                    "package_count": docket.package_count,
                    "package_type": docket.package_type,
                    "category": docket.category
                }
                response_data.append(docket_data)

            except Exception as docket_error:
                frappe.log_error(f"Error processing docket {docket.name}: {str(docket_error)}", "Docket Processing Error")

        total_count = len(frappe.get_all(
                            "Docket DB",
                            filters=filters,
                            or_filters=or_filters,
                            fields=["name"]
                        ))
        total_pages = (total_count + page_size - 1) // page_size

        frappe.response["message"] = {
            "total_count": total_count,
            "page": page,
            "page_size": page_size,
            "total_pages": total_pages,
            "docket": response_data
        }

    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Get All Docket API Error")
        frappe.response["http_status_code"] = 500
        frappe.response["message"] = {"error": str(e)}


