import frappe
import io
import os
import uuid
import base64
from frappe import _
import xlsxwriter
from frappe.utils import getdate, nowdate, get_url, get_site_path
from frappe.utils.response import build_response
from frappe.utils.file_manager import save_file
from frappe.utils.file_manager import save_file_on_filesystem, get_file_name
from datetime import date, datetime


@frappe.whitelist(allow_guest=True)
def get_bill_report(vendor_id=None, from_date=None, to_date=None):
    filters = {}

    if vendor_id:
        if not frappe.db.exists("Vendor", {"name": vendor_id}):
            return {"status_code": 404, "message": f"Vendor with '{vendor_id}' id does not exist."}
        filters["vendor"] = vendor_id
    else:
        return {"status_code": 400, "message": f"Vendor id is required."}


    if from_date and to_date:
        try:
            from_date = getdate(from_date)
            to_date = getdate(to_date)
        except Exception as e:
            return {"status_code": 400, "message": f"Invalid date format: {e}"}

        # Use >= and <= instead of "between" to handle nulls properly
        filters["bill_date"] = [">=", from_date]
        
    try:
        if from_date and to_date:
            # Get all records first, then filter by date range manually
            # This avoids the SQL syntax issue with null values
            bill_list = frappe.get_all(
                "Bill", 
                filters={
                    k: v for k, v in filters.items() if k != "bill_date"
                } | {"is_active": 1},
                fields=[
                    "name", "booking_id", "job_id", "qb_bill_id", "vendor_bill_no",
                    "bill_date", "total_amount", "balance_amount", "paid_amount", 
                    "status", "due_date","document_number","is_active"
                ],
            )
            for bill in bill_list:
                overdue_days = ""
                due_date = bill.get("due_date")
                balance_amount = bill.get("balance_amount") or 0  # handle None

                if balance_amount > 0 and due_date:
                    due_date_val = getdate(due_date)
                    today = date.today()
                    if today > due_date_val:
                        overdue_days = (today - due_date_val).days
                    else:
                        overdue_days = 0
                else:
                    overdue_days = 0

                bill["overdue_days"] = overdue_days

            # Filter by date range manually, handling null values
            filtered_list = []
            for bill in bill_list:
                bill_date = bill.get("bill_date")

                if bill_date and from_date <= getdate(bill_date) <= to_date:
                    filtered_list.append(bill)

                # Optionally include null dates - uncomment next 2 lines if you want null dates included
                # elif not bill_date:
                #     filtered_list.append(bill)

            bill_list = filtered_list
        else:
            # No date filtering, get all records
            bill_list = frappe.get_all(
                "Bill", 
                filters={**filters, "is_active": 1},
                fields=[
                    "name", "booking_id", "job_id", "qb_bill_id", "vendor_bill_no",
                    "bill_date", "total_amount", "balance_amount", "paid_amount", 
                    "status", "due_date","document_number","is_active"
                ],
            )

        return {
            "status_code": 200,
            "message": "Report generated successfully",
            "data": bill_list
        }
        
    except Exception as e:
        frappe.log_error(f"Bill Report Error: {str(e)}")
        return {
            "status_code": 500,
            "message": f"Error generating report: {str(e)}"
        }


@frappe.whitelist(allow_guest=True)
def export_bill_report(vendor_id=None, from_date=None, to_date=None):
    filters = {}

    if vendor_id:
        if not frappe.db.exists("Vendor", {"name": vendor_id}):
            return {"status_code": 404, "message": f"Vendor with '{vendor_id}' id does not exist."}
        filters["vendor"] = vendor_id
    else:
        return {"status_code": 400, "message": f"Vendor id is required."}

    
    vendor_doc = frappe.get_doc("Vendor", {"name": vendor_id})

    if from_date and to_date:
        try:
            # Try DD-MM-YYYY first (most common from frontend)
            from_date = datetime.strptime(from_date, "%d-%m-%Y").date()
            to_date = datetime.strptime(to_date, "%d-%m-%Y").date()
        except ValueError:
            try:
                # Fallback to YYYY-MM-DD (ISO format)
                from_date = datetime.strptime(from_date, "%Y-%m-%d").date()
                to_date = datetime.strptime(to_date, "%Y-%m-%d").date()
            except ValueError as e:
                return {"status_code": 400, "message": f"Invalid date format: {e}"}

        filters["bill_date"] = [">=", from_date]
        
    try:
        if from_date and to_date:
            # Get all records first, then filter by date range manually
            # This avoids the SQL syntax issue with null values
            bill_list = frappe.get_all(
                "Bill", 
                filters={k: v for k, v in filters.items() if k != "bill_date"} | {"is_active": 1},
                fields=[
                    "name", "booking_id", "job_id", "qb_bill_id", "vendor_bill_no",
                    "bill_date", "total_amount", "balance_amount", "paid_amount", 
                    "status", "due_date","document_number","is_active"
                ],
            )
            
            # Filter by date range manually, handling null values
            filtered_list = []
            for bill in bill_list:
                bill_date = bill.get("bill_date")

                if bill_date and from_date <= getdate(bill_date) <= to_date:
                    filtered_list.append(bill)

                # Optionally include null dates - uncomment next 2 lines if you want null dates included
                # elif not bill_date:
                #     filtered_list.append(bill)

            bill_list = filtered_list
        else:
            # No date filtering, get all records
            bill_list = frappe.get_all(
                "Bill", 
                filters={**filters, "is_active": 1},
                fields=[
                    "name", "booking_id", "job_id", "qb_bill_id", "vendor_bill_no",
                    "bill_date", "total_amount", "balance_amount", "paid_amount", 
                    "status", "due_date","document_number","is_active"
                ],
            )
    except Exception as e:
        frappe.log_error(f"Bill Report Error: {str(e)}")
        return {
            "status_code": 500,
            "message": f"Error generating report: {str(e)}"
        }

     # product group 

    # Create in-memory file
    output = io.BytesIO()
    workbook = xlsxwriter.Workbook(output, {'in_memory': True})
    worksheet = workbook.add_worksheet("Bill Report")

    # === Formats ===
    bold = workbook.add_format({'bold': True})
    
    # Center aligned date format
    date_format = workbook.add_format({
        'num_format': 'mmm-dd-yyyy',
        'align': 'left',
        'valign': 'vcenter'
    })

    # Center aligned data format (for all data cells)
    data_format = workbook.add_format({
        'align': 'left',
        'valign': 'vcenter'
    })

    # Center aligned date data format
    data_date_format = workbook.add_format({
    "num_format": "mmm-dd-yyyy",
    "align": "left",
    "valign": "vcenter"
    })
    date_left_format = workbook.add_format({
    "num_format": "mmm-dd-yyyy",
    "align": "left",
    "valign": "vcenter"
    })
    column_align = workbook.add_format({
        'align': 'left',
        'valign': 'vcenter'
    })
    data_right_align = workbook.add_format({
        'align': 'right',
        'valign': 'vcenter'
    })

    # Centered + bold heading format
    title_format = workbook.add_format({
        'bold': True,
        'align': 'center',
        'valign': 'vcenter',
        'font_size': 14
    })

    sub_title_format = workbook.add_format({
        'bold': True,
        'align': 'center',
        'valign': 'vcenter'
    })

    # Centered header format
    header_format = workbook.add_format({
        'bold': True,
        'align': 'center',
        'valign': 'vcenter'
    })
    # === Header setup ===
    headers = [
        "Sno.", "Booking #", "Job #", "Bill #", "Product Group", "Bill Date", "Bill Amount", 
        "Status", "Overdue Days"
    ]

    # === Set column widths ===
    column_widths = [6, 15, 15, 15, 15, 20, 18, 18, 18]
    for col, width in enumerate(column_widths):
        worksheet.set_column(col, col, width)

    # === Row tracker ===
    row = 0

    # === Title (merged + centered) ===
    worksheet.merge_range(row, 0, row, len(headers) - 2, "Bill Report", title_format)
    row += 2

    # === Subheadings ===
    worksheet.merge_range(
    row, 0, row, len(headers) - 2,
    f"Date Range: {from_date.strftime('%b-%d-%Y')} to {to_date.strftime('%b-%d-%Y')}",
    sub_title_format)   
    row += 1
    worksheet.merge_range(row, 0, row, len(headers) - 2, "Vendor: {}".format(vendor_doc.vendor_name), sub_title_format)
    row += 2

    # === Header Row ===
    for col, header in enumerate(headers):
        worksheet.write(row, col, header, header_format)
    row += 1

    # === Data Rows ===
    for i, result in enumerate(bill_list, start=1):
        bill_date = result.get("bill_date")
        bill_date_val = ""
        if bill_date:
            bill_date_val = getdate(bill_date)

        # Overdue days calculation
        overdue_days = ""
        due_date = result.get("due_date")
        if due_date:
            due_date_val = getdate(due_date)
            if date.today() > due_date_val:
                overdue_days = (date.today() - due_date_val).days
            else:
                overdue_days = 0

        worksheet.write(row, 0, i, column_align)
        worksheet.write(row, 1, result.get("booking_id"), column_align)
        worksheet.write(row, 2, result.get("job_id"), column_align)
        worksheet.write(row, 3, result.get("document_number"), column_align)   
        worksheet.write(row, 4, result.get("product_group", ""), column_align)
        worksheet.write(row, 5, bill_date_val, date_left_format)              
        worksheet.write(row, 6, result.get("total_amount", ""),data_right_align )
        worksheet.write(row, 7, result.get("status", ""), column_align)
        worksheet.write(row, 8, overdue_days, column_align)                  
        row += 1
    # Finalize Excel
    workbook.close()
    output.seek(0)

    # Generate filename
    filename = f"Bill_Report_{nowdate()}_{uuid.uuid4().hex[:8]}.xlsx"

    # Get site filesystem path (correct, not URL)
    site_path = get_site_path()

    # Construct physical path to public files folder
    public_files_path = os.path.join(site_path, 'public', 'files', filename)

    # Ensure directory exists
    os.makedirs(os.path.dirname(public_files_path), exist_ok=True)

    # Write the Excel file to disk (output is your BytesIO)
    output.seek(0)  # rewind buffer to start
    with open(public_files_path, 'wb') as f:
        f.write(output.read())

    # Construct public URL (files under public/files are served at /files/)
    file_url = get_url(f"/files/{filename}")

    # Return info including physical path, URL and filename
    return {
        "status_code": 200,
        "message": "Excel Report generated successfully",
        "file_name": filename,
        "file_url": file_url,
    }