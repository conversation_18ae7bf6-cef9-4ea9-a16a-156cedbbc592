import frappe
import io
import os
import uuid
import base64
from datetime import datetime
from frappe import _
import xlsxwriter
from frappe.utils import getdate, nowdate, get_url, get_site_path
from frappe.utils.response import build_response
from frappe.utils.file_manager import save_file
from frappe.utils.file_manager import save_file_on_filesystem, get_file_name



@frappe.whitelist(allow_guest=True)
def get_vendor_report(vendor_id=None, from_date=None, to_date=None, parent_company=None):

    if vendor_id:
        if not frappe.db.exists("Vendor", {"name": vendor_id}):
            return {"status_code": 404, "message": f"Vendor with '{vendor_id}' id does not exists." }
    else:
        return {"status_code": 400, "message": f"Vendor id is required."}

    filters = {}
    filters["vendor_name"] = vendor_id

    job_list = frappe.get_all("Job",
        filters=filters,
        fields=["name", "booking_id", "customer", "status", "no_of_containers", "vendor_name"]
    )
    
    # Input for filters
    from_date = getdate(from_date)
    to_date = getdate(to_date)

   
    filters = {}
    filters["shipped_on_board_date"] = ["between", [from_date, to_date]]    # shipped_on_board_date from bill of lading.
    
    # if parent_company:      # Can do later
    #     filters["parent_company"] = parent_company

    results = []

    if job_list:
        for job_data in job_list:
            
            if job_data.booking_id:
                booking_request_doc = frappe.get_doc("Booking Request", job_data.booking_id)
                carrier_booking = booking_request_doc.carrier_booking_number

                filters["carrier_booking_number"] = carrier_booking

                bol_records = frappe.get_all(
                    "Bill of Lading",
                    filters=filters,
                    fields=["name", "carrier_booking_number", "bol_number", "carrier", "shipped_on_board_date"],
                    limit_page_length=1
                )
            
                if not bol_records:
                    continue
                
                if job_data.vendor_name:
                    vendor_doc = frappe.get_doc("Vendor", job_data.vendor_name)

                bol_data = bol_records[0]
                bol_doc = frappe.get_doc("Bill of Lading", bol_data["name"])

                bol_data["booking_id"] = job_data.booking_id if job_data else None
                bol_data["job"] = job_data.name
                bol_data["vendor_name"] = vendor_doc.vendor_name if vendor_doc and vendor_doc.vendor_name else None

                ETA = None
                if booking_request_doc.latest_delivery_date:
                    ETA = booking_request_doc.latest_delivery_date.strftime("%b-%d-%Y")
                bol_data["ETA"] = ETA
                # Product Group
                # ETA 

                # No of containers and Net weight
                equipments = frappe.get_all(
                    'Equipments', 
                    filters={'booking_request': booking_request_doc.name}, 
                    fields=['name', 'equipment_name'], 
                    order_by='creation desc'
                )

                total_net_weight = 0  
                for eq in equipments:
                    cargo = frappe.get_value(
                        'Cargo',
                        filters={'parent': eq.name, 'parenttype': 'Equipments'},
                        fieldname=['net_weight'],
                        as_dict=True
                    )
                    
                    net_weight = cargo.net_weight if cargo and cargo.net_weight else 0
                    total_net_weight += net_weight

                bol_data["no_of_containers"] = len(equipments)
                bol_data['net_weight'] = (total_net_weight / 1000) if total_net_weight else None  # in tons



                # Bill
                bol_data['bill_value'] = None
                if frappe.db.exists("Bill", {"job_id": job_data.name, "is_active": True}):
                    bill_doc = frappe.get_doc("Bill", {"job_id": job_data.name, "is_active": True})
                    bol_data['bill_value'] = bill_doc.total_amount if bill_doc and bill_doc.total_amount else None

                results.append(bol_data)

    
    # Return info including physical path, URL and filename
    return {
        "status_code": 200,
        "message": "Report generated successfully",
        "data": results
    }



@frappe.whitelist(allow_guest=True)
def export_vendor_report(vendor_id=None, from_date=None, to_date=None, parent_company=None):

    if vendor_id:
            if not frappe.db.exists("Vendor", {"name": vendor_id}):
                return {"status_code": 404, "message": f"Vendor with '{vendor_id}' id does not exists." }
    else:
        return {"status_code": 400, "message": f"Vendor id is required."}

    filters = {}
    filters["vendor_name"] = vendor_id

    job_list = frappe.get_all("Job",
        filters=filters,
        fields=["name", "booking_id", "customer", "status", "no_of_containers", "vendor_name"]
    )
    
    # Input for filters
    try:
        from_date = datetime.strptime(from_date, "%d-%m-%Y").date()
        to_date = datetime.strptime(to_date, "%d-%m-%Y").date()
    except ValueError:
        # fallback in case UI sends ISO format
        from_date = datetime.strptime(from_date, "%Y-%m-%d").date()
        to_date = datetime.strptime(to_date, "%Y-%m-%d").date()

    filters = {}
    filters["shipped_on_board_date"] = ["between", [from_date, to_date]]
    
    # if parent_company:      # Can do later
    #     filters["parent_company"] = parent_company

    results = []

    if job_list:
        for job_data in job_list:
            
            if job_data.booking_id:
                booking_request_doc = frappe.get_doc("Booking Request", job_data.booking_id)
                carrier_booking = booking_request_doc.carrier_booking_number

                filters["carrier_booking_number"] = carrier_booking

                bol_records = frappe.get_all(
                    "Bill of Lading",
                    filters=filters,
                    fields=["name", "carrier_booking_number", "bol_number", "carrier", "shipped_on_board_date"],
                    limit_page_length=1
                )
            
                if not bol_records:
                    continue
                
                if job_data.vendor_name:
                    vendor_doc = frappe.get_doc("Vendor", job_data.vendor_name)

                bol_data = bol_records[0]
                bol_doc = frappe.get_doc("Bill of Lading", bol_data["name"])

                bol_data["booking_id"] = job_data.booking_id if job_data else None
                bol_data["job"] = job_data.name
                bol_data["vendor_name"] = vendor_doc.vendor_name if vendor_doc and vendor_doc.vendor_name else None

                ETA = None
                if booking_request_doc.latest_delivery_date:
                    ETA = booking_request_doc.latest_delivery_date.strftime("%b-%d-%Y")
                bol_data["ETA"] = ETA
                # Product Group
                # ETA 

                # No of containers and Net weight
                equipments = frappe.get_all('Equipments', 
                    filters={'booking_request': booking_request_doc.name}, 
                    fields=['name', 'equipment_name'], 
                    order_by='creation desc'
                )

                total_net_weight = 0  
                for eq in equipments:
                    cargo = frappe.get_value(
                        'Cargo',
                        filters={'parent': eq.name, 'parenttype': 'Equipments'},
                        fieldname=['net_weight'],
                        as_dict=True
                    )
                    
                    net_weight = cargo.net_weight if cargo and cargo.net_weight else 0
                    total_net_weight += net_weight
                bol_data["no_of_containers"] = len(equipments)
                bol_data['net_weight'] = (total_net_weight / 1000) if total_net_weight else None  # in tons

                # Bill
                bol_data['bill_value'] = None
                if frappe.db.exists("Bill", {"job_id": job_data.name, "is_active": True}):
                    bill_doc = frappe.get_doc("Bill", {"job_id": job_data.name, "is_active": True})
                    bol_data['bill_value'] = bill_doc.total_amount if bill_doc and bill_doc.total_amount else None

                results.append(bol_data)

    # Create in-memory file
    output = io.BytesIO()
    workbook = xlsxwriter.Workbook(output, {'in_memory': True})
    worksheet = workbook.add_worksheet("Vendor Report")

    # === Formats ===
    bold = workbook.add_format({'bold': True})
    
    # Center aligned date format
    date_format = workbook.add_format({
        'num_format': 'mmm-dd-yyyy',
        'align': 'center',
        'valign': 'vcenter'
    })

    # Center aligned data format (for all data cells)
    data_format = workbook.add_format({
        'align': 'center',
        'valign': 'vcenter'
    })

    # Center aligned date data format
    data_date_format = workbook.add_format({
        'num_format': 'mmm-dd-yyyy',
        'align': 'center',
        'valign': 'vcenter'
    })

    column_align = workbook.add_format({
        'align': 'center',
        'valign': 'vcenter'
    })

    # Centered + bold heading format
    title_format = workbook.add_format({
        'bold': True,
        'align': 'center',
        'valign': 'vcenter',
        'font_size': 14
    })

    sub_title_format = workbook.add_format({
        'bold': True,
        'align': 'center',
        'valign': 'vcenter'
    })

    # Centered header format
    header_format = workbook.add_format({
        'bold': True,
        'align': 'center',
        'valign': 'vcenter'
    })

        # === Headers  ===
    headers = [
        "Sno.", "Booking#", "Carrier", "Job#", "Vendor",
        "Product group", "Shipment Date", "ETA",
        "# of containers", "Net weight in Tons", "Bill value"
    ]

    column_widths = [6, 15, 15, 15, 45, 20, 18, 18, 16, 16, 15]
    for col, width in enumerate(column_widths):
        worksheet.set_column(col, col, width)

    row = 0

    # === Title row ===
    worksheet.merge_range(row, 0, row, len(headers)-1, "Shipment Report By Vendor", title_format)
    row += 2

    # === Subheader rows ===
    worksheet.merge_range(
    row, 0, row, len(headers)-1,
    f"Date Range: {from_date.strftime('%b-%d-%Y')} to {to_date.strftime('%b-%d-%Y')}", sub_title_format)

    row += 1
    worksheet.merge_range(row, 0, row, len(headers)-1, f"Parent: {parent_company or ''}", sub_title_format)
    row += 1
    worksheet.merge_range(row, 0, row, len(headers)-1, f"Vendor: {frappe.get_value('Vendor', vendor_id, 'vendor_name')}", sub_title_format)
    row += 2

    # === Header Row ===
    for col, header in enumerate(headers):
        worksheet.write(row, col, header, header_format)
    row += 1

    # === Data Rows ===
    total_net_weight = 0
    # total_bill_value = 0
    total_containers = 0

    for i, result in enumerate(results, start=1):
        worksheet.write(row, 0, i, column_align)  # Sno.
        worksheet.write(row, 1, result.get("carrier_booking_number"), column_align)  # Booking#
        worksheet.write(row, 2, result.get("carrier"), column_align)  # Carrier
        worksheet.write(row, 3, result.get("job"), column_align)  # Job#
        worksheet.write(row, 4, result.get("vendor_name", ""), column_align)  # Vendor
        worksheet.write(row, 5, result.get("product_categories", ""), column_align)  # Product Group

        # Shipment Date
        shipped_date = result.get("shipped_on_board_date")
        if shipped_date:
            try:
                shipped_date = getdate(shipped_date)
            except Exception:
                shipped_date = None
        # worksheet.write(row, 6, shipped_date, date_format)

        # ETA
        eta_date = result.get("ETA")
        worksheet.write(row, 7, eta_date, column_align)

        # No of containers
        worksheet.write(row, 8, result.get("no_of_containers", 0), column_align)
        total_containers += result.get("no_of_containers", 0)

        # Net weight in tons
        net_weight = result.get("net_weight") or 0
        worksheet.write(row, 9, round(net_weight, 2), column_align)
        total_net_weight += net_weight

        # Bill Value
        bill_value = result.get("bill_value") or 0
        worksheet.write(row, 10, bill_value, column_align)
        # total_bill_value += bill_value

        row += 1

    # === Totals Row ===
    worksheet.write(row, 7, "Totals", header_format)
    worksheet.write(row, 8, total_containers, header_format)
    worksheet.write(row, 9, round(total_net_weight, 2), header_format)
    # worksheet.write(row, 10, total_bill_value, header_format)


    # Finalize Excel
    workbook.close()
    output.seek(0)   

    # Generate filename
    filename = f"Vendor_Report_{nowdate()}_{uuid.uuid4().hex[:8]}.xlsx"

    # Get site filesystem path (correct, not URL)
    site_path = get_site_path()

    # Construct physical path to public files folder 
    public_files_path = os.path.join(site_path, 'public', 'files', filename)

    # Ensure directory exists
    os.makedirs(os.path.dirname(public_files_path), exist_ok=True)

    # Write the Excel file to disk (output is your BytesIO)
    output.seek(0)  # rewind buffer to start
    with open(public_files_path, 'wb') as f:
        f.write(output.read())

    # Construct public URL (files under public/files are served at /files/)
    file_url = get_url(f"/files/{filename}")

    # Return info including physical path, URL and filename
    return {
        "status_code": 200,
        "message": "Excel Report generated successfully",
        "file_name": filename,
        "file_url": file_url,
    }