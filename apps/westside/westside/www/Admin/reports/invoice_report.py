import frappe
import io
import os
import uuid
import base64
from frappe import _
import xlsxwriter
from frappe.utils import getdate, nowdate, get_url, get_site_path
from frappe.utils.response import build_response
from frappe.utils.file_manager import save_file
from frappe.utils.file_manager import save_file_on_filesystem, get_file_name


@frappe.whitelist(allow_guest=True)
def get_invoice_report(customer_id=None, from_date=None, to_date=None):
    filters = {}

    if customer_id:
        if not frappe.db.exists("Customer DB", {"name": customer_id}):
            return {"status_code": 404, "message": f"Customer with '{customer_id}' id does not exist."}
        filters["customer_id"] = customer_id
    else:
        return {"status_code": 400, "message": f"Customer id is required."}

    if from_date and to_date:
        try:
            from_date = getdate(from_date)
            to_date = getdate(to_date)
        except Exception as e:
            return {"status_code": 400, "message": f"Invalid date format: {e}"}

        # Use >= and <= instead of "between" to handle nulls properly
        filters["invoice_date"] = [">=", from_date]
        
    try:
        # Fetch invoices
        invoice_list = frappe.get_all(
            "Invoice DB", 
            filters={
                **{k: v for k, v in filters.items() if k != "invoice_date"},
                "is_active": 1
            },
            fields=[
                "name", "booking_id", "carrier_booking_number", "bol",
                "invoice_number", "invoice_date", "due_date", "total_amount",
                "status","is_active"
            ],
        )

        # Manual filtering + overdue calculation
        filtered_list = []
        today = getdate(nowdate())

        for invoice in invoice_list:
            invoice_date = invoice.get("invoice_date")

            # Apply date range filter manually
            if from_date and to_date:
                if not (invoice_date and from_date <= getdate(invoice_date) <= to_date):
                    continue

            # Calculate overdue days
            days_overdue = 0
            due_date = invoice.get("due_date")
            if due_date and invoice.get("status") not in ["Paid", "Voided"]:
                if today > getdate(due_date):
                    days_overdue = (today - getdate(due_date)).days

            # Inject into response
            invoice["days_overdue"] = days_overdue
            filtered_list.append(invoice)


        return {
            "status_code": 200,
            "message": "Report generated successfully",
            "data": filtered_list
        }
        
    except Exception as e:
        frappe.log_error(f"Invoice Report Error: {str(e)}")
        return {
            "status_code": 500,
            "message": f"Error generating report: {str(e)}"
        }


@frappe.whitelist(allow_guest=True)
def export_invoice_report(customer_id=None, from_date=None, to_date=None):
    filters = {}

    if customer_id:
        if not frappe.db.exists("Customer DB", {"name": customer_id}):
            return {"status_code": 404, "message": f"Customer with '{customer_id}' id does not exist."}
        filters["customer_id"] = customer_id
    else:
        return {"status_code": 400, "message": f"customer id is required."}
    
    customer_doc = frappe.get_doc("Customer DB", {"name": customer_id})

    if from_date and to_date:
        try:
            from_date = getdate(from_date)
            to_date = getdate(to_date)
        except Exception as e:
            return {"status_code": 400, "message": f"Invalid date format: {e}"}

    try:
        # Always fetch invoices first
        invoice_list = frappe.get_all(
            "Invoice DB", 
            filters={
                **{k: v for k, v in filters.items()},
                "is_active": 1
            },  
            fields=[
                "name", "booking_id", "carrier_booking_number", "bol",
                "invoice_number", "invoice_date", "due_date", "total_amount",
                "status","is_active"
            ],
        )

        today = getdate(nowdate())
        filtered_list = []

        for invoice in invoice_list:
            invoice_date = invoice.get("invoice_date")

            # Apply manual date range filter
            if from_date and to_date:
                if not (invoice_date and from_date <= getdate(invoice_date) <= to_date):
                    continue

            # === Overdue calculation ===
            days_overdue = 0
            due_date = invoice.get("due_date")
            if due_date and invoice.get("status") not in ["Paid", "Voided"]:
                if today > getdate(due_date):
                    days_overdue = (today - getdate(due_date)).days

            invoice["days_overdue"] = days_overdue
            filtered_list.append(invoice)

        invoice_list = filtered_list

    except Exception as e:
        frappe.log_error(f"Invoice Report Error: {str(e)}")
        return {
            "status_code": 500,
            "message": f"Error generating report: {str(e)}"
        }

    # Create in-memory file
    output = io.BytesIO()
    workbook = xlsxwriter.Workbook(output, {'in_memory': True})
    worksheet = workbook.add_worksheet("Invoice Report")

    # === Formats ===
    bold = workbook.add_format({'bold': True})
    column_align = workbook.add_format({'align': 'left', 'valign': 'vcenter'})
    date_format = workbook.add_format({'num_format': 'mmm-dd-yyyy', 'align': 'left', 'valign': 'vcenter'})
    title_format = workbook.add_format({'bold': True,'align': 'center','valign': 'vcenter','font_size': 14})
    sub_title_format = workbook.add_format({'bold': True,'align': 'center','valign': 'vcenter'})
    header_format = workbook.add_format({'bold': True,'align': 'center','valign': 'vcenter'})
    data_right_align = workbook.add_format({'align': 'right', 'valign': 'vcenter'})

    # === Headers ===
    headers = [
        "Sno.", "Booking #", "Bol #", "Invoice #", "Invoice Date", "Invoice Amount",
        "Status", "Overdue Days"
    ]
    column_widths = [6, 15, 15, 15, 15, 20, 18, 18]
    for col, width in enumerate(column_widths):
        worksheet.set_column(col, col, width)

    # === Title ===
    row = 0
    worksheet.merge_range(row, 0, row, len(headers) - 2, "Invoice Report", title_format)
    row += 2
    worksheet.merge_range(row, 0, row, len(headers) - 2, f"Date Range: {getdate(from_date).strftime("%b-%d-%Y")} to {getdate(to_date).strftime("%b-%d-%Y")}", sub_title_format)
    row += 1
    worksheet.merge_range(row, 0, row, len(headers) - 2, f"Customer: {customer_doc.customer_name}", sub_title_format)
    row += 2

    # === Header Row ===
    for col, header in enumerate(headers):
        worksheet.write(row, col, header, header_format)
    row += 1

    # === Data Rows ===
    for i, result in enumerate(invoice_list, start=1):
        worksheet.write(row, 0, i, column_align)
        worksheet.write(row, 1, result.get("carrier_booking_number"), column_align)
        worksheet.write(row, 2, result.get("bol"), column_align)
        worksheet.write(row, 3, result.get("invoice_number"), column_align)

        # Invoice Date
        invoice_date = result.get("invoice_date")
        if invoice_date:
            worksheet.write(row, 4, getdate(invoice_date), date_format)
        else:
            worksheet.write(row, 4, "", column_align)

        # Invoice Amount
        worksheet.write(row, 5, result.get("total_amount", ""), data_right_align)

        # Status
        worksheet.write(row, 6, result.get("status", ""), column_align)

        # Overdue days
        worksheet.write(row, 7, result.get("days_overdue"), column_align)

        row += 1

    # Finalize Excel
    workbook.close()
    output.seek(0)

    # Generate filename
    filename = f"Invoice_Report_{nowdate()}_{uuid.uuid4().hex[:8]}.xlsx"
    site_path = get_site_path()
    public_files_path = os.path.join(site_path, 'public', 'files', filename)
    os.makedirs(os.path.dirname(public_files_path), exist_ok=True)

    with open(public_files_path, 'wb') as f:
        f.write(output.read())

    file_url = get_url(f"/files/{filename}")

    return {
        "status_code": 200,
        "message": "Excel Report generated successfully",
        "file_name": filename,
        "file_url": file_url,
    }
