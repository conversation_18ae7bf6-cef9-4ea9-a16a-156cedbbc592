import frappe
import io
import os
import uuid
import base64
from frappe import _
import xlsxwriter
from frappe.utils import getdate, nowdate, get_url, get_site_path
from frappe.utils.response import build_response
from frappe.utils.file_manager import save_file
from frappe.utils.file_manager import save_file_on_filesystem, get_file_name




@frappe.whitelist()
def get_customer_report(customer_id=None, from_date=None, to_date=None, parent_company=None):

    if customer_id:
            if not frappe.db.exists("Customer DB", {"name": customer_id}):
                return {"status_code": 404, "message": f"Customer with '{customer_id}' id does not exists." }
    else:
        return {"status_code": 400, "message": f"customer id is required."}

    filters = {}
    filters["consignee"] = customer_id

    si_data_list = frappe.get_all("Shipping Instructions",
        filters=filters,
        fields=["name", "carrier_booking_number"]
    )
    
    # Input for filters
    from_date = getdate(from_date).strftime("%b-%d-%Y")
    to_date = getdate(to_date).strftime("%b-%d-%Y")

   
    filters = {}
    filters["shipped_on_board_date"] = ["between", [from_date, to_date]]    # shipped_on_board_date from bill of lading.
    
    # if parent_company:      # Can do later
    #     filters["parent_company"] = parent_company

    bol_data_list = []

    if si_data_list:
        for si_data in si_data_list:
            carrier_booking = si_data.carrier_booking_number
            filters["carrier_booking_number"] = carrier_booking
            bol_records = frappe.get_all(
                "Bill of Lading",
                filters=filters,
                fields=["name", "carrier_booking_number", "bol_number", "carrier", "shipped_on_board_date"],
                limit_page_length=1
            )
            
            if not bol_records:
                continue

            bol_data = bol_records[0]
            bol_doc = frappe.get_doc("Bill of Lading", bol_data["name"])

            # Customer
            consignee = next(
                (party for party in bol_doc.parties if party.partner_role == "Consignee"),
                None
            )

            bol_data["customer_name"] = consignee.partner_name if consignee else None

            # Check if docket exists
            docket_name = frappe.get_value("Docket DB", {"blno": bol_doc.bol_number}, "name")

            if not docket_name:
                frappe.logger().info(f"Docket not found for B/L number: {bol_doc.bol_number}")
                continue  
            
            # Docket and revision number
            docket_doc = frappe.get_doc("Docket DB", {"blno": bol_doc.bol_number})
           
            bol_data["docket"] = docket_doc.name if docket_doc.name else None
            bol_data["revision"] = docket_doc.revision_number if docket_doc.revision_number else None

            # No of containers and net weight
            booking_name = frappe.get_value("Booking Request", {"carrier_booking_number": bol_doc.carrier_booking_number}, "name")
            
            if not booking_name:
                return {"status": 400, "message": f"No Booking Request found with carrier_booking_number :{carrier_booking_number}"}
            booking_request = frappe.get_doc("Booking Request", booking_name)
            
            ETA = None
            if booking_request.latest_delivery_date:
                ETA = booking_request.latest_delivery_date.strftime("%b-%d-%Y")
            bol_data["ETA"] = ETA
            
            equipments = frappe.get_all('Equipments', 
                filters={'booking_request': booking_request.name}, 
                fields=['name', 'equipment_name'], 
                order_by='creation desc'
            )

            total_net_weight = 0  
            for eq in equipments:
                cargo = frappe.get_value(
                    'Cargo',
                    filters={'parent': eq.name, 'parenttype': 'Equipments'},
                    fieldname=['net_weight'],
                    as_dict=True
                )
                net_weight = cargo.net_weight if cargo and cargo.net_weight else 0
                total_net_weight += net_weight

            bol_data['number_of_containers'] = len(equipments)
            bol_data['net_weight'] = (total_net_weight / 1000) if total_net_weight else None  # tons

            # Check if invoice is linked in Docket DB
            invoice_number = docket_doc.invoice  # QuickBooks invoice number stored in docket

            if invoice_number:
                invoice_list = frappe.get_all(
                    "Invoice DB",
                    filters={"invoice_number": invoice_number},
                    fields=["name", "total_amount"],
                    limit_page_length=1
                )
                
                if invoice_list:
                    invoice_doc = frappe.get_doc("Invoice DB", invoice_list[0].name)
                    
                    # Fetch product categories from child table 'items'
                    categories = [item.category_name for item in invoice_doc.items if getattr(item, "category_name", None)]
                    bol_data["product_categories"] = ", ".join(categories) if categories else None

                    
                    # Fetch total amount
                    bol_data["invoice_value"] = invoice_doc.total_amount if invoice_doc.total_amount else None
                else:
                    bol_data["product_categories"] = None
                    bol_data["invoice_value"] = None
            else:
                bol_data["product_categories"] = None
                bol_data["invoice_value"] = None

            bol_data_list.append(bol_data)

    
    # Return info including physical path, URL and filename
    return {
        "status_code": 200,
        "message": "Report generated successfully",
        "data": bol_data_list
    }


@frappe.whitelist()
def export_customer_report(customer_id=None, from_date=None, to_date=None, parent_company=None):

    if customer_id:
            if not frappe.db.exists("Customer DB", {"name": customer_id}):
                return {"status_code": 404, "message": f"Customer with '{customer_id}' id does not exists." }
    else:
        return {"status_code": 400, "message": f"customer id is required."}

    filters = {}
    filters["consignee"] = customer_id

    si_data_list = frappe.get_all("Shipping Instructions",
        filters=filters,
        fields=["name", "carrier_booking_number"]
    )
    
    # Input for filters
    from_date = getdate(from_date).strftime("%b-%d-%Y")
    to_date = getdate(to_date).strftime("%b-%d-%Y")

    filters = {}
    filters["shipped_on_board_date"] = ["between", [from_date, to_date]]    # shipped_on_board_date from bill of lading.

    # if parent_company:      # Can do later
    #     filters["parent_company"] = parent_company

    bol_data_list = []

    if si_data_list:
        for si_data in si_data_list:
            carrier_booking = si_data.carrier_booking_number
            filters["carrier_booking_number"] = carrier_booking
            bol_records = frappe.get_all(
                "Bill of Lading",
                filters=filters,
                fields=["name", "carrier_booking_number", "bol_number", "carrier", "shipped_on_board_date"],
                limit_page_length=1
            )
            
            if not bol_records:
                continue

            bol_data = bol_records[0]
            bol_doc = frappe.get_doc("Bill of Lading", bol_data["name"])

            # Customer
            consignee = next(
                (party for party in bol_doc.parties if party.partner_role == "Consignee"),
                None
            )
            bol_data["customer_name"] = consignee.partner_name if consignee else None

             # Check if docket exists
            docket_name = frappe.get_value("Docket DB", {"blno": bol_doc.bol_number}, "name")

            if not docket_name:
                frappe.logger().info(f"Docket not found for B/L number: {bol_doc.bol_number}")
                continue  

            
            
            # Docket and revision number
            docket_doc = frappe.get_doc("Docket DB", {"blno": bol_doc.bol_number})
            bol_data["docket"] = docket_doc.name if docket_doc.name else None
            bol_data["revision"] = docket_doc.revision_number if docket_doc.revision_number else None

            # No of containers and net weight
            booking_name = frappe.get_value("Booking Request", {"carrier_booking_number": bol_doc.carrier_booking_number}, "name")
            
            if not booking_name:
                return {"status": 400, "message": f"No Booking Request found with carrier_booking_number :{carrier_booking_number}"}
            booking_request = frappe.get_doc("Booking Request", booking_name)

            ETA = None
            if booking_request.latest_delivery_date:
                ETA = booking_request.latest_delivery_date.strftime("%b-%d-%Y")
            bol_data["ETA"] = ETA
            
            equipments = frappe.get_all('Equipments', 
                filters={'booking_request': booking_request.name}, 
                fields=['name', 'equipment_name'], 
                order_by='creation desc'
            )

            total_net_weight = 0  
            for eq in equipments:
                cargo = frappe.get_value(
                    'Cargo',
                    filters={'parent': eq.name, 'parenttype': 'Equipments'},
                    fieldname=['net_weight'],
                    as_dict=True
                )
                
                net_weight = cargo.net_weight if cargo and cargo.net_weight else 0
                total_net_weight += net_weight
            bol_data['number_of_containers'] = len(equipments)
            bol_data['net_weight'] = (total_net_weight / 1000) if total_net_weight else None  # tons

            invoice_number = docket_doc.invoice  

            if invoice_number:
                invoice_list = frappe.get_all(
                    "Invoice DB",
                    filters={"invoice_number": invoice_number},
                    fields=["name", "total_amount"],
                    limit_page_length=1
                )
                
                if invoice_list:
                    invoice_doc = frappe.get_doc("Invoice DB", invoice_list[0].name)
                    
                    # Fetch product categories from child table 'items'
                    categories = [item.category_name for item in invoice_doc.items if getattr(item, "category_name", None)]
                    bol_data["product_categories"] = ", ".join(categories) if categories else None
                    
                    # Fetch total amount
                    bol_data["invoice_value"] = invoice_doc.total_amount if invoice_doc.total_amount else None
                else:
                    bol_data["product_categories"] = None
                    bol_data["invoice_value"] = None
            else:
                bol_data["product_categories"] = None
                bol_data["invoice_value"] = None

            bol_data_list.append(bol_data)

        
    # product group, ETA, invoice value
    
    # Create in-memory file
    output = io.BytesIO()
    workbook = xlsxwriter.Workbook(output, {'in_memory': True})
    worksheet = workbook.add_worksheet("Shipment Report")

    # === Formats ===
    bold = workbook.add_format({'bold': True})
    date_format = workbook.add_format({'num_format': 'mmm-dd-yyyy'})


    # Centered + bold heading format
    title_format = workbook.add_format({
        'bold': True,
        'align': 'center',
        'valign': 'vcenter',
        'font_size': 14
    })

    sub_title_format = workbook.add_format({
        'bold': True,
        'align': 'center',
        'valign': 'vcenter'
    })

    # Centered header format
    header_format = workbook.add_format({
        'bold': True,
        'align': 'center',
        'valign': 'vcenter'
    })
    # Left-aligned format for data rows
    data_left_align = workbook.add_format({
        'align': 'left',
        'valign': 'vcenter'
    })
    date_left_format = workbook.add_format({
    'num_format': 'mmm-dd-yyyy',
    'align': 'left',
    'valign': 'vcenter'
    })
    data_right_align = workbook.add_format({
        'align': 'right',
        'valign': 'vcenter'
    })

    # === Header setup ===
    headers = [
        "Sno.", "Booking #", "Bol #", "Docket #", "Rev #", "Carrier", "Customer",
        "Product Group", "Shipment Date", "ETA", "No of Containers",
        "Net weight in Tons", "Invoice Value"
    ]

    # === Set column widths ===
    column_widths = [6, 15, 15, 15, 8, 15, 45, 18, 18, 12, 16, 20, 15]
    for col, width in enumerate(column_widths):
        worksheet.set_column(col, col, width)

    # === Row tracker ===
    row = 0

    # === Title (merged + centered) ===
    worksheet.merge_range(row, 0, row, len(headers) - 2, "Shipment Report By Customer", title_format)
    row += 2

    # === Subheadings ===
    worksheet.merge_range(row, 0, row, len(headers) - 2, f"Date Range: {getdate(from_date).strftime("%b-%d-%Y")} to {getdate(to_date).strftime("%b-%d-%Y")}", sub_title_format)
    row += 1
    worksheet.merge_range(row, 0, row, len(headers) - 2, "Parent: parent", sub_title_format)
    row += 1
    customer_name = bol_data_list[0].get("customer_name") if bol_data_list else None
    if customer_name:
        worksheet.merge_range(row, 0, row, len(headers) - 2, f"Customer: {customer_name}", sub_title_format)
        row += 2

    # === Header Row ===
    for col, header in enumerate(headers):
        worksheet.write(row, col, header, header_format)
    row += 1
    total_containers = 0
    total_net_weight = 0
    total_invoice_value = 0

    # === Data Rows ===
    for i, bol in enumerate(bol_data_list, start=1):
        worksheet.write(row, 0, i, data_left_align)
        worksheet.write(row, 1, bol.get("carrier_booking_number"), data_left_align)
        worksheet.write(row, 2, bol.get("bol_number"), data_left_align)
        worksheet.write(row, 3, bol.get("docket"), data_left_align)
        worksheet.write(row, 4, bol.get("revision"), data_left_align)
        worksheet.write(row, 5, bol.get("carrier"), data_left_align)
        worksheet.write(row, 6, bol.get("customer_name") or "", data_left_align)

        # Product Group
        categories = bol.get("product_categories", "")
        if isinstance(categories, list):
            categories = ", ".join(categories)
        worksheet.write(row, 7, categories, data_left_align)

        # Shipment Date
        shipped_date = bol.get("shipped_on_board_date")
        worksheet.write(row, 8, getdate(shipped_date) if shipped_date else "", date_left_format)

        # ETA
        eta = bol.get("ETA")
        worksheet.write(row, 9, getdate(eta) if eta else "", date_left_format)

        # Containers
        containers = bol.get("number_of_containers") or 0
        worksheet.write(row, 10, containers, data_right_align)
        total_containers += containers

        # Net Weight
        net_weight = bol.get("net_weight") or 0
        worksheet.write(row, 11, round(float(net_weight), 3) if net_weight else "", data_right_align)
        total_net_weight += net_weight or 0

        # Invoice Value
        invoice_value = bol.get("invoice_value") or 0
        worksheet.write(row, 12, invoice_value, data_right_align)
        total_invoice_value += invoice_value or 0

        row += 1

    # === Totals Row ===
    worksheet.write(row, 9, "Totals", header_format)
    worksheet.write(row, 10, total_containers, header_format)
    worksheet.write(row, 11, round(total_net_weight, 3), header_format)
    # worksheet.write(row, 12, total_invoice_value, header_format)

    # Finalize Excel
    workbook.close()
    output.seek(0)

    # Generate filename
    filename = f"Shipment_Report_{nowdate()}_{uuid.uuid4().hex[:8]}.xlsx"

    # Get site filesystem path (correct, not URL)
    site_path = get_site_path()

    # Construct physical path to public files folder
    public_files_path = os.path.join(site_path, 'public', 'files', filename)

    # Ensure directory exists
    os.makedirs(os.path.dirname(public_files_path), exist_ok=True)

    # Write the Excel file to disk (output is your BytesIO)
    output.seek(0)  # rewind buffer to start
    with open(public_files_path, 'wb') as f:
        f.write(output.read())

    # Construct public URL (files under public/files are served at /files/)
    file_url = get_url(f"/files/{filename}")

    # Return info including physical path, URL and filename
    return {
        "status_code": 200,
        "message": "Excel Report generated successfully",
        "file_name": filename,
        "file_url": file_url,
    }