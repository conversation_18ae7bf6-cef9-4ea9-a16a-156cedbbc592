import json
import frappe
from frappe import _
from westside.www.Authentication.auth_decorators import role_required




@frappe.whitelist()
@role_required(["Vendor"])
def get_vendor_details():
    try:
        vendor = frappe.session.user # current user
        vendor_id = frappe.get_value("Vendor", {"email_id": vendor}, "name")

        try:
            vendor_doc = frappe.get_doc("Vendor", vendor_id)
        except Exception as e:
            return {'status': 404, 'message': 'Vendor not found'}

        if vendor_doc.is_active == False:
            return {'status': 404, 'message': 'Vendor not found'}

        vendor_country_name = {}

        if vendor_doc.country:
            country_code = vendor_doc.country.lower()
            country_doc = frappe.db.get_value("Country", vendor_doc.country, ["name", "country_name", "code"], as_dict=True)
            if country_doc:
                vendor_country_name = country_doc
        
        # Collect all passkeys from child table
        passkeys = [row.passkey for row in vendor_doc.get("pass_key")]

        vendor_data = {
            "id": vendor_doc.name,
            "first_name": vendor_doc.first_name,
            "last_name": vendor_doc.last_name,
            "vendor_name": vendor_doc.vendor_name,
            "vendor_code": vendor_doc.vendor_code,
            "parent_company": vendor_doc.parent_company,
            "phone": vendor_doc.phone,
            "city": vendor_doc.city,
            "state": vendor_doc.state,
            "zip": vendor_doc.zip,
            "country": vendor_country_name,
            "email_id": vendor_doc.email_id,
            "contact": vendor_doc.contact,
            "vendor_address" : vendor_doc.vendor_address,
            "passkeys": passkeys 
        }

        return {'status': 200, 'message': 'Vendor details fetched successfully', 'data': vendor_data}
    except Exception as e:
        frappe.log_error(frappe.get_traceback(), "Get Vendor Details API Error")
        return {'status': 500, "message": f'Internal Server Error: {str(e)}'}
