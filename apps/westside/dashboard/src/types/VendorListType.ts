export type RawVendorType = {
    id: string;
    vendor_name: string;
    parent_company: string;
    city: string;
    state: string;
    zip: string;
    country: string;
    phone: string;
    email: string;
    message:any;
  };
  
  export type VendorListType = {
    message: {
      total_count: number;
      page: number;
      page_size: number;
      total_pages: number;
      message:any;
      vendors: RawVendorType[];
    };
  };

  export type VendorDetail = {
    id: string;
    first_name: string | null;
    last_name: string | null;
    vendor_name: string;
    parent_company: string;
    phone: string;
    city: string;
    state: string;
    zip: string;
    country?: {
      name: string,
      code:string,
    };
    email_id: string;
    contact: string;
    vendor_address: string | null;
    vendor_code?: string;
  };
  