export type ListConfirmedShippingInsType = {
  name: string | null;
  shipment_type: string | null;
  booking_status: string | null;
  creation: string;
  modified: string;
  carrier_booking_number: string | null;
  shipper_reference_number: string | null;
  forwarder: string | null;
  bl_no: string | null;
  vessel: string | null;
  voyage: string | null;
  port_of_load: string | null;
  consignee_name: string | null;
  port_of_discharge: string | null;
  custom_shipper_name: string | null;
  carrier: string | null;
  owner: string | null;
  port_of_load_name: string | null;
  port_of_discharge_name: string | null;
  carrier_part_name: string | null;
};

export type ShippingInstructionDetailsType = {
  countries_visited_in_between: string;
  eori: string;
  ics2_parties: {
    address: null | string;
    city: null | string;
    contact_name: null | string;
    country: null | string;
    name: null | string;
    party_id: null;
    party_name: null | string;
    party_type: "Shipper" | "Consignee";
    po_box: null | string;
    postal_code: null | string;
    selected_party: null | string;
    state: null | string;
    street_name: null | string;
    street_number: null | string;
    tax_id_eori: null | string;
    telephone_number: null | string;
  }[];
  place_of_acceptance_origin_of_goods: string;
  place_of_final_delivery: string;
  place_of_acceptance_origin_of_goods_data: {
    coordinates: null | string;
    country: null | string;
    country_code: null | string;
    date: null | string;
    function: null | string;
    location_name: null | string;
    locode: null | string;
    name: null | string;
    status: null | string;
    sub_division: null | string;
  };
  place_of_final_delivery_data: {
    coordinates: null | string;
    country: null | string;
    country_code: null | string;
    date: null | string;
    function: null | string;
    location_name: null | string;
    locode: null | string;
    name: null | string;
    status: null | string;
    sub_division: null | string;
  };
  to_order_indicator: number;
  method_of_payment: string;
  name: string;
  modified_by: string;
  bookingnumber: string | null;
  creationdate: string | null;
  booking_request_id: string | null;
  contact_name: string | null;
  vessel: string | null;
  move_type: string | null;
  total_shipment_weight: string | null;
  currency_type_id: string;
  currency_type_data: {
    name: string;
    currency_type: string;
    country: string;
    alphabetic_code: string;
    numeric_code: string;
  };
  shipper_email: string | null;
  forwarder: string | null;
  forwarder_address: string | null;
  carrier_booking_number: string | null;
  bl_reference_number: string | null;
  transaction_number: string | null;
  contract_reference_number: string;
  consignee_order_number: string;
  invoice_reference_number: string;
  customs_house_broker_reference: string;
  government_reference_or_fmc_number: string;
  exporter_reference_number: string;
  are_the_goods_going_to_be_delivered_to_these_countries: string;
  do_you_prefer_the_carrier_to_declare_the_entry_summary_ens: string;
  have_house_bills_been_issued_for_this_shipment: string;
  pcin: string | null;
  csn: string | null;
  acid_number_mcin: string | null;
  contract_party: string | null;
  contract_party_address: string | null;
  freight_payer: string | null;
  freight_payer_address: string | null;
  manufacturersupplier: string | null;
  manufacturersupplier_address: string | null;
  consolidatorstuffer: string | null;
  consolidatorstuffer_address: string | null;
  importer: string | null;
  importer_address: string | null;
  warehouse_keeper: string | null;
  warehouse_keeper_address: string | null;
  shipment_type: string | null;
  voyage: string | null;
  imo_number: string | null;
  print_on_bl_asorigin: string | null;
  print_on_bl_aspol: string | null;
  print_on_bl_aspod: string | null;
  print_on_bl_asdestination: string | null;
  total_number_of_containers: string | null;
  total_number_of_packages: string | null;
  total_shipment_volume: string | null;
  shippers__declared_value: string | null;
  bl_release_office__location: string | null;
  print_on_bl_aslocation: string | null;
  requested_date_of_issue: string | null;
  letter_of_credit_reference: string | null;
  export_license_number: string | null;
  unfreightedno_of_documents: string | null;
  freightedno_of_documents: string | null;
  non_negotiable_freightedno_of_copies: string | null;
  non_negotiable_unfreighted_no_of_copies: string | null;
  bl_comments: string | null;
  si_requestor_emails: string | null;
  house_bill_number: string | null;
  partner_notification_emails: string | null;
  shipper_tax_id: string | null;
  consignee_tax_id: string | null;
  notify_party_tax_id: string | null;
  shipper_reference_number: string | null;
  forwarder_reference_number: string | null;
  unique_consignment_reference: string | null;
  purchase_order_number: string | null;
  documentation_clauses: string | null;
  lcr_issue_date: string | null;
  lcr_expiry_date: string | null;
  eln_issue_date: string | null;
  eln_expiry_date: string | null;
  date_cjmt: string | null;
  carrier_email: string;
  bill_type: string;
  shipper_data: {
    name: string;
    shipper_name: string | null;
    shipper_code: string | null;
    inttra_company_id: string | null;
    email: string | null;
    phone: string | null;
    postal_code: string | null;
    custom_address: string | null;
    fax: string | null;
    country: string | null;
  };

  carrier_data: {
    partyname1: string | null;
    name: string;
    partyalias: string;
    address: string;
    inttra_id: string;
    postal_code: string;
    country_code: string;
    doctype: string;
  };

  consignee_data: {
    name: string;
    customer_name: string | null;
    customer_city: string | null;
    customer_state: string | null;
    customer_zip: string | null;
    customer_country: {
      country_name: string | null;
    };
    phone: string | null;
    email_id: string | null;
    customer_address: string | null;
    fax: string | null;
    contact: string | null;
  };

  origin_place_of_carrier_receipt_data: {
    name: string;
    locode: string;
    country_code: string;
    country: string;
    location_name: string;
    sub_division: string;
  };

  notify_party_data: {
    name: string;
    customer_address: string | null;
    postal_code: string | null;
    customer_name: string | null;
    phone: string | null;
    email: string | null;
    fax: string | null;
  };

  additional_notify_party_1_id: string;
  additional_notify_party_1_data: {
    name: string;
    first_name: string;
    last_name: string;
    customer_name: string;
    parent_company: string;
    customer_city: string;
    customer_state: string;
    customer_zip: string;
    customer_country: string;
    contact: string;
    phone: string;
    inttra_company_id: string | null;
    customer_address: string;
    email_id: string;
    company_name: string;
  };
  additional_notify_party_2_id: string;
  additional_notify_party_2_data: {
    name: string;
    customer_name: string;
    parent_company: string | null;
    customer_city: string | null;
    customer_state: string | null;
    customer_zip: string;
    customer_country: string;
    contact: string | null;
    phone: string | null;
    inttra_company_id: string | null;
    customer_address: string;
    email_id: string;
    company_name: string;
  };

  port_of_load_data: {
    name: string;
    locode: string;
    country_code: string;
    country: string;
    location_name: string;
    sub_division: string;
  };

  port_of_discharge_data: {
    name: string;
    locode: string;
    country_code: string;
    country: string;
    location_name: string;
    sub_division: string;
  };

  destination_place_of_carrier_delivery_data: {
    name: string;
    locode: string;
    country_code: string;
    country: string;
    location_name: string;
    sub_division: string;
  };

  bl_release_office__location_data: {
    name: string;
    locode: string;
    country_code: string;
    country: string;
    location_name: string;
    sub_division: string;
  };

  print_on_bl_aslocation_data: {
    name: string;
    locode: string;
    country_code: string;
    country: string;
    location_name: string;
    sub_division: string;
  };

  freight_charges: {
    charge_type: string | null;
    freight_term: string | null;
    payer: string | null;
    type: "All Charges" | "Individual Charges";
    payment_location_data: {
      name: string;
      locode: string;
      country_code: string;
      country: string;
      location_name: string;
      sub_division: string;
    };
  }[];

  inttra_status: {
    name: string | null;
    submitted_status: string | null;
    inttra_si: string | null;
    acknowledgment_status: string | null;
    acknowledgment_date_gmt: string | null;
    status_date_gmt: string | null;
    product_channel: string | null;
    shipping_instruction?: string | null;
    si_inttra_status?: string | null;
  };

  equipment_list: {
    container_type_id_data: {
      name: number;
      shortdescription: string;
    };
    name: string | null;
    booking_request: string | null;
    count: string | null;
    equipment_name: string | null;
    code_value: string | null;
    description: string | null;
    comment: string | null;
    response_haulage_details: string | null;
    job: string | null;
    supplier_type: string | null;
    service_type: string | null;
    weight_value: string | null;
    weight_type: string | null;
    si_id: string | null;
    data_mjai: string | null;
    carrier_seal_number: string | null;
    shipper_seal_number: string | null;
    tare_weight: string | null;
    cargo_weight: string | null;
    gross_weight: string | null;
    wood_declaration: string | null;
    container_type_id: string;
    cargo:
      | {
          name: string | null;
          container_number: string | null;
          package_counttype_outermost_id: string | null;
          package_counttype_outermost_data: {
            name: string;
            standard_name: string | null;
            short_name: string | null;
            description: string | null;
            edi_factcode: string | null;
            x12code: string | null;
            xmlcode: string | null;
          };
          package_count: string | null;
          hs_code: string | null;
          origin_of_goods: string | null;
          print_on_bl_as: string;
          ncm_codes: string;
          cargo_description: string | null;
          schedule_b_number: string | null;
          cus_code: string | null;
          cargo_gross_weight: string | null;
          net_weight: number | null;
          net_weight_unit: string | null;
          marks_and_numbers: string | null;
          gross_volume: string | null;
          gross_volume_unit: string | null;
          hs_code_data:
            | {
                name: string | null;
                hs_code: string | null;
                hs_code_description: string | null;
                chapter_code: string | null;
                chapter_description: string | null;
                sub_chapter_code: string | null;
                sub_chapter_description: string | null;
              }[]
            | null;
        }[]
      | null;
  }[];
};

export type IntraStatusType = {
  name: string | null;
  creation: string | null;
  modified: string | null;
  modified_by: string | null;
  owner: string | null;
  docstatus: number;
  idx: number;
  shipping_instruction: string | null;
  submitted_status: string | null;
  inttra_si: string | null;
  acknowledgment_status: string | null;
  acknowledgment_date_gmt: string | null;
  status_date_gmt: string | null;
  product_channel: string | null;
  _user_tags: string | null;
  _comments: string | null;
  _assign: string | null;
  _liked_by: string | null;
};
