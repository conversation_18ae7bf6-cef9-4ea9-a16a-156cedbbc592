import { axiosClient } from "@/lib/axios";

export const fetchDataForEvgm = async (booking_number: string) => {
  try {
    const token = localStorage.getItem("token");
    if (!token) throw new Error("No token found in localStorage");

    const api = `method/westside.westside.doctype.evgm.evgm.featch_data_for_evgm?booking_number=${booking_number}`;
    const response = await axiosClient.get(api, {
      headers: {
        Authorization: `Basic ${token}`,
      },
    });
    return response.data;
  } catch (err) {
    console.error("Error fetching evgm data:", err);
    throw err;
  }
};

export const createEvgm = async (data: Record<string, any>) => {
  try {
    const token = localStorage.getItem("token");
    if (!token) throw new Error("No token found in localStorage");
    const formData = new FormData();

    // Append JSON data as a string
    formData.append("data", JSON.stringify(data));
    const api = `method/westside.westside.doctype.evgm.evgm.create_evgm`;
    const response = await axiosClient.post(api, formData, {
      headers: {
        Authorization: `Basic ${token}`,
      },
    });
    return response.data;
  } catch (err) {
    console.error("Error creating evgm:", err);
    throw err;
  }
};

export const fetchListOfEvgm = async (currentPage:any, searchKey:any) => {
  try {
    const token = localStorage.getItem("token");
    if (!token) throw new Error("No token found in localStorage");

    const api = `method/westside.westside.doctype.evgm.evgm.get_list_of_evgm`;
    const response = await axiosClient.get(api, {
      headers: {
        Authorization: `Basic ${token}`,
      },
      params: { page: currentPage || 1 , search: searchKey || '' },
    });
    return response.data;
  } catch (err) {
    console.error("Error fetching evgm list:", err);
    throw err;
  }
};

export const getEvgmDetails = async (equipment_id: string) => {
  try {
    const token = localStorage.getItem("token");
    if (!token) throw new Error("No token found in localStorage");

    const api = `method/westside.westside.doctype.evgm.evgm.get_evgm_details?equipment_id=${equipment_id}`;
    const response = await axiosClient.get(api, {
      headers: {
        Authorization: `Basic ${token}`,
      },
    });
    return response.data;
  } catch (err) {
    console.error("Error fetching evgm details:", err);
    throw err;
  }
};


export const cancelEvgm = async (equipment_id: string) => {
  try {
    const token = localStorage.getItem("token");
    if (!token) throw new Error("No token found in localStorage");

    const api = `method/westside.westside.doctype.evgm.evgm.cancel_evgm?equipment_id=${equipment_id}`;
    const response = await axiosClient.get(api, {
      headers: {
        Authorization: `Basic ${token}`,
      },
    });
    return response.data;
  } catch (err) {
    console.error("Error cancelling evgm:", err);
    throw err;
  }
};

export const amendEvgm = async (data: Record<string, any>) => {
  try {
    const token = localStorage.getItem("token");
    if (!token) throw new Error("No token found in localStorage");
    const formData = new FormData();

    // Append JSON data as a string
    formData.append("data", JSON.stringify(data));
    const api = `method/westside.westside.doctype.evgm.evgm.amend_evgm`;
    const response = await axiosClient.post(api, formData, {
      headers: {
        Authorization: `Basic ${token}`,
      },
    });
    return response.data;
  } catch (err) {
    console.error("Error amending evgm:", err);
    throw err;
  }
};
