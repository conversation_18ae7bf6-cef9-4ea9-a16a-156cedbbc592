import { axiosClient } from "@/lib/axios";
import { VendorListType } from "@/types/VendorListType";

interface VendorListParams {
  page?: number;
  vendorFilter?: string;
  companyFilter?: string;
  countryFilter?: string;
}

export const fetchVendorList = async ({
  page = 1,
  vendorFilter = "",
  companyFilter = "",
  countryFilter = "",
}: VendorListParams): Promise<{ message: VendorListType }> => {
  try {
    const token = localStorage.getItem("token");
    if (!token) throw new Error("No token found in localStorage");

    const api = `${
      import.meta.env.VITE_BACKEND_URL
    }/api/method/westside.www.Admin.vendor.vendors.get_all_vendors`;

    const response = await axiosClient.get(api, {
      headers: {
        Authorization: `Basic ${token}`, 
      },
      params: {
        search: vendorFilter,
        company: companyFilter,
        country: countryFilter,
        page,
      },
    });  
    return response.data;
  } catch (err) {
    console.error("Error fetching vendor list:", err);
    throw err;
  }
};


export const createVendor = async (data: any): Promise<any> => {
  try {
    const token = localStorage.getItem("token");
    if (!token) throw new Error("No token found in localStorage");

    const api = `${
      import.meta.env.VITE_BACKEND_URL
    }/api/method/westside.www.Admin.vendor.vendors.create_vendor`;

    const response = await axiosClient.post(api, data, {
      headers: {
        Authorization: `Basic ${token}`, 
      },
    });
    
    
    return response.data;

  } catch (err) {
    console.error("Error creating vendor:", err);
    throw err;
    
  }
}

export const editVendor = async ({
  formData,
  vendor_id,
}: {
  formData: Record<string, any>;
  vendor_id: string;
}) => {
  try {
    const token = localStorage.getItem("token");
    if (!token) throw new Error("No token found in localStorage");

    const data = new FormData();

    // Append JSON data as a string
    data.append("data", JSON.stringify(formData));

    const api = `${
      import.meta.env.VITE_BACKEND_URL
    }/api/method/westside.www.Admin.vendor.vendors.update_vendor?vendor_id=${vendor_id}`;

    const response = await axiosClient.post(api, data, {
      headers: {
        Authorization: `Basic ${token}`, 
      },
    });
    
    
    return response.data;

  } catch (err) {
    console.error("Error editing vendor:", err);
    throw err;
    
  }
}
export const getVendorById = async (vendor_id: string) => {
  try {
    const token = localStorage.getItem("token");
    if (!token) throw new Error("No token found in localStorage");

    const api = `/method/westside.www.Admin.vendor.vendors.get_vendor_details?vendor_id=${vendor_id}`;
    const response = await axiosClient.get(api, {
      headers: {
        Authorization: `Basic ${token}`,
      },
    });
    return response.data;
  } catch (err) {
    console.error("Error fetching vendor details:", err);
    throw err;
  }
};

export const deleteVendorById = async (
  vendor_id: string,
) => {
  try {
    const token = localStorage.getItem("token");
    if (!token) throw new Error("No token found in localStorage");

    const api = `/method/westside.www.Admin.vendor.vendors.delete_vendor`;
    const response = await axiosClient.delete(api, {
      headers: {
        Authorization: `Basic ${token}`,
      },
      params: {
        vendor_id: vendor_id,
       },
    });
    return response.data;
  } catch (err) {
    console.error("Error deleting vendor details:", err);
    throw err;
  }
};

export const vendorCountryList = async () => {
  try {
    const token = localStorage.getItem("token");
    if (!token) throw new Error("No token found in localStorage");

    const api = `method/westside.www.Admin.vendor.vendors.get_all_countries`;
    const response = await axiosClient.get(api, {
      headers: {
        Authorization: `Basic ${token}`,
      },
      
    });
    return response.data;
  } catch (err) {
    console.error("Error fetching vendor country list:", err);
    throw err;
  }
};

export const vendorParentCompanyList = async () => {
  try {
    const token = localStorage.getItem("token");
    if (!token) throw new Error("No token found in localStorage");

    const api = `method/westside.www.Admin.vendor.vendors.get_parent_companies`;
    const response = await axiosClient.get(api, {
      headers: {
        Authorization: `Basic ${token}`,
      },
      
    });
    return response.data;
  } catch (err) {
    console.error("Error fetching vendor company list:", err);
    throw err;
  }
};

export const addPassKey = async () => {
  try {
    const token = localStorage.getItem("token");
    if (!token) throw new Error("No token found in localStorage");

    const api = `/method/westside.www.Admin.vendor.vendors.add_passkey`;
    const response = await axiosClient.get(api, {
      headers: {
        Authorization: `Basic ${token}`,
      },
    });
    return response.data;
  } catch (err) {
    console.error("Error in generating pass key:", err);
    throw err;
  }
};
