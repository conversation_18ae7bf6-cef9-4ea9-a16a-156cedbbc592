import { axiosClient } from "@/lib/axios";

export const getVendorDetails = async () => {
  try {
    const token = localStorage.getItem("token");
    const api = `${
      import.meta.env.VITE_BACKEND_URL
    }/api/method/westside.www.Vendor.vendor.vendor_detail.get_vendor_details`;

    const response = await axiosClient.get(api, {
      headers: {
        Authorization: `Basic ${token}`,
      },
    });

    return response?.data;
  } catch (error) {
    console.error(error.response?.data || error.message);
    throw error;
  }
};
export const addPassKey = async () => {
  try {
    const token = localStorage.getItem("token");
    if (!token) throw new Error("No token found in localStorage");

    const api = `/method/westside.www.Admin.vendor.vendors.add_passkey`;
    const response = await axiosClient.get(api, {
      headers: {
        Authorization: `Basic ${token}`,
      },
    });
    return response.data;
  } catch (err) {
    console.error("Error in generating pass key:", err);
    throw err;
  }
};

export const removePassKey = async (passKey: string) => {
  try {
    const token = localStorage.getItem("token");
    if (!token) throw new Error("No token found in localStorage");

    const api = `/method/westside.www.Admin.vendor.vendors.remove_passkey`;
    const response = await axiosClient.delete(api, {
      headers: {
        Authorization: `Basic ${token}`,
      },
      params: { passkey: passKey },
    });
    return response.data;
  } catch (err) {
    console.error("Error in removing pass key:", err);
    throw err;
  }
};

export const regeneratePassKey = async () => {
  try {
    const token = localStorage.getItem("token");
    if (!token) throw new Error("No token found in localStorage");

    const api = `/method/westside.www.Admin.vendor.vendors.regenerate_all_passkeys`;
    const response = await axiosClient.delete(api, {
      headers: {
        Authorization: `Basic ${token}`,
      },
    });
    return response.data;
  } catch (err) {
    console.error("Error in removing pass key:", err);
    throw err;
  }
};
