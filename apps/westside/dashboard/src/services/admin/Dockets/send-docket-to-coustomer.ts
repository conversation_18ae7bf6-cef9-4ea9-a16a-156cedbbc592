import { axiosClient } from "@/lib/axios";
import { DocketRevisionResponse } from "@/types/DocketRevisionType";

export const sendDocketsToCustomer = async (
  docket_id: string,
  payload: any
): Promise<{ message: DocketRevisionResponse }> => {
  try {
    const token = localStorage.getItem("token");
    if (!token) throw new Error("No token found in localStorage");

    const api = `${
      import.meta.env.VITE_BACKEND_URL
    }/api/method/westside.www.Admin.customer.docket.attachments.attachments.send_docket_to_customer`;

    const response = await axiosClient.post(api, payload, { 
      headers: {
        Authorization: `Basic ${token}`,
        "Content-Type": "application/json",
      },
      params: {
        docket_id: docket_id,
      },
    });

    return response.data;
  } catch (error) {
    console.error("Failed to fetch dockets:", error);
    throw error;
  }
};
