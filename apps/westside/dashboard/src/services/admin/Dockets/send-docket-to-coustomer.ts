import { axiosClient } from "@/lib/axios";

export const sendDocketsToCustomer = async (
  docket_id: string,
  payload?: {
    subject?: string;
    message?: string;
    cc?: string;
    attachments?: File[];
  }
): Promise<{ message: { status: number; message: string; docket_id: string } }> => {
  try {
    const token = localStorage.getItem("token");
    if (!token) throw new Error("No token found in localStorage");

    const api = `${
      import.meta.env.VITE_BACKEND_URL
    }/api/method/westside.www.Admin.customer.docket.attachments.attachments.send_docket`;

    // Create FormData if there are files to upload
    let requestData: FormData | any = {};
    let headers: any = {
      Authorization: `Basic ${token}`,
    };

    if (payload?.attachments && payload.attachments.length > 0) {
      // Use FormData for file uploads
      const formData = new FormData();
      if (payload.subject) formData.append('subject', payload.subject);
      if (payload.message) formData.append('message', payload.message);
      if (payload.cc) formData.append('cc', payload.cc);

      payload.attachments.forEach((file) => {
        formData.append(`attachments`, file);
      });

      requestData = formData;
      // Don't set Content-Type for FormData, let browser set it with boundary
    } else {
      // Use JSON for simple data
      requestData = payload || {};
      headers["Content-Type"] = "application/json";
    }

    const response = await axiosClient.post(api, requestData, {
      headers,
      params: {
        docket_id: docket_id,
      },
    });

    return response.data;
  } catch (error) {
    console.error("Failed to send docket:", error);
    throw error;
  }
};
