import { axiosClient } from "@/lib/axios";

export const sendDocketsToCustomer = async (
  docket_id: string,
  payload?: {
    subject?: string;
    message?: string;
    cc?: string;
    attachments?: File[];
  }
): Promise<{ message: { status: number; message: string; docket_id: string } }> => {
  try {
    console.log('Sending docket with:', { docket_id, payload });

    const token = localStorage.getItem("token");
    if (!token) throw new Error("No token found in localStorage");

    const api = `${
      import.meta.env.VITE_BACKEND_URL
    }/api/method/westside.www.Admin.customer.docket.attachments.attachments.send_docket`;

    // Create FormData if there are files to upload
    let requestData: FormData | any = {};
    let headers: any = {
      Authorization: `Basic ${token}`,
    };

    if (payload?.attachments && payload.attachments.length > 0) {
      // Use FormData for file uploads - backend expects specific format
      const formData = new FormData();
      formData.append('docket_id', docket_id);
      if (payload.subject) formData.append('subject', payload.subject);
      if (payload.message) formData.append('message', payload.message);
      if (payload.cc) formData.append('cc', payload.cc);

      // Add attachment count
      formData.append('attachment_count', payload.attachments.length.toString());

      // Add each file with the expected naming convention
      payload.attachments.forEach((file, index) => {
        console.log(`Adding attachment_${index}:`, file.name, file.size, 'bytes');
        formData.append(`attachment_${index}`, file);
      });

      requestData = formData;
      // Don't set Content-Type for FormData, let browser set it with boundary

      console.log('FormData entries:');
      for (let [key, value] of formData.entries()) {
        console.log(key, typeof value === 'object' && value instanceof File ? `File: ${value.name}` : value);
      }
    } else {
      // Use JSON for simple data - always include docket_id
      requestData = {
        docket_id: docket_id,
        subject: payload?.subject || null,
        message: payload?.message || null,
        cc: payload?.cc || null,
        attachments: null
      };
      headers["Content-Type"] = "application/json";
    }

    console.log('Final request data:', requestData);

    const response = await axiosClient.post(api, requestData, {
      headers,
    });

    return response.data;
  } catch (error) {
    console.error("Failed to send docket:", error);
    throw error;
  }
};
