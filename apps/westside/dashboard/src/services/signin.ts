import { axiosClient } from "@/lib/axios";
import { AuthResponseType } from "@/types/AuthResponseType";

interface SignInCredentials {
  username: string;
  password: string;
}

const getNextAuthCsrfToken = (): string | null => {
  const match = document.cookie.match(/(?:^|;\s*)next-auth.csrf-token=([^;]+)/);
  if (match) {
    return decodeURIComponent(match[1]).split("|")[0];
  }
  return null;
};

export const SignIn = async (credentials: SignInCredentials): Promise<AuthResponseType> => {
  try {
    const csrfToken = getNextAuthCsrfToken();
    const api = `/method/westside.www.Authentication.login.login`;
    const response = await axiosClient.post(api, credentials, {
      headers: {
        "X-Frappe-CSRF-Token": csrfToken || "",
        "Content-Type": "application/json",
      },
    });

    const { auth_key, session, roles } = response.data.message;

    if (auth_key && session) {
      localStorage.setItem("token", auth_key);
      localStorage.setItem("role", session);
      localStorage.setItem("roles", JSON.stringify(roles));
    } else {
      throw new Error("Invalid login response format");
    }

    return response.data;
  } catch (err: any) {
    console.error("Error signing in:", err.response?.data || err.message);
    throw new Error(err.response?.data?.message || "Login failed");
  }
};

export const VerifyUser = async (): Promise<{
  message: {
    status_role: number;
    roles: string[];
    status: string;
    user: string;
  };
}> => {
  try {
    const token = localStorage.getItem("token");
    const api = `${import.meta.env.VITE_BACKEND_URL}/api/method/westside.www.Authentication.login.check_token_validity`;

    const response = await axiosClient.get(api, {
      headers: {
        Authorization: `Basic ${token}`,
      },
    });

    return response?.data;
  } catch (err: any) {
    console.error("Error verifying user:", err.response?.data || err.message);
    throw err;
  }
};


export const SignOut = async (): Promise<void> => {
  try {
    const token = localStorage.getItem("token");
    if (!token) throw new Error("No token found in localStorage");

    const api = `${import.meta.env.VITE_BACKEND_URL}/api/method/westside.www.Authentication.login.logout`;

    await axiosClient.get(api, {
      headers: {
        Authorization: `Basic ${token}`,
      },
    });

    localStorage.removeItem("token");
    localStorage.removeItem("role");
  } catch (err: any) {
    console.error("Error signing out:", err.response?.data || err.message);
  }
};

export const generateOtp = async (email: string) => {
  try {
    const api = `${import.meta.env.VITE_BACKEND_URL}/api/method/westside.westside.doctype.password_reset_otp.password_reset_otp.generate_otp`;
    const response = await axiosClient.post(api, { user_id: email });
    return response?.data;
  } catch (err: any) {
    console.error("Error generating OTP:", err.response?.data || err.message);
    throw err;
  }
};

export const verifyOtp = async (email: string, otp: string) => {
  try {
    const api = `${import.meta.env.VITE_BACKEND_URL}/api/method/westside.westside.doctype.password_reset_otp.password_reset_otp.verify_otp`;
    const response = await axiosClient.post(api, { user_id: email, otp: otp });
    return response?.data;
  } catch (err: any) {
    console.error("Error verifying OTP:", err.response?.data || err.message);
    throw err;
  }
};

function xorEncrypt(input: string, key: string): string {
  let output = "";
  for (let i = 0; i < input.length; i++) {
    output += String.fromCharCode(
      input.charCodeAt(i) ^ key.charCodeAt(i % key.length)
    );
  }
  return output;
}

export const resetPassword = async (email: string, newPassword: string) => {
  try {
    const customKey = "D7A933CB5792D523FA1D6591CF152"; // your custom key
    const encrypted = xorEncrypt(newPassword, customKey);
    const encodedPassword = btoa(encrypted); // Base64 encode

    const api = `${import.meta.env.VITE_BACKEND_URL}/api/method/westside.westside.doctype.password_reset_otp.password_reset_otp.reset_password`;
    const response = await axiosClient.post(api, {
      user_id: email,
      new_password: encodedPassword,
    });

    return response?.data;
  } catch (err: any) {
    console.error("Error resetting password:", err.response?.data || err.message);
    throw err;
  }
};




// export const authoriseUser = async (
//   token: string
// ): Promise<{
//   message: {
//     status_role: number;
//     roles: string[];
//     status: string;
//     user: string;
//   };
// }> => {
//   try {
//     const api = `${import.meta.env.VITE_BACKEND_URL}/api/method/westside.www.Authentication.login.check_token_validity`;

//     const response = await axiosClient.get(api, {
//       headers: {
//         Authorization: `Basic ${token}`,
//       },
//     });
//     return response?.data;
//   } catch (err: any) {
//     console.error("Error verifying user:", err.response?.data || err.message);
//     throw err;
//   }
// };


