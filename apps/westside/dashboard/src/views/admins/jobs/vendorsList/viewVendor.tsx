// components/CustomerProfilePage.tsx
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  Trash2,
  Phone,
  Building2,
  ChevronLeft,
  ChevronRight,
  Pencil,
  RefreshCw,
  IdCard,
  Plus,
} from "lucide-react";
import { useState, useEffect } from "react";
import UpdateVendorDeatails from "./UpdateVendorDetails";
import { getVendorById, addPassKey } from "@/services/admin/vendorList";
import { getCustomerBookingDetails } from "@/services/admin/adminCustomer";
import { useQuery } from "@tanstack/react-query";
import { useParams } from "react-router-dom";
import { CustomerBookingDetails } from "@/types/customer";
import { VendorDetail } from "@/types/VendorListType";

import { State } from "country-state-city";
import {
  getNames as getCountryNames,
  getCode as getCountryCode,
} from "country-list";
import { formatDateTime } from "@/utils/functions";
import { toast } from "sonner";

function getFlagEmoji(countryCode: string): string {
  return countryCode
    .toUpperCase()
    .replace(/./g, (char) => String.fromCodePoint(char.charCodeAt(0) + 127397));
}

export function VendorProfilePage() {
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [vendor, setVendor] = useState<VendorDetail>();
  const [bookingData, setBookingData] = useState<CustomerBookingDetails[]>();
  const { id } = useParams();

  const [currentPage, setCurrentPage] = useState(1);
  const [selectedVendor, setSelectedVendor] = useState<string | null>(null);

  const {
    data: vendorData,
    isLoading,
    isError,
    error,
    refetch,
  } = useQuery({
    queryKey: ["vendor_id", id],
    queryFn: () => getVendorById(id as string),
    enabled: !!id, // Only run if id exists
  });

  const { data: customerBookingData, refetch: refetchCustomerBookings } =
    useQuery({
      queryKey: ["customer_id", id, currentPage],
      queryFn: () => getCustomerBookingDetails(id as string, currentPage),
      enabled: !!id, // Only run if id exists
    });

  const itemsPerPage = 10;
  const totalCount = customerBookingData?.message?.total_count || 0;
  const totalPages = Math.ceil(totalCount / itemsPerPage);
  useEffect(() => {
    getCustomerBookingDetails(id as string, currentPage);
  }, [currentPage]);

  useEffect(() => {
    if (id) {
      setVendor(vendorData?.message?.data);
    }
  }, [id, vendorData]);
  useEffect(() => {
    if (id) {
      setBookingData(customerBookingData?.message?.data);
    }
  }, [id, customerBookingData]);

  function getStateName(countryCode?: string, stateCode?: string): string {
    if (!countryCode || !stateCode) return "-";
    // const selectedCountryCode = getCountryCode(countryCode);
    const states = State.getStatesOfCountry(countryCode.toUpperCase());
    const state = states.find((s) => s.isoCode === stateCode);
    return state?.name || "-";
  }

  const handleAddPassKey = async () => {
    const confirmAdd = window.confirm(
      "Are you sure you want to add a new pass key?"
    );
    if (!confirmAdd) return;

    try {
      const res = await addPassKey();
      toast.success("Pass key added successfully!");
      refetchCustomerBookings();

      // If vendor.passkeys is in state, update UI
      // setVendor((prev) => ({
      //   ...prev,
      //   passkeys: [...(prev.passkeys || []), res.new_passkey],
      // }));

      console.log("New passkey response:", res);
    } catch (err: any) {
      toast.error(err?.message || "Failed to add pass key");
    }
  };

  const handleRegenerateAllPassKeys = () => {
    console.log("Regenerate all pass keys");
  };

  const handleDeletePassKey = (key: string) => {
    console.log("Delete pass key:", key);
  };

  const handleCloseEditDrawer = () => {
    setDrawerOpen(false);
    // setEditVendorId(null);
    refetch();
  };

  return (
    <>
      <div className="p-6 bg-white shadow-md space-y-6">
        {/* Header */}
        <div className="flex items-start gap-6 border-b pb-6">
          {/* <img
            src={vendorData.imageUrl}
            alt="profile"
            className="w-16 h-16 rounded-full object-cover mt-1"
          /> */}
          <div className="flex-1">
            <h2 className="text-xl font-semibold">
              {vendor?.first_name} {vendor?.last_name}
            </h2>
            <p className="text-sm text-gray-500">{vendor?.email_id}</p>

            {/* Info Grid */}
            <div className="grid grid-cols-4 gap-4 mt-4 text-md text-gray-700">
              <div>
                <p className="text-gray-500 mb-1">Location</p>
                <p>
                  {vendor?.country && vendor?.state
                    ? getStateName(vendor.country?.code, vendor.state)
                    : "-"}
                  , {vendor?.country?.name}
                </p>
                {/* <p>
                  {vendor?.state}, {vendor?.country?.country_name}
                </p> */}
              </div>
              <div>
                <p className="text-gray-500 mb-1">Contact No</p>
                <p className="flex items-center gap-2">
                  <Phone className="w-4 h-4" />
                  {vendor?.contact}
                </p>
              </div>
              <div>
                <p className="text-gray-500 mb-1">Parent Company</p>
                <p className="flex items-center gap-2">
                  <Building2 className="w-4 h-4" />
                  {vendor?.parent_company}
                </p>
              </div>
              <div>
                <p className="text-gray-500 mb-1">Vendor Code</p>
                <p className="flex items-center gap-2">
                  <IdCard className="w-4 h-4" />
                  {vendor?.vendor_code}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="p-6 bg-white shadow-md space-y-6">
        {/* Tabs */}
        <Tabs defaultValue="profile" className="w-full">
          <div className="flex justify-between items-center border-b border-gray-200">
            <TabsList className="flex w-full max-w-md">
              <TabsTrigger
                value="profile"
                className="flex-1 text-center uppercase text-sm font-medium text-gray-500 data-[state=active]:text-orange-500 data-[state=active]:border-b-2 data-[state=active]:border-orange-500 data-[state=active]:font-semibold rounded-none"
              >
                Profile
              </TabsTrigger>
              {/* <TabsTrigger
                value="booking"
                className="flex-1 text-center uppercase text-sm font-medium text-gray-500 data-[state=active]:text-orange-500 data-[state=active]:border-b-2 data-[state=active]:border-orange-500 data-[state=active]:font-semibold rounded-none"
              >
                Job
              </TabsTrigger>
              <TabsTrigger
                value="transaction"
                className="flex-1 text-center uppercase text-sm font-medium text-gray-500 data-[state=active]:text-orange-500 data-[state=active]:border-b-2 data-[state=active]:border-orange-500 data-[state=active]:font-semibold rounded-none"
              >
                Bill
              </TabsTrigger> */}
            </TabsList>
            {/* <Button
              variant="outline"
              className="ml-4 whitespace-nowrap"
              onClick={() => {
                setDrawerOpen(true);
                setSelectedVendor(String(vendor?.id));
              }}
            >
              Edit
            </Button> */}
          </div>

          <TabsContent value="profile" className="pt-4">
            <div className="flex items-center justify-between mb-1">
              <div className="flex-1" />
              <Button
                variant="outline"
                className="ml-4 whitespace-nowrap float-right"
                onClick={() => {
                  setDrawerOpen(true);
                  setSelectedVendor(String(vendor?.id));
                }}
              >
                <Pencil className="w-4 h-4" />
                Edit
              </Button>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-md text-gray-800">
              <div>
                <strong>First Name</strong>
                <div>{vendor?.first_name}</div>
              </div>
              <div>
                <strong>Last Name</strong>
                <div>{vendor?.last_name}</div>
              </div>
              <div>
                <strong>Email ID</strong>
                <div>{vendor?.email_id}</div>
              </div>
              <div>
                <strong>Vendor Name</strong>
                <div>{vendor?.vendor_name}</div>
              </div>
              <div>
                <strong>Phone No</strong>
                <div>{vendor?.phone}</div>
              </div>

              <div>
                <strong>Contact</strong>
                <div>{vendor?.contact}</div>
              </div>
              <div>
                <strong>Parent Company</strong>
                <div>{vendor?.parent_company}</div>
              </div>

              {/* New address-related fields */}
              <div>
                <strong>Address</strong>
                <div>{vendor?.vendor_address}</div>
              </div>
              {/* <div>
                <strong>Address Type</strong>
                <div>{vendorData.addressType}</div>
              </div>
              <div>
                <strong>Address Line 1</strong>
                <div>{vendorData.addressLine1}</div>
              </div>
              <div>
                <strong>Address Line 2</strong>
                <div>{vendorData.addressLine2}</div>
              </div>
              <div>
                <strong>Postal Code</strong>
                <div>{vendorData.postal}</div>
              </div> */}
              <div>
                <strong>City</strong>
                <div>{vendor?.city}</div>
              </div>
              <div>
                <strong>State</strong>
                <div>
                  {" "}
                  {vendor?.state && vendor?.state
                    ? getStateName(vendor.country?.code, vendor.state)
                    : "-"}
                </div>
                {/* <div> {vendor?.state ? vendor?.state : "-"}</div> */}
              </div>
              <div>
                <strong>Country</strong>
                <div>{vendor?.country?.country_name}</div>
              </div>
              <div>
                <strong>Zip code</strong>
                <div>{vendor?.zip}</div>
              </div>

              {/* <div className="md:col-span-3">
                <strong>Address</strong>
                <div>{vendorData.address}</div>
              </div> */}
            </div>

            {/* Pass Key Section */}
            <div className="mt-6 border rounded-lg shadow-sm bg-white w-1/2">
              {/* Header with actions */}
              <div className="flex items-center justify-between px-4 py-3 border-b">
                <h3 className="text-lg font-semibold text-gray-900">
                  Pass Key Details
                </h3>
                {/* <div className="flex gap-2">
      <button
        onClick={handleAddPassKey}
        className="flex items-center gap-1 px-3 py-1.5 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
      >
        <Plus className="w-4 h-4" />
        Add Pass Key
      </button>
      <button
        onClick={handleRegenerateAllPassKeys}
        className="flex items-center gap-1 px-3 py-1.5 text-sm bg-orange-600 text-white rounded hover:bg-orange-700"
      >
        <RefreshCw className="w-4 h-4" />
        Regenerate All
      </button>
    </div> */}
              </div>

              {/* List of passkeys */}
              <div className="p-4">
                {vendor?.passkeys?.length ? (
                  <ul className="space-y-2">
                    {vendor.passkeys.map((key: string, index: number) => (
                      <li
                        key={index}
                        className="flex items-center justify-between bg-gray-50 px-3 py-2 rounded border text-gray-700"
                      >
                        <span className="break-all">{key}</span>
                        {/* <button
              onClick={() => handleDeletePassKey(key)}
              className="text-red-600 hover:text-red-800 transition-colors"
            >
              <Trash2 className="w-4 h-4" />
            </button> */}
                      </li>
                    ))}
                  </ul>
                ) : (
                  <div className="text-gray-500">No pass keys available</div>
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="booking" className="space-y-4 pt-4">
            {Array.isArray(bookingData) && bookingData.length > 0 ? (
              bookingData.map((booking: any, index: any) => (
                <div
                  key={index}
                  className="border rounded-md p-4 shadow-sm bg-gray-50"
                >
                  <div className="grid grid-cols-6 gap-4 text-sm text-gray-700">
                    {/* Booking ID */}
                    <div>
                      <p className="text-gray-400 mb-1">Booking ID</p>
                      <p>{booking.booking_id}</p>
                    </div>

                    {/* Contract No */}
                    <div>
                      <p className="text-gray-400 mb-1">Contract No</p>
                      <p>
                        {booking.contract_number
                          ? booking.contract_number
                          : "---"}
                      </p>
                    </div>

                    {/* Total No Of Containers */}
                    <div>
                      <p className="text-gray-400 mb-1">
                        Total No Of Containers
                      </p>
                      <p>
                        {booking.total_number_of_containers
                          ? booking.total_number_of_containers
                          : "---"}
                      </p>
                    </div>

                    {/* Place Of Carrier Receipt */}
                    <div>
                      <p className="text-gray-400 mb-1">
                        Place Of Carrier Receipt
                      </p>
                      {booking.place_of_carrier_receipt ? (
                        <p>
                          <span className="text-lg mr-1">
                            {getFlagEmoji(
                              booking.place_of_carrier_receipt.country_code
                            )}
                          </span>
                          <span className="font-medium">
                            {booking.place_of_carrier_receipt.location_name},{" "}
                            {booking.place_of_carrier_receipt.country} (
                            {booking.place_of_carrier_receipt.locode})
                          </span>
                          <br />
                          <span className="text-xs text-gray-500">
                            On{" "}
                            {formatDateTime(
                              booking?.carrier_receipt_delivery_date?.etd
                            )}
                          </span>
                        </p>
                      ) : (
                        <p className="text-gray-400">----</p>
                      )}
                    </div>

                    {/* Place Of Carrier Delivery */}
                    <div>
                      <p className="text-gray-400 mb-1">
                        Place Of Carrier Delivery
                      </p>
                      {booking.place_of_carrier_delivery ? (
                        <p>
                          <span className="text-lg mr-1">
                            {getFlagEmoji(
                              booking.place_of_carrier_delivery.country_code
                            )}
                          </span>
                          <span className="font-medium">
                            {booking.place_of_carrier_delivery.location_name},{" "}
                            {booking.place_of_carrier_delivery.country} (
                            {booking.place_of_carrier_delivery.locode})
                          </span>
                          <br />
                          <span className="text-xs text-gray-500">
                            On{" "}
                            {formatDateTime(
                              booking?.carrier_receipt_delivery_date?.eta
                            )}
                          </span>
                        </p>
                      ) : (
                        <p className="text-gray-400">----</p>
                      )}
                    </div>

                    {/* Status */}
                    <div className="flex items-center">
                      <span
                        className={`text-xs px-3 py-1 rounded-full font-medium border ${
                          booking.booking_status === "CONFIRM"
                            ? "bg-green-100 text-green-600"
                            : booking.booking_status === "AMEND"
                            ? "bg-orange-100 text-orange-600"
                            : booking.booking_status === "REQUEST"
                            ? "bg-blue-100 text-blue-600"
                            : booking.booking_status === "CANCEL"
                            ? "bg-gray-100 text-gray-600"
                            : booking.booking_status === "DECLINE"
                            ? "bg-red-100 text-red-600"
                            : booking.booking_status === "REPLACE"
                            ? "bg-yellow-100 text-yellow-700"
                            : booking.booking_status === "FAILED"
                            ? "bg-red-200 text-red-800"
                            : "bg-black text-white"
                        }`}
                      >
                        {booking.booking_status}
                      </span>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center text-sm text-gray-500 pt-4">
                No job details available
              </div>
            )}
            <div className="flex items-center justify-between mt-6">
              <div className="flex items-center gap-2 mx-auto sm:mx-0 sm:ml-auto">
                <Button
                  className="rounded-lg px-3 py-2"
                  variant="outline"
                  onClick={() =>
                    setCurrentPage((prev) => Math.max(prev - 1, 1))
                  }
                  disabled={currentPage === 1}
                  size="sm"
                >
                  <ChevronLeft className="text-black h-4 w-4" />
                </Button>
                <div className="flex gap-1">
                  {Array.from({ length: Math.min(totalPages, 5) }).map(
                    (_, index) => {
                      let page;
                      if (totalPages <= 5) {
                        page = index + 1;
                      } else if (currentPage <= 3) {
                        page = index + 1;
                      } else if (currentPage >= totalPages - 2) {
                        page = totalPages - 4 + index;
                      } else {
                        page = currentPage - 2 + index;
                      }

                      return (
                        <Button
                          key={page}
                          onClick={() => setCurrentPage(page)}
                          className={`rounded-lg px-3 py-2 ${
                            page === currentPage
                              ? "bg-white text-black border"
                              : "text-gray-500"
                          }`}
                          variant="outline"
                          size="sm"
                        >
                          {page}
                        </Button>
                      );
                    }
                  )}
                  {totalPages > 5 && currentPage < totalPages - 2 && (
                    <>
                      <span className="flex items-center px-2">...</span>
                      <Button
                        onClick={() => setCurrentPage(totalPages)}
                        className={`rounded-lg px-3 py-2 ${
                          totalPages === currentPage
                            ? "bg-white text-black border"
                            : "text-gray-500"
                        }`}
                        variant="outline"
                        size="sm"
                      >
                        {totalPages}
                      </Button>
                    </>
                  )}
                </div>
                <Button
                  className="rounded-lg px-3 py-2"
                  variant="outline"
                  onClick={() =>
                    setCurrentPage((prev) => Math.min(prev + 1, totalPages))
                  }
                  disabled={currentPage === totalPages}
                  size="sm"
                >
                  <ChevronRight className="text-black h-4 w-4" />
                </Button>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="transaction">
            <p className="text-sm text-gray-600 pt-4">
              No bill history available.
            </p>
          </TabsContent>
        </Tabs>
        <UpdateVendorDeatails
          open={drawerOpen}
          onClose={handleCloseEditDrawer}
          vendorId={String(selectedVendor)}
          // refetchListCusomer={refetchCustomerListData}
        />
        {/* Edit Button */}
      </div>
    </>
  );
}
