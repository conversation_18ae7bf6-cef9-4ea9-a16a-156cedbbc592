import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi } from 'vitest';
import SendDocketModal from './SendDocketModal';

// Mock the toast function
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

describe('SendDocketModal', () => {
  const defaultProps = {
    open: true,
    onClose: vi.fn(),
    docketId: 'test-docket-123',
    defaultSubject: 'Test Subject',
    defaultMessage: 'Test Message',
    defaultCc: '<EMAIL>',
    defaultAttachments: [{ name: 'test.pdf', url: '/test.pdf' }],
    onSend: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders modal with prefilled values', () => {
    render(<SendDocketModal {...defaultProps} />);
    
    expect(screen.getByDisplayValue('Test Subject')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Test Message')).toBeInTheDocument();
    expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('📎 test.pdf')).toBeInTheDocument();
  });

  it('allows user to edit subject and message', () => {
    render(<SendDocketModal {...defaultProps} />);
    
    const subjectInput = screen.getByDisplayValue('Test Subject');
    const messageTextarea = screen.getByDisplayValue('Test Message');
    
    fireEvent.change(subjectInput, { target: { value: 'Updated Subject' } });
    fireEvent.change(messageTextarea, { target: { value: 'Updated Message' } });
    
    expect(screen.getByDisplayValue('Updated Subject')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Updated Message')).toBeInTheDocument();
  });

  it('calls onSend with correct payload when Send button is clicked', async () => {
    const mockOnSend = vi.fn().mockResolvedValue(undefined);
    
    render(<SendDocketModal {...defaultProps} onSend={mockOnSend} />);
    
    const sendButton = screen.getByText('Send');
    fireEvent.click(sendButton);
    
    await waitFor(() => {
      expect(mockOnSend).toHaveBeenCalledWith({
        subject: 'Test Subject',
        message: 'Test Message',
        cc: '<EMAIL>',
        attachments: [],
      });
    });
  });

  it('calls onClose when Cancel button is clicked', () => {
    const mockOnClose = vi.fn();
    
    render(<SendDocketModal {...defaultProps} onClose={mockOnClose} />);
    
    const cancelButton = screen.getByText('Cancel');
    fireEvent.click(cancelButton);
    
    expect(mockOnClose).toHaveBeenCalled();
  });

  it('shows loading state when sending', async () => {
    const mockOnSend = vi.fn().mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));
    
    render(<SendDocketModal {...defaultProps} onSend={mockOnSend} />);
    
    const sendButton = screen.getByText('Send');
    fireEvent.click(sendButton);
    
    expect(screen.getByText('Sending...')).toBeInTheDocument();
    
    await waitFor(() => {
      expect(screen.getByText('Send')).toBeInTheDocument();
    });
  });
});
