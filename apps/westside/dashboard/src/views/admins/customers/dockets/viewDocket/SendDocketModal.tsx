import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";

interface SendDocketModalProps {
  open: boolean;
  onClose: () => void;
  docketId: string;
  defaultSubject: string;
  defaultMessage: string;
  defaultCc?: string;
  defaultAttachments?: { name: string; url: string }[];
  onSend: (payload: {
    subject: string;
    message: string;
    cc?: string;
    attachments?: File[];
  }) => Promise<void>;
}

export default function SendDocketModal({
  open,
  onClose,
  docketId,
  defaultSubject,
  defaultMessage,
  defaultCc,
  defaultAttachments = [],
  onSend,
}: SendDocketModalProps) {
  const [subject, setSubject] = useState(defaultSubject);
  const [message, setMessage] = useState(defaultMessage);
  const [cc, setCc] = useState(defaultCc || "");
  const [attachments, setAttachments] = useState<File[]>([]);
  const [isSending, setIsSending] = useState(false);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      setAttachments(Array.from(e.target.files));
    }
  };

  const handleSend = async () => {
    setIsSending(true);
    try {
      await onSend({ subject, message, cc, attachments });
      toast.success("Docket email sent successfully");
      onClose();
    } catch (err) {
      console.error(err);
      toast.error("Failed to send email");
    } finally {
      setIsSending(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Send Docket Email</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <Input
            placeholder="CC Email(s)"
            value={cc}
            onChange={(e) => setCc(e.target.value)}
          />
          <Input
            placeholder="Subject"
            value={subject}
            onChange={(e) => setSubject(e.target.value)}
          />
          <Textarea
            rows={6}
            value={message}
            onChange={(e) => setMessage(e.target.value)}
          />

          <div>
            <label className="block text-sm mb-1">Attachments</label>
            <input type="file" multiple onChange={handleFileChange} />
            <ul className="mt-2 text-sm text-gray-600">
              {defaultAttachments.map((att, idx) => (
                <li key={idx}>📎 {att.name}</li>
              ))}
              {attachments.map((file, idx) => (
                <li key={idx}>📎 {file.name}</li>
              ))}
            </ul>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button disabled={isSending} onClick={handleSend}>
            {isSending ? "Sending..." : "Send"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
