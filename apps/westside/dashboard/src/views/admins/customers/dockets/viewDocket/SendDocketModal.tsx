import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { FileText, Upload, X, Mail, Users, MessageSquare, Paperclip } from "lucide-react";

interface SendDocketModalProps {
  open: boolean;
  onClose: () => void;
  docketId: string;
  customerEmail: string;
  customerName: string;
  carrierBooking: string;
  blno: string;
  revisionNumber: string;
  existingAttachments: { name: string; url: string }[];
  onSend: (payload: {
    subject?: string;
    message?: string;
    cc?: string;
    attachments?: File[];
  }) => Promise<void>;
}

export default function SendDocketModal({
  open,
  onClose,
  docketId,
  customerEmail,
  customerName,
  carrierBooking,
  blno,
  revisionNumber,
  existingAttachments = [],
  onSend,
}: SendDocketModalProps) {
  // Generate default values based on backend logic
  const defaultSubject = `Shipment:${carrierBooking}  Bol: ${blno}  Docket: ${docketId}`;
  const defaultMessage = `Dear ${customerName},

We are pleased to inform you that a new docket revision Rev${revisionNumber} has been created for your booking ${carrierBooking}.

Please find the relevant documents attached with this email for your review.

Documents attached:
${existingAttachments.map(att => `• ${att.name}`).join('\n')}

If you have any questions or require further clarification, feel free to reach out.

Please review your documents, login to your customer portal account and approve/reject the docket.

Thanks,

Docs Team
Westside Exports LLC
4017 Vallonia Dr
Cary, NC 27519
Email: <EMAIL>`;

  const [useCustomSubject, setUseCustomSubject] = useState(false);
  const [useCustomMessage, setUseCustomMessage] = useState(false);
  const [subject, setSubject] = useState(defaultSubject);
  const [message, setMessage] = useState(defaultMessage);
  const [cc, setCc] = useState("");
  const [additionalAttachments, setAdditionalAttachments] = useState<File[]>([]);
  const [isSending, setIsSending] = useState(false);

  // Reset form when modal opens
  useEffect(() => {
    if (open) {
      setUseCustomSubject(false);
      setUseCustomMessage(false);
      setSubject(defaultSubject);
      setMessage(defaultMessage);
      setCc("");
      setAdditionalAttachments([]);
    }
  }, [open, defaultSubject, defaultMessage]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      setAdditionalAttachments(Array.from(e.target.files));
    }
  };

  const removeAttachment = (index: number) => {
    setAdditionalAttachments(prev => prev.filter((_, i) => i !== index));
  };

  const handleSend = async () => {
    setIsSending(true);
    try {
      const payload: any = {};

      // Only send custom values if user has modified them
      if (useCustomSubject && subject !== defaultSubject) {
        payload.subject = subject;
      }
      if (useCustomMessage && message !== defaultMessage) {
        payload.message = message;
      }
      if (cc.trim()) {
        payload.cc = cc;
      }
      if (additionalAttachments.length > 0) {
        payload.attachments = additionalAttachments;
      }

      console.log('Modal sending payload:', payload);
      await onSend(payload);
      toast.success("Docket email sent successfully");
      onClose();
    } catch (err) {
      console.error('Modal send error:', err);
      toast.error("Failed to send email");
    } finally {
      setIsSending(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-9xl w-[95vw] max-h-[90vh] overflow-y-auto">
        <DialogHeader className="pb-4">
          <DialogTitle className="flex items-center gap-2 text-xl">
            <Mail className="w-8 h-8 text-blue-600" />
            Send Docket Email
          </DialogTitle>
          <p className="text-sm text-gray-600">
            Sending to: <span className="font-medium">{customerEmail}</span> • Docket: <span className="font-medium">{docketId}</span>
          </p>
        </DialogHeader>

        <div className="space-y-6">
          {/* Recipient & CC Section */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2 mb-3">
                <Users className="w-4 h-4 text-gray-600" />
                <Label className="text-sm font-medium">Recipients</Label>
              </div>
              <div className="space-y-3">
                <div>
                  <Label className="text-xs text-gray-500">To (Primary)</Label>
                  <div className="mt-1 p-3 bg-blue-50 rounded border border-blue-200 text-sm">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <span className="text-blue-800 font-medium">{customerName}</span>
                    </div>
                    <div className="text-xs text-blue-600 mt-1 ml-4">
                      Will be sent to customer's registered notification email
                    </div>
                  </div>
                </div>
                <div>
                  <Label className="text-xs text-gray-500">CC (Optional)</Label>
                  <Input
                    placeholder="Additional email addresses (comma separated)"
                    value={cc}
                    onChange={(e) => setCc(e.target.value)}
                    className="mt-1"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Subject Section */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <MessageSquare className="w-4 h-4 text-gray-600" />
                  <Label className="text-sm font-medium">Subject</Label>
                </div>
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="customSubject"
                    checked={useCustomSubject}
                    onChange={(e) => setUseCustomSubject(e.target.checked)}
                    className="rounded"
                  />
                  <Label htmlFor="customSubject" className="text-xs text-gray-600">
                    Customize subject
                  </Label>
                </div>
              </div>

              {useCustomSubject ? (
                <Input
                  value={subject}
                  onChange={(e) => setSubject(e.target.value)}
                  className="font-mono text-sm"
                />
              ) : (
                <div className="p-3 bg-gray-50 rounded border text-sm font-mono text-gray-700">
                  {defaultSubject}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Message Section */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <FileText className="w-4 h-4 text-gray-600" />
                  <Label className="text-sm font-medium">Message</Label>
                </div>
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="customMessage"
                    checked={useCustomMessage}
                    onChange={(e) => setUseCustomMessage(e.target.checked)}
                    className="rounded"
                  />
                  <Label htmlFor="customMessage" className="text-xs text-gray-600">
                    Customize message
                  </Label>
                </div>
              </div>

              {useCustomMessage ? (
                <Textarea
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  rows={12}
                  className="font-mono text-sm"
                />
              ) : (
                <div className="p-3 bg-gray-50 rounded border text-sm font-mono text-gray-700 whitespace-pre-line max-h-48 overflow-y-auto">
                  {defaultMessage}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Attachments Section */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2 mb-3">
                <Paperclip className="w-4 h-4 text-gray-600" />
                <Label className="text-sm font-medium">Attachments</Label>
              </div>

              {/* Existing Attachments */}
              <div className="mb-4">
                <Label className="text-xs text-gray-500 mb-2 block">System Documents (Auto-included)</Label>
                <div className="grid grid-cols-2 gap-2">
                  {existingAttachments.map((att, idx) => (
                    <div key={idx} className="flex items-center gap-2 p-2 bg-blue-50 rounded border border-blue-200">
                      <FileText className="w-4 h-4 text-blue-600" />
                      <span className="text-sm text-blue-800 truncate">{att.name}</span>
                      <Badge variant="secondary" className="text-xs">System</Badge>
                    </div>
                  ))}
                </div>
              </div>

              {/* Additional Attachments */}
              <div>
                <Label className="text-xs text-gray-500 mb-2 block">Additional Files (Optional)</Label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                  <input
                    type="file"
                    multiple
                    onChange={handleFileChange}
                    className="hidden"
                    id="additional-files"
                  />
                  <label htmlFor="additional-files" className="cursor-pointer">
                    <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-sm text-gray-600">Click to upload additional files</p>
                    <p className="text-xs text-gray-400">Multiple files supported</p>
                  </label>
                </div>

                {additionalAttachments.length > 0 && (
                  <div className="mt-3 space-y-2">
                    {additionalAttachments.map((file, idx) => (
                      <div key={idx} className="flex items-center justify-between p-2 bg-green-50 rounded border border-green-200">
                        <div className="flex items-center gap-2">
                          <FileText className="w-4 h-4 text-green-600" />
                          <span className="text-sm text-green-800">{file.name}</span>
                          <Badge variant="outline" className="text-xs">Additional</Badge>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeAttachment(idx)}
                          className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                        >
                          <X className="w-3 h-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        <DialogFooter className="pt-6">
          <Button variant="outline" onClick={onClose} disabled={isSending}>
            Cancel
          </Button>
          <Button onClick={handleSend} disabled={isSending} className="min-w-24">
            {isSending ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Sending...
              </>
            ) : (
              <>
                <Mail className="w-4 h-4 mr-2" />
                Send Email
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
