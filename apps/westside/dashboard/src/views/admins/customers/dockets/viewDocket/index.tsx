import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { useState, useRef, useEffect, useCallback } from "react";
import {
  PackageIcon,
  Pencil,
  MessageSquareCode,
  FileText,
  Eye,
  Plus,
  Minus,
  Trash2,
  AlertTriangle,
} from "lucide-react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { RightDrawer } from "../editFileInformation";
import CommentDrawer from "../commentDrawer";
import {
  fetchDocketDetails,
  updateDocketStatus,
} from "@/services/admin/Dockets/docket-Details-View";
import {
  Navigate,
  useParams,
  useNavigate,
  useSearchParams,
} from "react-router-dom";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import Loader from "@/components/Loader";
import {
  fetchDocketsAttachments,
  updateDocketRevision,
  deleteAdditionalAttachment,
} from "@/services/admin/Dockets/docket-Revision-Data";
import PDF from "../../../../../assets/img/pdf.svg";
import ImageFile from "../../../../../assets/img/imageFile.svg";
import {
  CreateDocuments,
  reGenerateDocuments,
} from "@/services/admin/Dockets/create-Documents";
import { sendDocketsToCustomer } from "@/services/admin/Dockets/send-docket-to-coustomer";
import { fetchDocketbasedContainers } from "@/services/admin/Dockets/create-docket";
import { Card, CardContent } from "@/components/ui/card";
import { format } from "date-fns";
import SendDocketModal from "./SendDocketModal";

interface ContainerData {
  containerNo: string;
  sealNo: string;
  netWeight: string;
  equipmentId: string;
}


export function DocketView() {
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [drawerMode, setDrawerMode] = useState<"edit" | "create">("edit");
  const [invoiceMode, setInvoiceMode] = useState<"edit" | "create">("edit");
  const [invoiceDrawerOpen, setInvoiceDrawerOpen] = useState(false);
  const [commentDrawerOpen, setCommentDrawerOpen] = useState(false);
  const [selectedRevision, setSelectedRevision] = useState("");
  const [selectedRevisionNumber, setSelectedRevisionNumber] = useState("");
  const [isCreatingDocs, setIsCreatingDocs] = useState(false);
  const [isSendingDocket, setIsSendingDocket] = useState(false);
  const [isDeletingFile, setIsDeletingFile] = useState(false);
  const [fileInputs, setFileInputs] = useState<
    { id: number; fileName?: string }[]
  >([{ id: 1 }]);
  const [hasSentDocket, setHasSentDocket] = useState(false);
  const [isReopenMode, setIsReopenMode] = useState(false);
  const fileInputRefs = useRef<Record<number, HTMLInputElement | null>>({});
  const queryClient = useQueryClient();

  const { id } = useParams();
  const [searchParams, setSearchParams] = useSearchParams();
  const params = new URLSearchParams(searchParams);
  const carrierBooking = params.get("carrierBooking");
  const [sendModalOpen, setSendModalOpen] = useState(false);

  const navigate = useNavigate();

  const {
    data: apiData,
    isLoading: isApiLoading,
    isError: isApiError,
    error: apiError,
    refetch: refetchDocketDetails,
  } = useQuery({
    queryKey: ["DocketDetails", id],
    queryFn: () => fetchDocketDetails({ docket_id: String(id) }),
  });

  const {
    data: DocketData,
    isLoading: isDocketLoading,
    isError: isDocketError,
    error: docketError,
    refetch: refetchDocketAttachments,
  } = useQuery({
    queryKey: ["DocketAttachmentDetails", id],
    queryFn: () => fetchDocketsAttachments(String(id)),
  });

  const {
    data: containerListData,
    isLoading: isContainerLoading,
    isError: isContainerError,
    error: containerError,
    refetch: refetchContainers,
  } = useQuery({
    queryKey: ["ContainerListDetails", carrierBooking],
    queryFn: () =>
      fetchDocketbasedContainers({
        carrier_booking_number: String(carrierBooking),
      }),
    enabled: !!carrierBooking,
  });

  const containerList = containerListData?.message?.data?.equipments || [];

  const DocketRevitionDate = DocketData?.message?.revisions || [];

  const [containersData, setContainersData] = useState<ContainerData[]>([]);

  useEffect(() => {
    if (containerList.length > 0) {
      const mappedContainers = containerList.map((container: any) => ({
        containerNo: container.equipment_name || "",
        sealNo: container.shipper_seal_number || "",
        netWeight: container.net_weight ? String(container.net_weight) : "",
        equipmentId: container.name || "",
      }));
      setContainersData(mappedContainers);
    }
  }, [containerList]);

  // Calculate total net weight
  const totalNetWeight = containersData.reduce((total, container) => {
    const weight = parseFloat(container.netWeight) || 0;
    return total + weight;
  }, 0);

  // Check if total weight matches container net weight total
  const dataWeight = parseFloat(apiData?.message?.data?.weight || "0");
  const weightMismatch =
    dataWeight > 0 && Math.abs(totalNetWeight - dataWeight) > 0.01;

  const handleDeleteAdditionalFile = async (
    docket_id: string,
    revisionId: string,
    file_id: string
  ) => {
    setIsDeletingFile(true);
    try {
      const response = await deleteAdditionalAttachment(
        (docket_id = String(id)),
        revisionId,
        file_id
      );
      if (response.message?.status === 200) {
        toast.success("File deleted successfully");
        refetchDocketAttachments();
      } else {
        toast.error("Failed to delete file");
      }
    } catch (error) {
      console.error("Error deleting file:", error);
      toast.error("Failed to delete file");
    } finally {
      setIsDeletingFile(false);
    }
  };

  const handleCreateDocuments = async (id: string, booking: string) => {
    // Check if invoice is empty
    if (!apiData?.message?.data?.invoice) {
      toast.error(
        "Invoice number is not available. Please generate the invoice to create the docket."
      );
      return;
    }

    setIsCreatingDocs(true);
    try {
      const response = await CreateDocuments({
        docket_id: String(id),
        booking_id: String(booking),
      });

      // Handle different response statuses
      if (response.message?.status === 200) {
        toast.success("Documents created successfully.");
        await Promise.all([refetchDocketDetails(), refetchDocketAttachments()]);
      } else if (response.message?.status === 400) {
        // Show the specific error message from the response
        const errorMessage = response.message?.message;
        toast.error(errorMessage);
      } else if (response.message?.status === 500) {
        toast.error("Server error. Please try again later.");
      } else {
        // Handle other status codes
        toast.error("Failed to create documents. Please try again.");
      }
    } catch (error) {
      console.error("Failed to create documents:", error);

      // More specific error handling based on error type
      if (error instanceof Error) {
        toast.error(`Failed to create documents: ${error.message}`);
      } else {
        toast.error("Failed to create documents. Please try again.");
      }
    } finally {
      setIsCreatingDocs(false);
    }
  };

  const handleReGenerateDocuments = async (id: string, booking: string) => {
    toast.custom((t) => (
      <div className="bg-white p-4 rounded-md shadow-lg border border-gray-200 w-full max-w-md">
        <h3 className="font-medium text-gray-800 mb-2">Confirm Regeneration</h3>
        <p className="text-sm text-gray-600 mb-4">
          Are you sure you want to Regenerate the documents?
        </p>
        <div className="flex justify-end gap-2">
          <Button
            variant="outline"
            className="text-sm px-3 py-1 h-8"
            onClick={() => toast.dismiss(t)}
          >
            Cancel
          </Button>
          <Button
            variant="default"
            className="text-sm px-3 py-1 h-8"
            onClick={async () => {
              toast.dismiss(t);

              // Check if invoice is empty
              if (!apiData?.message?.data?.invoice) {
                toast.error(
                  "Invoice number is not available. Please generate the invoice to create the docket."
                );
                return;
              }

              setIsCreatingDocs(true);
              try {
                const response = await reGenerateDocuments({
                  docket_id: String(id),
                  booking_id: String(booking),
                });

                if (response.message?.status === 200) {
                  toast.success("Documents Regenerated successfully.");
                  await Promise.all([
                    refetchDocketDetails(),
                    refetchDocketAttachments(),
                  ]);
                } else if (response.message?.status === 500) {
                  toast.error(
                    "Failed to Regenerate documents. Please try again."
                  );
                }
              } catch (error) {
                console.error("Failed to Regenerate documents:", error);
                toast.error(
                  "Failed to Regenerate documents. Please try again."
                );
              } finally {
                setIsCreatingDocs(false);
              }
            }}
          >
            Yes, ReGenerate
          </Button>
        </div>
      </div>
    ));
  };

  const handleStatusChange = async (
    status: "Reopen",
    revisionNumber?: string
  ) => {
    if (!id) {
      toast.error("Docket ID is missing");
      return;
    }

    try {
      const response = await updateDocketStatus({
        docketId: id,
        status: status,
        revision_id: revisionNumber ?? "",
      });

      if (response.message?.status === 200) {
        const displayStatus = status === "Reopen" ? "Reopened" : status;
        toast.success(`Docket has been ${displayStatus} successfully`);

        await refetchDocketDetails();
        await refetchDocketAttachments(); // Refresh attachments if needed
      } else {
        const errorMessage =
          response.message?.message ||
          `Failed to ${status.toLowerCase()} docket`;
        toast.error(errorMessage);
      }
    } catch (error: any) {
      console.error(`Error ${status.toLowerCase()}ing docket:`, error);
      const errorMessage =
        error.response?.data?.message ||
        error.message ||
        `Failed to ${status.toLowerCase()} docket`;
      toast.error(errorMessage);
    }
  };

  const handleReopenWithComment = () => {
    // Get the latest revision data
    if (DocketRevitionDate.length > 0) {
      const latestRevision = DocketRevitionDate[0];
      setSelectedRevision(latestRevision.revision_name);
      setSelectedRevisionNumber(latestRevision.revision_number);
      setIsReopenMode(true);
      setCommentDrawerOpen(true);
    }
  };

  const handleReopenComplete = async (revisionNumber?: string) => {
    try {
      await handleStatusChange("Reopen", revisionNumber);
      setIsReopenMode(false);
      setCommentDrawerOpen(false);
    } catch (error) {
      console.error("Error during reopen:", error);
      toast.error("Failed to reopen docket. Please try again.");
    }
  };

  const handleSendDocketWithModal = useCallback(
    async (payload: {
      subject?: string;
      message?: string;
      cc?: string;
      attachments?: File[];
    }) => {
      setIsSendingDocket(true);
      try {
        const response = await sendDocketsToCustomer(String(id), payload);

        if (response?.message?.status === 200) {
          toast.success("Docket sent successfully.");
          setHasSentDocket(true);
          setSendModalOpen(false);
          await Promise.all([
            refetchDocketDetails(),
            refetchDocketAttachments(),
          ]);
        } else if (
          response?.message?.status === 400 &&
          response?.message?.message
        ) {
          toast.error(response?.message.message);
        } else {
          toast.error("Unexpected error occurred. Please try again.");
        }
      } catch (error: any) {
        const apiMessage = error?.response?.data?.message;
        if (apiMessage) {
          toast.error(apiMessage);
        } else {
          toast.error("Failed to send docket. Please try again.");
        }
      } finally {
        setIsSendingDocket(false);
      }
    },
    [id, refetchDocketDetails, refetchDocketAttachments]
  );

  const addFileInput = () => {
    setFileInputs([...fileInputs, { id: Date.now() }]);
  };

  const removeFileInput = (id: number) => {
    if (fileInputs.length > 1) {
      setFileInputs(fileInputs.filter((input) => input.id !== id));
    }
  };

  const handleFileChange = (
    id: number,
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      setFileInputs(
        fileInputs.map((input) =>
          input.id === id ? { ...input, fileName: files[0].name } : input
        )
      );
    }
  };

  const handleUpdateDocketRevision = async (
    revision_id: string,
    docket_id: string
  ) => {
    toast.custom((t) => (
      <div className="bg-white p-4 rounded-md shadow-lg border border-gray-200 w-full max-w-md">
        <h3 className="font-medium text-gray-800 mb-2">Confirm Upload</h3>
        <p className="text-sm text-gray-600 mb-4">
          Are you sure you want to upload these files?
        </p>
        <div className="flex justify-end gap-2">
          <Button
            variant="outline"
            className="text-sm px-3 py-1 h-8"
            onClick={() => toast.dismiss(t)}
          >
            Cancel
          </Button>
          <Button
            variant="default"
            className="text-sm px-3 py-1 h-8"
            onClick={async () => {
              toast.dismiss(t);
              try {
                const files: File[] = [];
                Object.values(fileInputRefs.current).forEach((input) => {
                  if (input?.files?.[0]) {
                    files.push(input.files[0]);
                  }
                });

                if (files.length === 0) {
                  toast.error("Please select at least one file to upload");
                  return;
                }

                const response = await updateDocketRevision(
                  docket_id,
                  revision_id,
                  files
                );

                if (response?.message?.status === 200) {
                  toast.success("Docket updated successfully.");
                  setFileInputs([{ id: 1 }]);
                  fileInputRefs.current = {};
                  await refetchDocketAttachments();
                } else {
                  toast.error(
                    "Failed to update docket revision. Please try again."
                  );
                }
              } catch (error) {
                console.error("Failed to update docket revision:", error);
                toast.error(
                  "Failed to update docket revision. Please try again."
                );
              }
            }}
          >
            Upload
          </Button>
        </div>
      </div>
    ));
  };

  interface CustomerData {
    customer?: string | null;
    docket_id?: string | null;
    booking?: string | null;
    origin?: string | null;
    shipper?: string | null;
    shipper_name?: string | null;
    blno?: string | null;
    consignee?: string | null;
    material?: string | null;
    shipping_date?: string | null;
    invoice?: string | null;
    hs_code?: string | null;
    destination_contact?: string | null;
    destination?: string | null;
    telephone?: string | null;
    terms?: string | null;
    origin_of_goods?: string | null;
    weight?: string | null;
    containers?: string | null;
    shipline?: string | null;
    contact?: string | null;
    revision_number?: string | null;
    comment?: string | null;
    status?: string | null;
    is_send_docket_button_needed?: boolean;
    carrier_booking_number?: string | null;
    country_of_import_export?: string | null;
    customer_invoice_id?: string | null;
    package_type?: string | null;
    package_count?: string | null;
    category?: string | null;
  }

  const data: CustomerData = apiData?.message?.data || {};

  const getStatusClasses = (status: string) => {
    switch (status) {
      case "New":
        return "bg-[#DBEAFE] text-[#1D4ED8] border-[#3B82F6]";
      case "Rejected":
        return "bg-[#FEE2E2] text-[#B91C1C] border-[#EF4444]";
      case "Accepted":
        return "bg-[#DCFCE7] text-[#15803D] border-[#22C55E]";
      case "Acknowledged":
        return "bg-[#FFEDD5] text-[#C2410C] border-[#F97316]";
      case "Reopen":
        return "bg-[#FEF9C3] text-[#A16207] border-[#EAB308]";
      case "Open":
        return "bg-[#EDE9FE] text-[#6D28D9] border-[#8B5CF6]";
      case "Sent":
        return "bg-[#cceded] text-[#2f9696] border-[#2f9696]";
      case "Revised":
        return "bg-[#b6d4e3] text-[#1782b8] border-[#1782b8]";
      default:
        return "bg-[#F3F4F6] text-[#374151] border-[#9CA3AF]";
    }
  };

  const formatDate = (dateString?: string | null) => {
    if (!dateString) return "N/A";
    try {
      return format(new Date(dateString), "MMM dd, yyyy");
    } catch (error) {
      console.error("Error formatting date:", error);
      return dateString;
    }
  };

  if (isApiLoading || isDocketLoading) {
    return <Loader />;
  }

  if (isApiError || isDocketError) {
    toast.error(
      `Error loading docket data: ${String(
        apiError || docketError || "Unknown error"
      )}`
    );
    return null;
  }

  const isLatestRevisionAccepted =
    DocketRevitionDate.length > 0 &&
    DocketRevitionDate[0].status === "Accepted";

  return (
    <>
      <div className="p-6 bg-white shadow-md space-y-6">
        <div className="flex items-start gap-6 border-b pb-6">
          <div className="flex-1">
            <h2 className="text-xl font-semibold">{data?.customer || "N/A"}</h2>

            <div className="grid grid-cols-5 gap-4 mt-4 text-sm text-gray-700">
              <div>
                <p className="text-gray-500 mb-1">Dockets ID</p>
                <p>{data?.docket_id || "N/A"}</p>
              </div>
              <div>
                <p className="text-gray-500 mb-1">Booking ID</p>
                <p>{data?.carrier_booking_number || "N/A"}</p>
              </div>
              <div>
                <p className="text-gray-500 mb-1">Port Of Origin</p>
                <p className="flex items-center gap-2">
                  {data?.origin || "N/A"}
                </p>
              </div>
              <div className="flex flex-col gap-2 ml-4">
                {!isLatestRevisionAccepted && (
                  <>
                    {data.status === "Rejected" || data.status === "Reopen" ? (
                      <Button
                        variant="ghost"
                        className="w-fit border border-red-500 text-red-500 hover:bg-red-500 hover:text-red-50 px-3 py-1 text-sm whitespace-nowrap flex items-center gap-2"
                        onClick={() => {
                          handleCreateDocuments(String(id), data.booking ?? "");
                        }}
                        disabled={isCreatingDocs}
                      >
                        {isCreatingDocs ? (
                          <>
                            <svg
                              className="animate-spin h-4 w-4 text-red-500"
                              xmlns="http://www.w3.org/2000/svg"
                              fill="none"
                              viewBox="0 0 24 24"
                            >
                              <circle
                                className="opacity-25"
                                cx="12"
                                cy="12"
                                r="10"
                                stroke="currentColor"
                                strokeWidth="4"
                              ></circle>
                              <path
                                className="opacity-75"
                                fill="currentColor"
                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                              ></path>
                            </svg>
                            {data.revision_number !== 0
                              ? "Updating..."
                              : "Creating..."}
                          </>
                        ) : data.revision_number !== 0 ? (
                          "Update Doc"
                        ) : (
                          "Create Docs"
                        )}
                      </Button>
                    ) : data.status === "New" ? (
                      <Button
                        variant="ghost"
                        className="w-fit border border-red-500 text-red-500  hover:bg-red-500 hover:text-red-50 px-3 py-1 text-sm whitespace-nowrap flex items-center gap-2"
                        onClick={() => {
                          handleCreateDocuments(String(id), data.booking ?? "");
                        }}
                        disabled={isCreatingDocs}
                      >
                        {isCreatingDocs ? (
                          <>
                            <svg
                              className="animate-spin h-4 w-4 text-red-500"
                              xmlns="http://www.w3.org/2000/svg"
                              fill="none"
                              viewBox="0 0 24 24"
                            >
                              <circle
                                className="opacity-25"
                                cx="12"
                                cy="12"
                                r="10"
                                stroke="currentColor"
                                strokeWidth="4"
                              ></circle>
                              <path
                                className="opacity-75"
                                fill="currentColor"
                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                              ></path>
                            </svg>
                            {data.revision_number !== 0
                              ? "Updating..."
                              : "Creating..."}
                          </>
                        ) : data.revision_number !== 0 ? (
                          "Update Doc"
                        ) : (
                          "Create Docs"
                        )}
                      </Button>
                    ) : (data.status === "Open" || data.status === "Reopen") &&
                      data.is_send_docket_button_needed === true &&
                      !hasSentDocket ? (
                      <div className="flex gap-2">
                        <Button
                        variant="ghost"
                        className="w-fit border border-red-500 text-red-500 hover:bg-red-500 hover:text-red-50 px-3 py-1 text-sm whitespace-nowrap flex items-center gap-2"
                        onClick={() => setSendModalOpen(true)}
                      >
                        Send Docs
                      </Button>

                        <Button
                          variant="ghost"
                          className="w-fit border border-blue-500 text-blue-500 hover:bg-blue-500 hover:text-blue-50 px-3 py-1 text-sm whitespace-nowrap flex items-center gap-2"
                          onClick={() => {
                            handleReGenerateDocuments(
                              String(id),
                              data.booking ?? ""
                            );
                          }}
                          disabled={isCreatingDocs}
                        >
                          {isCreatingDocs ? (
                            <>
                              <svg
                                className="animate-spin h-4 w-4 text-blue-500"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                              >
                                <circle
                                  className="opacity-25"
                                  cx="12"
                                  cy="12"
                                  r="10"
                                  stroke="currentColor"
                                  strokeWidth="4"
                                ></circle>
                                <path
                                  className="opacity-75"
                                  fill="currentColor"
                                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3042 1.135 5.824 3 7.938l3-2.647z"
                                ></path>
                              </svg>
                              Regenerating...
                            </>
                          ) : (
                            "Regenerate Doc"
                          )}
                        </Button>
                      </div>
                    ) : null}
                  </>
                )}
                {isLatestRevisionAccepted && (
                  <Button
                    variant="ghost"
                    className="w-fit border border-[#D70654] text-[#D70654] hover:bg-[#D70654] hover:text-white px-3 py-1 text-sm whitespace-nowrap flex items-center gap-2 transition-colors"
                    onClick={handleReopenWithComment}
                    disabled={isApiLoading}
                  >
                    {isApiLoading ? (
                      <>
                        <svg
                          className="animate-spin h-4 w-4 text-[#D70654]"
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                        >
                          <circle
                            className="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            strokeWidth="4"
                          ></circle>
                          <path
                            className="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                          ></path>
                        </svg>
                        Processing...
                      </>
                    ) : (
                      "ReOpen"
                    )}
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="p-6 bg-white shadow-md space-y-6">
        <Tabs defaultValue="file-information" className="w-full">
          <div className="flex justify-between items-center border-b border-gray-200">
            <TabsList className="flex w-full max-w-md">
              <TabsTrigger
                value="file-information"
                className="flex-1 text-center uppercase text-sm font-medium text-gray-500 data-[state=active]:text-orange-500 data-[state=active]:border-b-2 data-[state=active]:border-orange-500 data-[state=active]:font-semibold rounded-none"
              >
                FILE INFORMATION
              </TabsTrigger>
              <TabsTrigger
                value="attachments"
                className="flex-1 text-center uppercase text-sm font-medium text-gray-500 data-[state=active]:text-orange-500 data-[state=active]:border-b-2 data-[state=active]:border-orange-500 data-[state=active]:font-semibold rounded-none"
              >
                ATTACHMENTS
              </TabsTrigger>
            </TabsList>
            <div className="flex gap-2">
              <Button
                variant="ghost"
                className="flex items-center gap-1 border border-gray-300 text-gray-600 hover:bg-gray-100 px-3 py-1 text-sm rounded-md"
                onClick={() => {
                  if (data?.invoice) {
                    window.location.href = `/dashboard/customers/update-invoice/${data?.customer_invoice_id}?docketId=${id}`;
                  } else {
                    window.location.href = `/dashboard/customers/generate-invoice?docketId=${id}`;
                  }
                }}
              >
                <FileText className="text-primary" />
                {!data?.invoice ? "Generate Invoice" : "Update Invoice"}
              </Button>
              <Button
                variant="ghost"
                className="flex items-center gap-1 border border-gray-300 text-gray-600 hover:bg-gray-100 px-3 py-1 text-sm rounded-md"
                onClick={() => {
                  setDrawerMode("edit");
                  setDrawerOpen(true);
                }}
              >
                <Pencil className="w-4 h-4" />
                Edit File Information
              </Button>
            </div>
          </div>

          <TabsContent value="details" className="pt-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm text-gray-800">
              <div className="md:col-span-3">
                <strong>No data available</strong>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="file-information" className="space-y-4 pt-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <p className="text-gray-500 font-medium mb-1">Shipper</p>
                <p>
                  {data?.shipper_name || "N/A"}
                  {data?.shipper && (
                    <>
                      <br />
                      {data.shipper}
                    </>
                  )}
                </p>
              </div>
              <div>
                <p className="text-gray-500 font-medium mb-1">Consignee</p>
                <p>{data?.consignee || "N/A"}</p>
              </div>
              <div>
                <p className="text-gray-500 font-medium mb-1">Material</p>
                <p>{data?.material || "N/A"}</p>
              </div>
            </div>
            <div className="border-t pt-6" />
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              <div>
                <p className="text-gray-500 font-medium mb-1">Shipping Date</p>
                <p>{formatDate(data?.shipping_date) || "N/A"}</p>
              </div>
              <div>
                <p className="text-gray-500 font-medium mb-1">Invoice</p>
                <p>{data?.invoice || "N/A"}</p>
              </div>
              <div>
                <p className="text-gray-500 font-medium mb-1">Origin</p>
                <p>{data.origin || "N/A"}</p>
              </div>
              <div>
                <p className="text-gray-500 font-medium mb-1">HS Code</p>
                <p>{data?.hs_code || "N/A"}</p>
              </div>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              <div>
                <p className="text-gray-500 font-medium mb-1">Status</p>
                <p>{data?.status || "N/A"}</p>
              </div>
              <div>
                <p className="text-gray-500 font-medium mb-1">Destination</p>
                <p>{data?.destination || "N/A"}</p>
              </div>
              <div>
                <p className="text-gray-500 font-medium mb-1">
                  Destination Contact
                </p>
                <p>{data?.destination_contact || "N/A"}</p>
              </div>
              <div>
                <p className="text-gray-500 font-medium mb-1">BL No</p>
                <p>{data?.blno || "N/A"}</p>
              </div>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              <div>
                <p className="text-gray-500 font-medium mb-1">Telephone</p>
                <p>{data?.telephone || "N/A"}</p>
              </div>
              <div>
                <p className="text-gray-500 font-medium mb-1">Terms</p>
                <p>{data?.terms || "N/A"}</p>
              </div>
              <div>
                <p className="text-gray-500 font-medium mb-1">
                  Origin Of Goods
                </p>
                <p>{data?.origin_of_goods || "N/A"}</p>
              </div>
              <div>
                <p className="text-gray-500 font-medium mb-1">
                  Country of Import/Export
                </p>
                <p>{data?.country_of_import_export || "N/A"}</p>
              </div>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              <div>
                <p className="text-gray-500 font-medium mb-1">Contact</p>
                <p>{data?.contact || "N/A"}</p>
              </div>
              <div>
                <p className="text-gray-500 font-medium mb-1">Containers</p>
                <p>{data.containers || "N/A"}</p>
              </div>
              <div>
                <p className="text-gray-500 font-medium mb-1">Package Type</p>
                <p>{data.package_type || "N/A"}</p>
              </div>
              <div>
                <p className="text-gray-500 font-medium mb-1">Package Count</p>
                <p>{data.package_count || "N/A"}</p>
              </div>
              <div>
                <p className="text-gray-500 font-medium mb-1">Shipline</p>
                <p>{data.shipline || "N/A"}</p>
              </div>
              <div>
                <p className="text-gray-500 font-medium mb-1">Category</p>
                <p>{data.category || "N/A"}</p>
              </div>
              <div>
                <p className="text-gray-500 font-medium mb-1">
                  Revision Number
                </p>
                <p>{data?.revision_number || "0"}</p>
              </div>
              <div>
                <p className="text-gray-500 font-medium mb-1">Total Weight</p>
                <p>{data?.weight || "0"} Kg</p>
                {weightMismatch && (
                  <div className="flex items-center mt-1 text-red-500 text-xs">
                    <AlertTriangle className="w-3 h-3 mr-1" />
                    <span>
                      Mismatch in the total weight from the BL and total
                      container weight
                    </span>
                  </div>
                )}
              </div>
            </div>

            <div className="mt-8 mb-4 border-t pt-6">
              <h2 className="text-lg font-semibold text-gray-800">
                Containers List
              </h2>
            </div>

            <div className="space-y-4">
              {containersData && containersData.length > 0 ? (
                <>
                  {containersData.map((container, index) => (
                    <Card key={index} className="p-5 mt-6">
                      <CardContent>
                        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 items-end text-xs">
                          <div className="flex flex-col space-y-1">
                            <p className="text-xs sm:text-sm text-gray-500">
                              Container No
                            </p>
                            <p className="font-semibold text-sm break-all">
                              {container.containerNo}
                            </p>
                          </div>
                          <div className="flex flex-col space-y-1">
                            <p className="text-xs sm:text-sm text-gray-500">
                              Seal No
                            </p>
                            <p className="font-semibold text-sm">
                              {container.sealNo || "-"}
                            </p>
                          </div>
                          <div className="flex flex-col space-y-1 text-left">
                            <p className="text-xs sm:text-sm text-gray-500">
                              Net Weight
                            </p>
                            <p className="font-semibold text-sm">
                              {container.netWeight || "-"} Kg
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}

                  {/* Total Net Weight Row */}
                  <Card className="p-5 mt-6 bg-gray-50">
                    <CardContent>
                      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 items-end text-xs">
                        {/* Left - Total Containers */}
                        <div className="flex flex-col space-y-1">
                          <p className="text-xs sm:text-sm text-gray-500">
                            Total
                          </p>
                          <p className="font-semibold text-sm break-all">
                            {containersData.length} Containers
                          </p>
                        </div>

                        {/* Empty placeholder to keep same grid alignment */}
                        <div></div>

                        {/* Right - Total Container Weight (aligned with Net Weight column) */}
                        <div className="flex flex-col space-y-1 text-left">
                          <p className="text-xs sm:text-sm text-gray-500">
                            Total Container Weight
                          </p>
                          <p className="font-semibold text-sm">
                            {totalNetWeight.toFixed(2)} Kg
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </>
              ) : (
                <div className="text-sm text-gray-500 italic mt-4">
                  No container data available.
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="attachments" className="pt-4 space-y-6">
            <Accordion type="single" collapsible className="w-full">
              {DocketRevitionDate?.map((docket: any, index: any) => {
                const createdDate = formatDate(docket.creation);

                const standardFiles = [
                  { name: "Bill of Lading", file: docket.bill_of_ladding },
                  {
                    name: "Certificate of Origin",
                    file: docket.certificate_of_origin,
                  },
                  { name: "Form 6", file: docket.form6 },
                  { name: "Form 9", file: docket.form9 },
                  { name: "Packing List", file: docket.packing_list },
                  { name: "Invoice", file: docket.invoice },
                  {
                    name: "ISCC PLUS Self-Declaration",
                    file: docket.isccplus_self_declaration,
                  },
                  {
                    name: "ISCC Self-Declaration",
                    file: docket.iscc_self_declaration,
                  },
                ].filter((file) => file.file);

                const additionalFiles =
                  docket.additional_attachments?.map((attachment: any) => ({
                    name: attachment.file_name || "Additional File",
                    file: attachment.url,
                    id: attachment.name,
                  })) || [];

                const files = [...standardFiles, ...additionalFiles];

                const badgeClasses = docket.status
                  ? getStatusClasses(docket.status)
                  : "";

                return (
                  <AccordionItem value={`item-${index}`} key={index}>
                    <AccordionTrigger className="hover:no-underline">
                      <div className="flex justify-between w-full text-left">
                        <div>
                          <div className="flex items-center gap-3 font-medium text-gray-800">
                            <PackageIcon className="text-primary" />
                            Revision {docket.revision_number}
                          </div>
                          <div className="text-xs text-gray-500">
                            {" "}
                            {`Created on ${createdDate ?? "N/A"}`}
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge
                            variant="outline"
                            className={`${badgeClasses} text-xs font-medium`}
                          >
                            {docket.status === "Reopen"
                              ? "Reopened"
                              : docket.status}
                          </Badge>

                          <Button
                            variant="ghost"
                            size="icon"
                            className="text-gray-400 hover:text-gray-600"
                            onClick={() => {
                              setSelectedRevision(docket.revision_name);
                              setSelectedRevisionNumber(docket.revision_number);
                              setIsReopenMode(false);
                              setCommentDrawerOpen(true);
                            }}
                          >
                            <MessageSquareCode className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    </AccordionTrigger>

                    <AccordionContent className="space-y-3 pt-4">
                      {files.length > 0 ? (
                        <>
                          {docket.status === "Open" && (
                            <div className="mt-4 border-t pt-4">
                              <div className="flex items-center justify-between mb-2">
                                <h3 className="text-sm font-medium text-gray-700">
                                  Upload New Files
                                </h3>
                              </div>

                              {fileInputs.map((input, idx) => (
                                <div
                                  key={input.id}
                                  className="flex items-center gap-2 mb-2"
                                >
                                  <div className="relative w-64">
                                    <input
                                      type="file"
                                      className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                                      id={`file-upload-${input.id}`}
                                      ref={(el) =>
                                        (fileInputRefs.current[input.id] = el)
                                      }
                                      onChange={(e) =>
                                        handleFileChange(input.id, e)
                                      }
                                    />
                                    <label
                                      htmlFor={`file-upload-${input.id}`}
                                      className="block w-full border border-gray-300 rounded-md px-3 py-1.5 text-sm text-gray-500 hover:border-orange-500 transition-colors cursor-pointer truncate"
                                    >
                                      {input.fileName || "Choose file"}
                                    </label>
                                  </div>
                                  {idx === fileInputs.length - 1 && (
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      className="h-8 w-8 text-gray-500 hover:text-orange-500"
                                      onClick={addFileInput}
                                    >
                                      <Plus className="w-4 h-4" />
                                    </Button>
                                  )}
                                  {fileInputs.length > 1 && (
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      className="h-8 w-8 text-gray-500 hover:text-red-500"
                                      onClick={() => removeFileInput(input.id)}
                                    >
                                      <Minus className="w-4 h-4" />
                                    </Button>
                                  )}
                                </div>
                              ))}

                              <Button
                                variant="outline"
                                className="mt-2 text-sm py-1 px-3 h-8"
                                onClick={() =>
                                  handleUpdateDocketRevision(
                                    docket.revision_name,
                                    String(id)
                                  )
                                }
                              >
                                Upload Files
                              </Button>
                            </div>
                          )}

                          {files.map((file, i) => {
                            const fileExtension = file.file
                              .split(".")
                              .pop()
                              ?.toLowerCase();
                            const isPDF = fileExtension === "pdf";

                            return (
                              <div
                                key={i}
                                className="flex items-center justify-between border-t pt-3 text-sm"
                              >
                                <div className="flex items-center gap-3 text-gray-700">
                                  <img
                                    src={isPDF ? PDF : ImageFile}
                                    alt={isPDF ? "PDF" : "File"}
                                    className="w-4 h-4"
                                  />
                                  <div className="flex flex-col">
                                    <span>{file.name}</span>
                                    <span className="text-xs text-gray-400">
                                      {file.file.split("/").pop()}
                                    </span>
                                  </div>
                                </div>
                                <div className="flex items-center gap-2 mr-5">
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    className="text-gray-400  border border-gray-300 rounded-md hover:text-orange-500 hover:border-orange-500"
                                    onClick={() =>
                                      window.open(file.file, "_blank")
                                    }
                                  >
                                    <Eye className="w-4 h-4" />
                                  </Button>

                                  {additionalFiles.some(
                                    (f) => f.file === file.file
                                  ) && (
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      className="text-gray-400 border border-gray-300 rounded-md hover:text-red-500 hover:border-red-500"
                                      onClick={() => {
                                        toast.custom((t) => (
                                          <div className="bg-white p-4 rounded-md shadow-lg border border-gray-200 w-full max-w-md">
                                            <h3 className="font-medium text-gray-800 mb-2">
                                              Confirm Deletion
                                            </h3>
                                            <p className="text-sm text-gray-600 mb-4">
                                              Are you sure you want to delete
                                              this file?
                                            </p>
                                            <div className="flex justify-end gap-2">
                                              <Button
                                                variant="outline"
                                                className="text-sm px-3 py-1 h-8"
                                                onClick={() => toast.dismiss(t)}
                                              >
                                                Cancel
                                              </Button>
                                              <Button
                                                variant="destructive"
                                                className="text-sm px-3 py-1 h-8"
                                                onClick={async () => {
                                                  toast.dismiss(t);
                                                  await handleDeleteAdditionalFile(
                                                    String(id),
                                                    docket.revision_name,
                                                    file.id
                                                  );
                                                }}
                                                disabled={isDeletingFile}
                                              >
                                                {isDeletingFile
                                                  ? "Deleting..."
                                                  : "Delete"}
                                              </Button>
                                            </div>
                                          </div>
                                        ));
                                      }}
                                      disabled={isDeletingFile}
                                    >
                                      <Trash2 className="w-4 h-4" />
                                    </Button>
                                  )}

                                  {/* {file.name === "Invoice" && file.file && (
                                    <Button
                                      variant="ghost"
                                      className="text-gray-500 border border-gray-300 hover:text-orange-500 hover:border-orange-500 transition-colors"
                                      onClick={() => {
                                        setInvoiceMode("edit");
                                        setInvoiceDrawerOpen(true);
                                      }}
                                    >
                                      <Pencil className="w-4 h-4" />
                                    </Button>
                                  )} */}
                                </div>
                              </div>
                            );
                          })}
                        </>
                      ) : (
                        <div className="text-sm text-gray-500 italic">
                          No files available
                        </div>
                      )}
                    </AccordionContent>
                  </AccordionItem>
                );
              })}
            </Accordion>
          </TabsContent>
        </Tabs>
        <RightDrawer
          open={drawerOpen}
          onOpenChange={setDrawerOpen}
          mode={drawerMode}
          carrierBooking={carrierBooking ?? undefined}
        />
        <CommentDrawer
          open={commentDrawerOpen}
          onOpenChange={() => {
            setCommentDrawerOpen(false);
            setIsReopenMode(false);
          }}
          revisionName={selectedRevision}
          revisionNumber={selectedRevisionNumber}
          isReopenMode={isReopenMode}
          onReopenComplete={handleReopenComplete}
        />
        <SendDocketModal
          open={sendModalOpen}
          onClose={() => setSendModalOpen(false)}
          docketId={String(id)}
          customerEmail="Customer's registered email"
          customerName={data?.customer || 'Customer'}
          carrierBooking={data?.carrier_booking_number || 'N/A'}
          blno={data?.blno || 'N/A'}
          revisionNumber={data?.revision_number || '0'}
          existingAttachments={
            DocketRevitionDate.length > 0
              ? [
                  { name: "Packing List", url: DocketRevitionDate[0]?.packing_list || "" },
                  { name: "Certificate of Origin", url: DocketRevitionDate[0]?.certificate_of_origin || "" },
                  { name: "Form 9", url: DocketRevitionDate[0]?.form9 || "" },
                  { name: "Form 6", url: DocketRevitionDate[0]?.form6 || "" },
                  { name: "Invoice", url: DocketRevitionDate[0]?.invoice || "" },
                  { name: "Bill of Lading", url: DocketRevitionDate[0]?.bill_of_ladding || "" },
                  ...(DocketRevitionDate[0]?.isccplus_self_declaration ? [{ name: "ISCC Plus Self Declaration", url: DocketRevitionDate[0].isccplus_self_declaration }] : []),
                  ...(DocketRevitionDate[0]?.iscc_self_declaration ? [{ name: "ISCC Self Declaration", url: DocketRevitionDate[0].iscc_self_declaration }] : []),
                  ...(DocketRevitionDate[0]?.additional_attachments?.map((att: any) => ({ name: att.file_name || "Additional File", url: att.url })) || [])
                ].filter(att => att.url)
              : []
          }
          onSend={handleSendDocketWithModal}
        />
      </div>
    </>
  );
}
