import React, { useEffect, useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { Eye, EyeOff, Check, X, Info } from "lucide-react";
import { resetPassword } from "@/services/signin";
import { useMutation } from "@tanstack/react-query";

const ResetPasswordPage: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { username } = location.state || {};

  useEffect(() => {
    if (!username) {
      toast.error("Unauthorized access to Reset Password page.");
      navigate("/dashboard", { replace: true });
    }
  }, [username, navigate]);

  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [showPasswordHint, setShowPasswordHint] = useState(false);
  const passwordValidation = {
    length: newPassword.length >= 8,
    uppercase: /[A-Z]/.test(newPassword),
    lowercase: /[a-z]/.test(newPassword),
    number: /\d/.test(newPassword),
    specialChar: /[@$!%*?&]/.test(newPassword),
  };
  const isPasswordValid = Object.values(passwordValidation).every(Boolean);

  // Mutation for Reset Password
  const resetPasswordMutation = useMutation({
    mutationFn: ({
      username,
      password,
    }: {
      username: string;
      password: string;
    }) => resetPassword(username, password),
    onSuccess: (res) => {
      if (res?.message?.status_code === 200) {
        toast.success(res?.message?.message);
        navigate("/dashboard"); // redirect to login
      } else {
        toast.error(res?.message?.message || "Failed to reset password");
      }
    },
    onError: () => {
      toast.error("Something went wrong. Try again.");
    },
  });

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!isPasswordValid) {
      toast.error("Password does not meet requirements.");
      return;
    }
    if (!newPassword || !confirmPassword) {
      toast.error("Please fill in both fields.");
      return;
    }

    if (newPassword !== confirmPassword) {
      toast.error("Passwords do not match!");
      return;
    }

    resetPasswordMutation.mutate({ username, password: newPassword });
  };

  return (
    <div className="flex h-[100vh] items-center justify-center bg-gray-50 px-4">
      <div className="w-full max-w-md rounded-lg bg-white p-8 shadow-lg">
        <h1 className="text-2xl font-bold text-center text-[#1a1e3a] mb-6">
          Reset Password
        </h1>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* New Password */}
          <div className="relative">
            <label
              htmlFor="newPassword"
              className="block text-sm font-medium text-gray-700 flex justify-between items-center"
            >
              New Password
              <span
                className="relative flex items-center"
                title="Password requirements" 
              >
                <Info
                  size={16}
                  className="cursor-pointer text-gray-400 hover:text-gray-600 ml-2"
                  onClick={() => setShowPasswordHint(!showPasswordHint)}
                />
              </span>
            </label>
            <Input
              id="newPassword"
              type={showNewPassword ? "text" : "password"}
              placeholder="Enter New Password"
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
              required
              className="h-12 rounded-md border border-gray-300 px-4 text-sm focus:border-gray-400 focus:outline-none mt-1 pr-10"
            />
            <span
              onClick={() => setShowNewPassword(!showNewPassword)}
              className="absolute right-3 top-[38px] cursor-pointer text-gray-500"
            >
              {showNewPassword ? <EyeOff size={18} /> : <Eye size={18} />}
            </span>
          </div>
          {newPassword && (
            <div className="h-2 w-full rounded-full bg-gray-200 mt-2">
              <div
                className={`h-2 rounded-full transition-all duration-300 ${
                  isPasswordValid
                    ? "bg-green-500 w-full"
                    : newPassword.length >= 8
                    ? "bg-yellow-400 w-3/4"
                    : "bg-red-400 w-1/4"
                }`}
              ></div>
            </div>
          )}

          {/* Password Hint */}
          {showPasswordHint && (
            <div className="border border-gray-200 rounded-md p-4 bg-gray-50 text-sm text-gray-700 mt-2 transition-all duration-300">
              <p className="font-semibold mb-2">
                Your password should include:
              </p>
              <ul className="space-y-1">
                {[
                  {
                    label: "At least 8 characters",
                    valid: passwordValidation.length,
                  },
                  {
                    label: "One uppercase letter",
                    valid: passwordValidation.uppercase,
                  },
                  {
                    label: "One lowercase letter",
                    valid: passwordValidation.lowercase,
                  },
                  { label: "One number", valid: passwordValidation.number },
                  {
                    label: "One special character (@$!%*?&)",
                    valid: passwordValidation.specialChar,
                  },
                ].map((item, index) => (
                  <li
                    key={index}
                    className={`flex items-center gap-2 ${
                      item.valid ? "text-green-600" : "text-red-500"
                    }`}
                  >
                    {item.valid ? (
                      <Check size={16} className="flex-shrink-0" />
                    ) : (
                      <X size={16} className="flex-shrink-0" />
                    )}
                    <span>{item.label}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}
          {/* Confirm Password */}
          <div className="relative">
            <label
              htmlFor="confirmPassword"
              className="block text-sm font-medium text-gray-700"
            >
              Confirm Password
            </label>
            <Input
              id="confirmPassword"
              type={showConfirmPassword ? "text" : "password"}
              placeholder="Confirm New Password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              required
              className="h-12 rounded-md border border-gray-300 px-4 text-sm focus:border-gray-400 focus:outline-none mt-1 pr-10"
            />
            <span
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              className="absolute right-3 top-[38px] cursor-pointer text-gray-500"
            >
              {showConfirmPassword ? <EyeOff size={18} /> : <Eye size={18} />}
            </span>
          </div>

          {/* Reset Password Button */}
          <Button
            type="submit"
            disabled={
              !newPassword ||
              !confirmPassword ||
              newPassword !== confirmPassword ||
              !isPasswordValid ||
              resetPasswordMutation.isPending
            }
            className={`w-full py-3 text-sm text-white ${
              newPassword &&
              confirmPassword &&
              newPassword === confirmPassword &&
              isPasswordValid &&
              !resetPasswordMutation.isPending
                ? "bg-[#1a1e3a] hover:bg-[#272c4e]"
                : "bg-gray-400 cursor-not-allowed"
            }`}
          >
            {resetPasswordMutation.isPending
              ? "Resetting..."
              : "Reset Password"}
          </Button>

          {/* Back to Login */}
          <p className="text-center text-sm text-gray-600 mt-4">
            <Link to="/dashboard" className="hover:text-gray-800">
              Back to Login
            </Link>
          </p>
        </form>
      </div>
    </div>
  );
};

export default ResetPasswordPage;
