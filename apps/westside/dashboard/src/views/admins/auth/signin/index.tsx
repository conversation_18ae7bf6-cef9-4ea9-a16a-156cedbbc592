import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Link, useNavigate } from "react-router-dom";
import { SignIn } from "@/services/signin";
import { AuthResponseType } from "@/types/AuthResponseType";
import { toast } from "sonner";
import logo from "@/assets/img/logo.svg";
import handshake from "@/assets/img/handshake.svg";
import { useAuthContext } from "@/lib/providers/context/AuthContext";
import { Eye, EyeOff } from "lucide-react";

const SigninPageViews = () => {
  const navigate = useNavigate();
  const [password, setPassword] = useState("");
  const [username, setUsername] = useState("");

  const { setUserData } = useAuthContext();
  const [showPassword, setShowPassword] = useState(false);


  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const res: AuthResponseType = await SignIn({ username, password });
      const authKeyFromResponse = res?.message?.auth_key;

      setUserData(authKeyFromResponse, res?.message?.session, res?.message?.roles);
      localStorage.setItem("token", authKeyFromResponse);

      navigate("/dashboard");
    } catch (error) {
      console.error("Error during sign-in:", error);
      if (error instanceof Error) {
        toast.error(error.message);
      } else {
        toast.error("An unknown error occurred");
      }
    }
  };

  return (
    <div className="flex h-[100dvh] w-full flex-col md:flex-row">
      {/* Left Panel */}
      <div className="flex w-full h-full flex-col justify-between bg-white px-8 py-12 md:w-1/2 lg:px-20 lg:py-16">
        <div className="h-full">
          {/* Logo */}
          <img
            src={logo} // Update with actual logo path
            alt="Westside Exports Logo"
            className="h-18"
          />

          <div className="h-full flex flex-col justify-center">
            <div className="mb-10">
              <h2 className="text-xl font-normal text-gray-700">Hello,</h2>
              <h1 className="text-4xl font-bold text-[#1a1e3a]">
                Welcome <span className="text-[#1a1e3a]">!</span>
              </h1>
            </div>

            <form onSubmit={handleSubmit} className="space-y-5 max-w-md w-full">
              <Input
                type="text"
                name="Username"
                placeholder="User ID"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                required
                className="h-12 rounded-md border border-gray-300 px-4 text-sm focus:border-gray-400 focus:outline-none"
              />
              <div className="relative">
                <Input
                  type={showPassword ? "text" : "password"}
                  name="password"
                  placeholder="Password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  className="h-12 rounded-md border border-gray-300 px-4 text-sm focus:border-gray-400 focus:outline-none pr-10"
                />

                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                >
                  {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                </button>
              </div>


              <div className="flex items-center justify-between text-sm">
                {/* <div className="flex items-center space-x-2">
                  <Checkbox
                    id="remember"
                    className="border-gray-400 data-[state=checked]:bg-[#1a1e3a] data-[state=checked]:text-white"
                  />
                  <label htmlFor="remember" className="text-gray-600">
                    Remember Me
                  </label>
                </div> */}

                {/* <Link
                  to="/forgot-password"
                  className="text-gray-600 hover:text-gray-800"
                >
                  Forgot Password?
                </Link> */}
              </div>

              <Button type="submit" className="w-full bg-[#1a1e3a] py-3 text-white hover:bg-[#272c4e] text-sm">
                Log In
              </Button>
              <div className="flex items-center justify-between text-sm">
                <Link
                  to="/forgot-password"
                  className="text-gray-600 hover:text-gray-800"
                >
                  Forgot Password?
                </Link>
              </div>


              <footer className="text-center text-xs text-gray-500 pt-4">
                <p>©2025 Westside Exports. All Rights Reserved.</p>
                <p className="mt-1 space-x-1">
                  <Link to="/legal" className="hover:text-gray-700">
                    Legal Terms & Conditions
                  </Link>
                  <span>/</span>
                  <Link to="/privacy" className="hover:text-gray-700">
                    Privacy Policy
                  </Link>
                </p>
              </footer>
            </form>
          </div>
        </div>
      </div>

      {/* Right Panel */}
      <div className="hidden md:block md:w-1/2 relative">
        <img
          src={handshake} // Update with actual image path
          alt="Handshake visual"
          className="h-full w-full object-cover"
        />
      </div>
    </div>
  );
};

export default SigninPageViews;
