import { Typography } from "@/components/typography";
import { But<PERSON> } from "@/components/ui/button";
import { Printer, Repeat2, FilePenLine, Truck } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import { useNavigate, useParams } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import Loader from "@/components/Loader";
import { RadioGroup } from "@/components/ui/radio-group";
import { fetchShippingInstructionDetails, getAllSiStatuses } from "@/services/admin/shippingInstructionConfirmation";
import { useState, useEffect } from "react";

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

function ShippingInstructionConfirmationView() {
  const { id } = useParams();
  const [canRender, setCanRender] = useState(false);
  const navigate = useNavigate();

  const { data, isLoading, isError } = useQuery({
    queryKey: ["shippingInstructionDetails", { name: String(id) || "" }],
    queryFn: fetchShippingInstructionDetails,
    enabled: !!id,
  });

  const {
    data: _siStatuses,
    isLoading: _isStatusesLoading,
    isError: _isStatusesError,
  } = useQuery({
    queryKey: ["siStatuses"],
    queryFn: () => getAllSiStatuses(),
    enabled: !!id,
  });

  useEffect(() => {
    if (!isLoading && data) {
      const timer = setTimeout(() => {
        setCanRender(true);
      }, 50);

      return () => clearTimeout(timer);
    }
  }, [isLoading, data]);

  if (isLoading || !data || !canRender) {
    return (
      <div className="flex justify-center items-center min-h-[300px]">
        <Loader />
      </div>
    );
  }

  const details = data?.message;
  if (isError) return <div>Error loading data</div>;

  function formatToCustomGMT(dateString: string): string {
    let date: Date;
    if (!dateString) return " ";
    if (dateString.includes(" ")) {
      // Remove microseconds and treat it as local time
      const cleanedDateString = dateString.split(".")[0];
      date = new Date(cleanedDateString.replace(" ", "T"));
    } else {
      // Just a date, assume midnight local time
      date = new Date(dateString);
    }

    return date.toUTCString(); // convert to GMT
  }

  function formatDate(dateString: string | undefined) {
    if (!dateString) return null;
    const date = new Date(dateString);
    const formattedDate = format(date, "dd-MMM-yyyy");
    return formattedDate;
  }

  function formatDateGMT(dateStr: string): string {
    if (!dateStr) return "—";

    const [datePart, timePart] = dateStr.trim().split(" ");
    if (!datePart || !timePart) return "—";

    const [dayStr, monthStr, yearStr] = datePart.split("-");
    const [hourStr, minuteStr] = timePart.split(":");

    const day = parseInt(dayStr, 10);
    const month = parseInt(monthStr, 10);
    const year = parseInt(yearStr, 10);
    const hour = parseInt(hourStr, 10);
    const minute = parseInt(minuteStr, 10);

    if ([day, month, year, hour, minute].some((v) => isNaN(v))) return "—";

    const date = new Date(Date.UTC(year, month - 1, day, hour, minute));
    if (isNaN(date.getTime())) return "—";

    const weekdays = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
    const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];

    const weekday = weekdays[date.getUTCDay()];
    const monthName = months[date.getUTCMonth()];
    const dd = String(date.getUTCDate()).padStart(2, "0");
    const yyyy = date.getUTCFullYear();

    let hrs = date.getUTCHours();
    const mins = String(date.getUTCMinutes()).padStart(2, "0");
    const ampm = hrs >= 12 ? "PM" : "AM";
    hrs = hrs % 12 || 12;

    return `${weekday}, ${dd} ${monthName} ${yyyy}  ${hrs}:${mins} ${ampm}`;
  }

  return (
    <div className="mt-10 mb-[200px]">
      {details ? (
        <div className="print-area print confirmation-div">
          <div className="flex justify-end gap-2 print:hidden">
            <Button
              size={"lg"}
              variant={"outline"}
              className="border-sidebar cursor-pointer"
              onClick={() => window.print()}
            >
              <Printer />
              <span className="">Printer-Friendly</span>
            </Button>
            {/* <Button
              size={"lg"}
              variant={"outline"}
              className="border-[#D3DAE7]"
            >
              <Repeat2 />
              <span className="">Reuse</span>
            </Button> */}
            <Button
              size={"lg"}
              type="button"
              variant={"outline"}
              className="border-[#D3DAE7] "
              onClick={() => navigate(`/dashboard/booking/my-booking/create-si?amend_id=${details?.name}`)}
            >
              <FilePenLine />
              <span className="">Amend</span>
            </Button>
            {/* <Button
              size={"lg"}
              variant={"outline"}
              className="border-[#D3DAE7] "
            >
              <Truck />
              <span className="">Track Containers</span>
            </Button> */}
          </div>
          <div>
            <div className="mt-4 grid grid-cols-3 gap-3 py-8 px-5 border border-[#D3DAE7]">
              <div className="flex flex-col justify-center gap-2">
                <div className="flex gap-2 items-center">
                  <span className="text-[#929FB8] w-40">SI Name:</span>
                  <Typography>{details?.name}</Typography>
                </div>
                <div className="flex gap-2 items-center">
                  <span className="text-[#929FB8] w-40">Product Channel:</span>
                  <Typography>{details?.inttra_status?.product_channel || "Not Available"}</Typography>
                </div>
              </div>
              <div className="flex flex-col justify-center gap-2">
                <div className="flex gap-2 items-center">
                  <span className="text-[#929FB8] w-50">SI Requestor Company:</span>
                  <Typography>{details?.shipper_data?.shipper_name}</Typography>
                </div>
                <div className="flex gap-2 items-center">
                  <span className="text-[#929FB8] w-50">Created by: </span>
                  <Typography className="text-black">{details?.shipper_data?.contact_name} </Typography>
                </div>
                <div className="flex gap-2 items-center">
                  <span className="text-[#929FB8] w-50">Created on:</span>
                  <Typography>{formatToCustomGMT(details?.creationdate)}</Typography>
                </div>
              </div>
              <div className="flex flex-col justify-center gap-2">
                <div className="flex gap-2 items-center">
                  <span className="text-[#929FB8] w-20">Phone:</span>
                  <Typography>{details?.shipper_data?.phone}</Typography>
                </div>
                {details?.shipper_data?.fax && (
                  <div className="flex gap-2 items-center">
                    <span className="text-[#929FB8] w-20">Fax:</span>
                    <Typography>{details?.shipper_data?.fax}</Typography>
                  </div>
                )}
                <div className="flex gap-2 items-center">
                  <span className="text-[#929FB8] w-20">Email:</span>
                  <Typography>{details?.shipper_email}</Typography>
                </div>
              </div>
            </div>
            {details?.inttra_status?.shipping_instruction &&
              (() => {
                const matched = _siStatuses?.message?.data?.find(
                  (item: any) => item.shipping_instruction === details.inttra_status.shipping_instruction
                );

                return (
                  <>
                    <div className="flex border border-[#D3DAE7] p-4 gap-2 items-center border-t-0">
                      <Typography className="text-[#929FB8]">INTTRA SI Status:</Typography>
                      <Typography weight="semibold">{matched?.si_inttra_status || "N/A"}</Typography>
                      {matched?.si_inttra_status_date && (
                        <span className="text-[#4B5563]">
                          {" "}
                          <span className="font-bold">{"On "}</span>
                          {formatDateGMT(matched.si_inttra_status_date)} (GMT)
                        </span>
                      )}
                    </div>

                    <div className="flex border border-[#D3DAE7] p-4 gap-2 items-center border-t-0">
                      <Typography className="text-[#929FB8]">Carrier Acknowledgement Status:</Typography>
                      <Typography weight="semibold">{matched?.si_carrier_status || "N/A"}</Typography>
                      {matched?.si_carrier_status_date && (
                        <span className="text-[#4B5563]">
                          {" "}
                          <span className="font-bold">{"On "}</span> {formatDateGMT(matched.si_carrier_status_date)}{" "}
                          (GMT)
                        </span>
                      )}
                    </div>
                  </>
                );
              })()}
            {/* {details?.inttra_status?.acknowledgment_status && (
              <div className="flex border border-[#D3DAE7] p-4 gap-2 items-center border-t-0">
                <Typography>Carrier Acknowledgement Status:</Typography>
                <Typography weight={"semibold"}>
                  {details?.inttra_status?.acknowledgment_status} On
                </Typography>
                <span className="text-[#929FB8]">
                  {formatToCustomGMT(
                    details?.inttra_status?.acknowledgment_date_gmt
                  )}
                </span>
              </div>
            )} */}

            <div className="flex items-center space-x-2 py-5">
              <Checkbox id="terms" />
              <label
                htmlFor="terms"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                <Typography>This is a Consolidation Shipment containing both Master and House Bill data.</Typography>
              </label>
            </div>

            <Table className="border border-[#D3DAE7] bg-white">
              <TableBody>
                <TableRow>
                  <TableCell className="px-6 py-7 border-r border-[#D3DAE7] align-top" rowSpan={2}>
                    <div className="mb-3">
                      <Typography>
                        <span className="font-semibold">Shipper</span> ({details?.shipper_data?.inttra_company_id})
                      </Typography>
                      <Typography>{details?.shipper_data?.shipper_name}</Typography>
                      <Typography>{details?.shipper_data?.custom_address}</Typography>
                    </div>
                    <div className="mb-3">
                      {details?.shipper_data?.postal_code && (
                        <Typography>Postal Code: {details?.shipper_data?.postal_code}</Typography>
                      )}
                      {details?.shipper_data?.country && (
                        <Typography>Country: {details?.shipper_data?.country}</Typography>
                      )}
                    </div>

                    {details?.shipper_tax_id && (
                      <div className="mb-3">
                        <Typography weight={"semibold"}>Government Tax ID Number:</Typography>
                        <Typography>{details?.shipper_tax_id}</Typography>
                      </div>
                    )}

                    <div>
                      <Typography weight={"semibold"}>Contact</Typography>
                      {details?.shipper_data?.contact_name && (
                        <Typography>Name: {details?.shipper_data?.contact_name}</Typography>
                      )}
                      <Typography>Phone: {details?.shipper_data?.phone}</Typography>
                      {/* {details?.consignee?.fax && (
                  <Typography>Fax: {details?.consignee?.fax}</Typography>
                )} */}
                      <Typography>E-mail: {details?.shipper_email}</Typography>
                    </div>
                  </TableCell>
                  <TableCell className="px-6 py-7 align-top border-r border-[#D3DAE7]" rowSpan={2}>
                    <Typography weight={"semibold"}>Forwarder</Typography>
                    <div className="mb-3">
                      <Typography>{details?.forwarder}</Typography>
                    </div>
                    <div className="mb-3">
                      {details?.forwarder_address?.split("\n").map((line, idx) => (
                        <Typography key={idx}>{line}</Typography>
                      ))}
                    </div>
                  </TableCell>
                  <TableCell className="px-6 py-7 align-top border-r border-[#D3DAE7]">
                    <div className="mb-3">
                      <Typography weight={"semibold"}>Carrier</Typography>
                      <Typography>{details?.carrier_data?.partyname1}</Typography>
                    </div>
                    <div>
                      <Typography weight={"semibold"}>Carrier Booking Number</Typography>
                      <Typography>{details?.carrier_booking_number}</Typography>
                    </div>
                  </TableCell>
                  <TableCell className="px-6 py-7 align-top border-r border-[#D3DAE7]">
                    <div className="mb-3">
                      <Typography weight={"semibold"}>INTTRA SI Number</Typography>
                      <Typography>{details?.inttra_status?.shipping_instruction}</Typography>
                    </div>
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="px-6 py-7 align-top border-r border-[#D3DAE7]">
                    <div className="mb-3">
                      <Typography weight={"semibold"}>References</Typography>
                      {details?.purchase_order_number && (
                        <Typography>Purchase Order Number: {details?.purchase_order_number}</Typography>
                      )}
                      {details?.shipper_reference_number && (
                        <Typography>Shipper’s Reference Number: {details?.shipper_reference_number}</Typography>
                      )}
                      {details?.contract_reference_number && (
                        <Typography>Contract Reference Number: {details?.contract_reference_number}</Typography>
                      )}
                      {details?.exporter_reference_number && (
                        <Typography>
                          Exporter Reference Number (RUC Number): {details?.exporter_reference_number}
                        </Typography>
                      )}
                      {details?.consignee_order_number && (
                        <Typography>Consignee Order Number: {details?.consignee_order_number}</Typography>
                      )}
                      {details?.forwarder_order_number && (
                        <Typography>Forwarder Order Number: {details?.forwarder_order_number}</Typography>
                      )}
                      {details?.forwarder_reference_number && (
                        <Typography>Forwarder’s Reference Number: {details?.forwarder_reference_number}</Typography>
                      )}
                      {details?.bl_reference_number && (
                        <Typography>B/L Reference Number: {details?.bl_reference_number}</Typography>
                      )}
                      {details?.letter_of_credit_reference && (
                        <Typography>Letter of Credit Reference: {details?.letter_of_credit_reference}</Typography>
                      )}
                      {details?.transaction_number && (
                        <Typography>Transaction Number (ITN/MRN/DU-E): {details?.transaction_number}</Typography>
                      )}
                      {details?.unique_consignment_reference && (
                        <Typography>Unique Consignment Reference: {details?.unique_consignment_reference}</Typography>
                      )}
                      {details?.export_license_number && (
                        <Typography>Export Licence Number: {details?.export_license_number}</Typography>
                      )}
                      {details?.customs_house_broker_reference && (
                        <Typography>
                          Customs House Broker Reference: {details?.customs_house_broker_reference}
                        </Typography>
                      )}
                      {details?.government_reference_or_fmc_number && (
                        <Typography>
                          Government Reference or FMC Number: {details?.government_reference_or_fmc_number}
                        </Typography>
                      )}
                      {details?.pcin && <Typography>PCIN: {details?.pcin}</Typography>}
                      {details?.csn && <Typography>CSN: {details?.csn}</Typography>}
                      {details?.acid_number_mcin && (
                        <Typography>ACID Number, MCIN: {details?.acid_number_mcin}</Typography>
                      )}
                    </div>
                  </TableCell>
                  <TableCell className="px-6 py-7 align-top border-r border-[#D3DAE7]">
                    <Typography weight={"semibold"}>Status Date</Typography>
                    <Typography>{formatToCustomGMT(details?.creationdate)}</Typography>
                  </TableCell>
                </TableRow>

                <TableRow>
                  <TableCell className="px-6 py-7 border-r border-[#D3DAE7] align-top">
                    <Typography weight={"semibold"}>Consignee</Typography>
                    <div className="mb-3">
                      <Typography>{details?.consignee_data?.customer_name}</Typography>
                    </div>
                    <div className="mb-3">
                      <Typography>{details?.consignee__address}</Typography>
                      {/* <Typography>
                      {details?.consignee_data?.customer_state}{" "}
                    </Typography>
                    {details?.consignee_data?.customer_zip && (
                      <Typography>
                        Postal Code: {details?.consignee_data?.customer_zip}
                      </Typography>
                    )} */}
                    </div>
                    {details?.consignee_tax_id && (
                      <div className="mb-3">
                        <Typography weight={"semibold"}>Government Tax ID Number:</Typography>
                        <Typography>{details?.consignee_tax_id}</Typography>
                      </div>
                    )}
                    <div>
                      <Typography weight={"semibold"}>Contact</Typography>
                      {details?.consignee_data?.contact && (
                        <Typography>Name: {details?.consignee_data?.contact}</Typography>
                      )}
                      {details?.consignee_data?.phone && (
                        <Typography>Phone: {details?.consignee_data?.phone}</Typography>
                      )}
                      {details?.consignee_data?.fax && <Typography>Fax: {details?.consignee_data?.fax}</Typography>}
                      {details?.consignee_data?.email_id && (
                        <Typography>E-mail: {details?.consignee_data?.email_id}</Typography>
                      )}
                    </div>
                  </TableCell>

                  <TableCell className="px-6 py-7 border-r border-[#D3DAE7] align-top">
                    <Typography weight={"semibold"}>Notify Party</Typography>
                    <div className="mb-3">
                      <Typography> {details?.notify_party_data?.company_name} </Typography>
                    </div>
                    <div className="mb-3">
                      {details?.notify_party_data?.customer_address?.split("\n").map((line, idx) => (
                        <Typography key={idx}>{line}</Typography>
                      ))}
                      {details?.notify_party_data?.postal_code && (
                        <Typography>Postal Code: {details?.notify_party_data?.postal_code} </Typography>
                      )}
                    </div>

                    {details?.notify_party_tax_id && (
                      <div className="mb-3">
                        <Typography weight={"semibold"}>Government Tax ID Number:</Typography>
                        <Typography>{details?.notify_party_tax_id}</Typography>
                      </div>
                    )}

                    <Typography weight={"semibold"}>Contact</Typography>
                    <Typography>Name: {details?.notify_party_data?.customer_name} </Typography>
                    {details?.notify_party_data?.phone && (
                      <Typography>Phone: {details?.notify_party_data?.phone} </Typography>
                    )}
                    {/* {details?.notify_party_data?.fax && (
                      <Typography>
                        Phone: {details?.notify_party_data?.fax}{" "}
                      </Typography>
                    )} */}
                    <Typography>Email: {details?.notify_party_data?.email_id} </Typography>
                  </TableCell>
                  <TableCell className="px-6 py-7 align-top border-r border-[#D3DAE7]">
                    <Typography weight={"semibold"}>Additional Notify Party 1</Typography>
                    <div className="mb-3">
                      <Typography> {details?.additional_notify_party_1_data?.company_name} </Typography>
                    </div>
                    <div className="mb-3">
                      {details?.additional_notify_party_1_data?.customer_address?.split("\n").map((line, idx) => (
                        <Typography key={idx}>{line}</Typography>
                      ))}
                      {details?.additional_notify_party_1_data?.postal_code && (
                        <Typography>Postal Code: {details?.additional_notify_party_1_data?.postal_code} </Typography>
                      )}
                    </div>

                    <Typography weight={"semibold"}>Contact</Typography>
                    <Typography>Name: {details?.additional_notify_party_1_data?.customer_name} </Typography>
                    {details?.additional_notify_party_1_data?.phone && (
                      <Typography>Phone: {details?.additional_notify_party_1_data?.phone} </Typography>
                    )}

                    <Typography>Email: {details?.additional_notify_party_1_data?.email_id} </Typography>
                  </TableCell>
                  <TableCell className="px-6 py-7 align-top border-r border-[#D3DAE7]">
                    <Typography weight={"semibold"}>Additional Notify Party 2</Typography>
                    <div className="mb-3">
                      <Typography> {details?.additional_notify_party_2_data?.company_name} </Typography>
                    </div>
                    <div className="mb-3">
                      {details?.additional_notify_party_2_data?.customer_address?.split("\n").map((line, idx) => (
                        <Typography key={idx}>{line}</Typography>
                      ))}
                      {details?.additional_notify_party_2_data?.postal_code && (
                        <Typography>Postal Code: {details?.additional_notify_party_2_data?.postal_code} </Typography>
                      )}
                    </div>

                    <Typography weight={"semibold"}>Contact</Typography>
                    <Typography>Name: {details?.additional_notify_party_2_data?.customer_name} </Typography>
                    {details?.additional_notify_party_2_data?.phone && (
                      <Typography>Phone: {details?.additional_notify_party_2_data?.phone} </Typography>
                    )}

                    <Typography>Email: {details?.additional_notify_party_2_data?.email_id} </Typography>
                  </TableCell>
                </TableRow>

                <TableRow>
                  <TableCell className="px-6 py-7 align-top border-r border-[#D3DAE7]">
                    <Typography weight={"semibold"}>Contract Party</Typography>
                    <div className="mb-3">
                      {details?.contract_party?.[0]?.party_name && (
                        <Typography className="mb-3">{details.contract_party[0].party_name}</Typography>
                      )}

                      {!details?.contract_party?.[0]?.party_name && details?.contract_party_name_entry && (
                        <Typography className="mb-3">{details.contract_party_name_entry}</Typography>
                      )}

                      {details?.contract_party?.[0]?.address &&
                        details.contract_party[0].address
                          .split("\n")
                          .map((line, idx) => <Typography key={idx}>{line}</Typography>)}

                      {!details?.contract_party?.[0]?.address &&
                        details?.contract_party_address_entry &&
                        details.contract_party_address_entry
                          .split("\n")
                          .map((line, idx) => <Typography key={idx}>{line}</Typography>)}
                    </div>
                    {(details?.contract_party?.[0]?.phone ||
                      details?.contract_party?.[0]?.email ||
                      details?.contract_party?.[0]?.postal_code) && (
                      <div>
                        <Typography weight={"semibold"}>Contact</Typography>
                        {details?.contract_party?.[0]?.phone && (
                          <Typography>Phone: {details.contract_party[0].phone}</Typography>
                        )}
                        {details?.contract_party?.[0]?.email && (
                          <Typography>Email: {details.contract_party[0].email}</Typography>
                        )}
                        {details?.contract_party?.[0]?.postal_code && (
                          <Typography>Postal Code : {details.contract_party[0].postal_code}</Typography>
                        )}
                      </div>
                    )}
                  </TableCell>
                  <TableCell className="px-6 py-7 align-top border-r border-[#D3DAE7]">
                    <Typography weight={"semibold"}>Freight Payer</Typography>
                    <Typography>{details?.freight_payer} </Typography>
                    {details?.freight_payer_address?.split("\n").map((line, idx) => (
                      <Typography key={idx}>{line}</Typography>
                    ))}
                  </TableCell>
                  <TableCell className="px-6 py-7 align-top border-r border-[#D3DAE7]">
                    <Typography weight={"semibold"}>Manufacturer / Supplier</Typography>
                    <Typography>{details?.manufacturersupplier} </Typography>
                    {details?.manufacturersupplier_address?.split("\n").map((line, idx) => (
                      <Typography key={idx}>{line}</Typography>
                    ))}
                  </TableCell>
                  <TableCell className="px-6 py-7 align-top border-r border-[#D3DAE7]">
                    <Typography weight={"semibold"}>Ship To</Typography>
                  </TableCell>
                </TableRow>

                <TableRow>
                  <TableCell className="px-6 py-7 align-top border-r border-[#D3DAE7]">
                    <Typography weight={"semibold"}>Consolidator / Stuffer</Typography>
                    <Typography>{details?.consolidatorstuffer} </Typography>
                    {details?.consolidatorstuffer_address?.split("\n").map((line, idx) => (
                      <Typography key={idx}>{line}</Typography>
                    ))}
                  </TableCell>
                  <TableCell className="px-6 py-7 align-top border-r border-[#D3DAE7]">
                    <Typography weight={"semibold"}>Importer</Typography>
                    <Typography>{details?.importer} </Typography>
                    {details?.importer_address?.split("\n").map((line, idx) => (
                      <Typography key={idx}>{line}</Typography>
                    ))}
                  </TableCell>
                  <TableCell className="px-6 py-7 align-top border-r border-[#D3DAE7]">
                    <Typography weight={"semibold"}>WareHouse Keeper</Typography>
                    <Typography>{details?.warehouse_keeper} </Typography>
                    {details?.warehouse_keeper_address?.split("\n").map((line, idx) => (
                      <Typography key={idx}>{line}</Typography>
                    ))}
                  </TableCell>
                  <TableCell className="px-6 py-7 align-top border-r border-[#D3DAE7]">
                    <Typography weight={"semibold"}></Typography>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>

            <Table className="border border-[#D3DAE7] mt-6">
              <TableHeader>
                <TableRow>
                  <TableHead className="px-4 py-4" colSpan={4}>
                    <Typography variant={"h4"} weight={"medium"}>
                      Transport
                    </Typography>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  <TableCell className="px-6 py-7 align-top border-r border-[#D3DAE7] w-[24%]">
                    <Typography weight={"semibold"}>Move Type</Typography>
                    <Typography>{details?.move_type}</Typography>
                  </TableCell>
                  <TableCell className="px-6 py-7 align-top border-r border-[#D3DAE7] w-[24%]">
                    <Typography weight={"semibold"}>Shipment Type</Typography>
                    <Typography>{details?.shipment_type}</Typography>
                  </TableCell>
                  <TableCell className="px-6 py-7 align-top border-r border-[#D3DAE7] w-[24%]">
                    <Typography weight={"semibold"}>Vessel, Voyage, IMO Number</Typography>
                    {(details?.vessel || details?.voyage || details?.imo_number) && (
                      <Typography>
                        {details?.vessel ?? "—"}, {details?.voyage ?? "—"}, {details?.imo_number ?? "—"}
                      </Typography>
                    )}
                  </TableCell>
                  <TableCell className="px-6 py-7 align-top border-r border-[#D3DAE7] w-[28%]"></TableCell>
                </TableRow>

                <TableRow>
                  <TableCell className="px-6 py-7 align-top border-r border-[#D3DAE7] w-[24%]">
                    <div className="mb-3">
                      <Typography weight={"semibold"}>Origin</Typography>
                      <Typography>City: {details?.origin_place_of_carrier_receipt_data?.location_name}</Typography>
                      <Typography>Country: {details?.origin_place_of_carrier_receipt_data?.country}</Typography>
                      <Typography>Location ID: {details?.origin_place_of_carrier_receipt_data?.locode}</Typography>
                    </div>
                    <div>
                      <Typography weight={"semibold"}>Print on B/L as</Typography>
                      <Typography>{details?.origin_place_of_carrier_receipt_data?.location_name}</Typography>
                    </div>
                  </TableCell>
                  <TableCell className="px-6 py-7 align-top border-r border-[#D3DAE7] w-[24%]">
                    <div className="mb-3">
                      <Typography weight={"semibold"}>Port of Load</Typography>
                      <Typography>City: {details?.port_of_load_data?.location_name}</Typography>
                      <Typography>Country: {details?.port_of_load_data?.country}</Typography>
                      <Typography>Location ID: {details?.port_of_load_data?.locode}</Typography>
                    </div>
                    <div>
                      <Typography weight={"semibold"}>Print on B/L as</Typography>
                      <Typography>{details?.port_of_load_data?.location_name}</Typography>
                    </div>
                  </TableCell>
                  <TableCell className="px-6 py-7 align-top border-r border-[#D3DAE7] w-[24%]">
                    <div className="mb-3">
                      <Typography weight={"semibold"}>Port of Discharge</Typography>
                      <Typography>City: {details?.port_of_discharge_data?.location_name}</Typography>
                      <Typography>Country: {details?.port_of_discharge_data?.country}</Typography>
                      <Typography>Location ID: {details?.port_of_discharge_data?.locode}</Typography>
                    </div>
                    <div>
                      <Typography weight={"semibold"}>Print on B/L as</Typography>
                      <Typography>{details?.port_of_discharge_data?.location_name}</Typography>
                    </div>
                  </TableCell>
                  <TableCell className="px-6 py-7 align-top border-r border-[#D3DAE7] w-[24%]">
                    <div className="mb-3">
                      <Typography weight={"semibold"}>Destination</Typography>
                      <Typography>
                        City: {details?.destination_place_of_carrier_delivery_data?.location_name}
                      </Typography>
                      <Typography>Country: {details?.destination_place_of_carrier_delivery_data?.country}</Typography>
                      <Typography>
                        Location ID: {details?.destination_place_of_carrier_delivery_data?.locode}
                      </Typography>
                    </div>
                    <div>
                      <Typography weight={"semibold"}>Print on B/L as</Typography>
                      <Typography>{details?.destination_place_of_carrier_delivery_data?.location_name}</Typography>
                    </div>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>

            <div className="mt-8">
              <RadioGroup>
                <Table className="border border-[#D3DAE7]">
                  <TableHeader>
                    <TableRow>
                      <TableHead className="px-4 py-4">
                        <Typography variant={"h4"} weight={"medium"}>
                          Particulars
                        </Typography>
                      </TableHead>
                    </TableRow>
                    <TableRow>
                      <TableHead className="px-6 py-7 align-top border-r border-r-[#D3DAE7] w-[24%]">
                        <Typography weight={"semibold"}>Container</Typography>
                      </TableHead>
                      <TableHead className="px-6 py-7 align-top border-r border-r-[#D3DAE7] w-[12%]">
                        <Typography weight={"semibold"}>GID Seq. / Packing Level</Typography>
                      </TableHead>
                      <TableHead className="px-6 py-7 align-top border-r border-r-[#D3DAE7] w-[10%]">
                        <Typography weight={"semibold"}>Marks & Numbers</Typography>
                      </TableHead>
                      <TableHead className="px-6 py-7 align-top border-r border-r-[#D3DAE7] w-[14%]">
                        <Typography weight={"semibold"}>Package Count/Type</Typography>
                      </TableHead>
                      <TableHead className="px-6 py-7 align-top border-r border-r-[#D3DAE7] w-[20%]">
                        <Typography weight={"semibold"}>Cargo Description</Typography>
                      </TableHead>
                      <TableHead className="px-6 py-7 align-top border-r border-r-[#D3DAE7] w-[8%]">
                        <Typography weight={"semibold"}>Cargo Gross Weight</Typography>
                      </TableHead>
                      <TableHead className="px-6 py-7 align-top border-r border-r-[#D3DAE7] w-[12%]">
                        <Typography weight={"semibold"}>Cargo Gross Volume</Typography>
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {details?.equipment_list?.map((item, index) => (
                      <TableRow key={index}>
                        <TableCell className="px-6 py-7 align-top border-r border-r-[#D3DAE7]">
                          <Typography weight={"semibold"} className="mb-4">
                            {item.equipment_name}
                          </Typography>
                          <Typography className="mb-4">
                            Container Type: {item.description}
                            {/* ({item.code_value}) */}
                          </Typography>
                          <div className="mb-4">
                            <Typography weight={"semibold"}>Container References</Typography>
                            <Typography>Customs Release Code: {item.cargo?.[0]?.cus_code}</Typography>
                            <Typography weight={"semibold"}>Seal Numbers</Typography>
                            <div className="mb-2">
                              <Typography>Carrier Seal Number: {item.carrier_seal_number}</Typography>
                            </div>
                            <Typography>Shipper Seal Number: {item.shipper_seal_number}</Typography>
                          </div>
                          <div>
                            <Typography weight={"semibold"}>Reefer Details</Typography>
                            {item.comment && <Typography>Container Comments: {item.comment}</Typography>}
                            {item.weight_value && (
                              <Typography>
                                Container Net Weight: {item.weight_value} {item.weight_type}
                              </Typography>
                            )}
                            {item.gross_weight && (
                              <Typography>Container Gross Volume: {item.gross_weight} Cbm</Typography>
                            )}
                            {item.supplier_type && <Typography>Container Supplier: {item.supplier_type}</Typography>}
                            {item.wood_declaration && (
                              <Typography>Wood Declaration: {item.wood_declaration}</Typography>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="px-6 py-7 align-top border-r border-r-[#D3DAE7]">
                          <Typography>(1) Outer</Typography>
                        </TableCell>

                        <TableCell className="px-6 py-7 align-top border-r border-r-[#D3DAE7]">
                          {item.cargo?.map((cargos) => (
                            <Typography>{cargos.marks_and_numbers}</Typography>
                          ))}
                        </TableCell>

                        <TableCell className="px-6 py-7 align-top border-r border-r-[#D3DAE7]">
                          {item.cargo?.map((cargos) => (
                            <Typography>
                              {cargos.package_count} {cargos.package_counttype_outermost}
                            </Typography>
                          ))}
                        </TableCell>

                        <TableCell className="px-6 py-7 align-top border-r border-r-[#D3DAE7]">
                          <div className="mb-4">
                            {/* {item.cargo?.map((cargos) => (
                              <Typography>{cargos.name}</Typography>
                            ))} */}
                            {item.cargo?.map((cargos) => (
                              <Typography className="text-wrap">{cargos.cargo_description}</Typography>
                            ))}
                          </div>
                          {item.cargo?.map((cargos) => (
                            <Typography>HS Code: {cargos.hs_code}</Typography>
                          ))}
                          {details?.purchase_order_number && (
                            <Typography>Purchase Order Number: {details?.purchase_order_number}</Typography>
                          )}
                        </TableCell>
                        <TableCell className="px-6 py-7 align-top border-r border-r-[#D3DAE7]">
                          <Typography>Per Container:</Typography>
                          {item.cargo?.map((cargos) => (
                            <Typography className="text-wrap">
                              {cargos.cargo_gross_weight} {cargos.net_weight_unit}
                            </Typography>
                          ))}
                        </TableCell>
                        <TableCell className="px-6 py-7 align-top ">
                          <Typography>Per Container:</Typography>
                          {item.cargo?.map((cargos) => (
                            <Typography className="text-wrap">
                              {cargos.gross_volume} {cargos.gross_volume_unit}
                            </Typography>
                          ))}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </RadioGroup>
            </div>

            <Table className="mt-8 border border-[#D3DAE7]">
              <TableHeader>
                <TableRow>
                  <TableHead className="px-4 py-4">
                    <Typography variant={"h4"} weight={"medium"}>
                      Control Totals
                    </Typography>
                  </TableHead>
                </TableRow>
                <TableRow>
                  <TableHead className="p-5 align-top border-r border-r-[#D3DAE7] w-[25%]">
                    <Typography weight={"semibold"}>Total Number of Containers</Typography>
                  </TableHead>
                  <TableHead className="p-5 align-top border-r border-r-[#D3DAE7] w-[25%]">
                    <Typography weight={"semibold"}>Total Number of Packages</Typography>
                  </TableHead>
                  <TableHead className="p-5 align-top border-r border-r-[#D3DAE7] w-[25%]">
                    <Typography weight={"semibold"}>Total Shipment Weight</Typography>
                  </TableHead>
                  <TableHead className="p-5 align-top border-r border-r-[#D3DAE7] w-[25%]">
                    <Typography weight={"semibold"}>Total Shipment Volume</Typography>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  <TableCell className="p-5 align-top border-r border-r-[#D3DAE7] w-[25%]">
                    <Typography>{details?.total_number_of_containers}</Typography>
                  </TableCell>
                  <TableCell className="p-5 align-top border-r border-r-[#D3DAE7] w-[25%]">
                    <Typography>{details?.total_number_of_packages}</Typography>
                  </TableCell>
                  <TableCell className="p-5 align-top border-r border-r-[#D3DAE7] w-[25%]">
                    <Typography>{details?.total_shipment_weight} Kgs</Typography>
                  </TableCell>
                  <TableCell className="p-5 align-top border-r border-r-[#D3DAE7] w-[25%]">
                    <Typography>{details?.total_shipment_volume} Cbm</Typography>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>

            <Table className="mt-8 border border-[#D3DAE7]">
              <TableHeader>
                <TableRow>
                  <TableHead className="px-4 py-4">
                    <Typography variant={"h4"} weight={"medium"}>
                      Customs Compliance
                    </Typography>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  <TableCell className="p-5 align-top border border-[#D3DAE7]" colSpan={2}>
                    <Typography className="font-medium">ICS2 ENS Full Filer Details</Typography>
                    <Typography>{details?.ics2_ens_full_filer_details}</Typography>
                  </TableCell>
                </TableRow>

                <TableRow>
                  <TableCell className="p-5 align-top border border-[#D3DAE7]" colSpan={2}>
                    <Typography>
                      Goods delivered in the EU, Norway, Switzerland and Northern Ireland:{" "}
                      {details?.goods_delivered_in_eu ?? "No"}
                    </Typography>
                  </TableCell>
                </TableRow>

                <TableRow>
                  <TableCell className="p-5 align-top border border-[#D3DAE7]" colSpan={2}>
                    <Typography>
                      House bill(s) issued for this shipment: {details?.house_bill_issued_for_shipment}
                    </Typography>
                  </TableCell>
                </TableRow>
                {details?.do_you_prefer_the_carrier_to_declare_the_entry_summary_ens === "Self" ? (
                  <TableRow>
                    <TableCell className="p-5 align-top border border-[#D3DAE7] w-full">
                      <Typography className="font-medium">Supplimentary Declarant EORI</Typography>
                      <Typography>{details?.eori}</Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  ""
                )}
                <TableRow>
                  <TableCell className="p-5 align-top border border-[#D3DAE7] w-1/2">
                    <Typography className="font-medium">Seller</Typography>
                    <Typography>{details?.seller}</Typography>
                  </TableCell>
                  <TableCell className="p-5 align-top border border-[#D3DAE7] w-1/2">
                    <Typography className="font-medium">Buyer</Typography>
                    <Typography>{details?.buyer}</Typography>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>

            {/* <Table className="mt-8 border border-[#D3DAE7]">
              <TableHeader>
                <TableRow>
                  <TableHead className="p-5 border-r border-r-[#D3DAE7]">
                    <Typography weight={"semibold"}>Seller</Typography>
                  </TableHead>
                  <TableHead className="p-5 border-r border-r-[#D3DAE7]">
                    <Typography weight={"semibold"}>Buyer</Typography>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  <TableCell className="p-5 border-r border-r-[#D3DAE7]"></TableCell>
                  <TableCell className="p-5 border-r border-r-[#D3DAE7]"></TableCell>
                </TableRow>
              </TableBody>
            </Table> */}
            {details?.do_you_prefer_the_carrier_to_declare_the_entry_summary_ens === "Carrier" &&
            details?.have_house_bills_been_issued_for_this_shipment === "One" &&
            details?.house_bill_number ? (
              <Table className="mt-8 border border-[#D3DAE7]">
                <TableHeader>
                  <TableRow>
                    <TableHead className="px-4 py-4" colSpan={5}>
                      <Typography variant={"h4"} weight={"medium"}>
                        House Bill Compliance Information
                      </Typography>
                    </TableHead>
                  </TableRow>
                  <TableRow>
                    <TableHead className="px-6 py-7 align-top border-r border-r-[#D3DAE7]">
                      <Typography weight={"semibold"}>House Bill Information</Typography>
                    </TableHead>
                    <TableHead className="px-6 py-7 align-top border-r border-r-[#D3DAE7]">
                      <Typography weight={"semibold"}>Locations</Typography>
                    </TableHead>
                    <TableHead className="px-6 py-7 align-top" colSpan={3}>
                      <Typography weight={"semibold"}>Filing Information</Typography>
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell className="px-6 py-7 align-top border-r border-r-[#D3DAE7] w-[24%]">
                      <Typography weight={"semibold"}>
                        HBL Number:
                        <span className="font-normal">{details?.house_bill_number || ""}</span>
                      </Typography>
                      <Typography weight={"semibold"}>
                        Method of Payment:
                        <span className="font-normal">{details?.method_of_payment || ""}</span>
                      </Typography>
                      <Typography weight={"semibold"}>
                        Countries of Routing of Consignment:
                        <span className="font-normal">{details?.countries_visited_in_between || ""}</span>
                      </Typography>
                      <Typography weight={"semibold"}>
                        To Order Shipments:{details?.to_order_indicator === 0 ? "Not Provided" : ""}
                      </Typography>
                    </TableCell>
                    <TableCell className="px-6 py-7 align-top border-r border-r-[#D3DAE7] w-[26%]">
                      <Typography weight={"semibold"}>Place of Acceptance:</Typography>
                      {details?.place_of_acceptance_origin_of_goods && (
                        <div className="mb-10">
                          <Typography>
                            City: {details?.place_of_acceptance_origin_of_goods_data?.location_name}
                          </Typography>
                          <Typography>
                            {" "}
                            Country: {details?.place_of_acceptance_origin_of_goods_data?.country}
                          </Typography>
                          <Typography>Location: {details?.place_of_acceptance_origin_of_goods_data?.locode}</Typography>
                        </div>
                      )}
                      <Typography weight={"semibold"}>Place of Final Delivery:</Typography>
                      {details?.place_of_final_delivery && (
                        <div className="mb-10">
                          <Typography>City: {details?.place_of_final_delivery_data?.location_name}</Typography>
                          <Typography>Country: {details?.place_of_final_delivery_data?.country}</Typography>
                          <Typography>Location: {details?.place_of_final_delivery_data?.locode}</Typography>
                        </div>
                      )}
                    </TableCell>
                    <TableCell className="px-6 py-7 align-top border-r border-r-[#D3DAE7] w-[25%]">
                      <Typography weight={"semibold"}>Actual Shipper</Typography>
                      {(function () {
                        const shipper = details?.ics2_parties?.find((item) => item?.party_type === "Shipper");
                        if (!shipper) return <></>;
                        return (
                          <div className="">
                            {shipper?.party_name ? <Typography>{shipper?.party_name}</Typography> : ""}
                            {shipper?.street_name ? <Typography>Street: {shipper?.street_name}</Typography> : ""}
                            <br />
                            {shipper?.street_number ? (
                              <Typography>Street Number:{shipper?.street_number}</Typography>
                            ) : (
                              ""
                            )}
                            <br />
                            {shipper?.city ? <Typography>City:{shipper?.city}</Typography> : ""}
                            <br />
                            {shipper?.state ? <Typography>State: {shipper?.state}</Typography> : ""}
                            {shipper?.postal_code ? <Typography>Postal Code:{shipper?.postal_code}</Typography> : ""}
                            {shipper?.country ? <Typography>Country: {shipper?.country}</Typography> : ""}
                            <br />
                            {shipper?.country ? <Typography>PO Box: {shipper?.po_box}</Typography> : ""}

                            {shipper?.tax_id_eori ? (
                              <div className="mt-4">
                                <Typography weight={"semibold"}>Government Tax ID Number</Typography>
                                <Typography>{shipper?.tax_id_eori}</Typography>
                              </div>
                            ) : (
                              ""
                            )}
                            {shipper?.contact_name ? (
                              <div className="mt-4">
                                <Typography weight={"semibold"}>Contact</Typography>
                                <Typography>{shipper?.contact_name}</Typography>
                              </div>
                            ) : (
                              ""
                            )}
                          </div>
                        );
                      })()}
                    </TableCell>
                    <TableCell className="px-6 py-7 align-top border-r border-r-[#D3DAE7] w-[25%]">
                      <Typography weight={"semibold"}>Actual Consignee</Typography>
                      {(function () {
                        const consignee = details?.ics2_parties?.find((item) => item?.party_type === "Consignee");
                        if (!consignee) return <></>;
                        return (
                          <div className="">
                            {consignee?.party_name ? <Typography>{consignee?.party_name}</Typography> : ""}
                            {consignee?.street_name ? <Typography>Street: {consignee?.street_name}</Typography> : ""}
                            <br />
                            {consignee?.street_number ? (
                              <Typography>Street Number:{consignee?.street_number}</Typography>
                            ) : (
                              ""
                            )}
                            <br />
                            {consignee?.city ? <Typography>City:{consignee?.city}</Typography> : ""}
                            <br />
                            {consignee?.state ? <Typography>State: {consignee?.state}</Typography> : ""}
                            {consignee?.postal_code ? (
                              <Typography>Postal Code:{consignee?.postal_code}</Typography>
                            ) : (
                              ""
                            )}
                            {consignee?.country ? <Typography>Country: {consignee?.country}</Typography> : ""}
                            <br />
                            {consignee?.country ? <Typography>PO Box: {consignee?.po_box}</Typography> : ""}
                            {consignee?.tax_id_eori ? (
                              <div className="mt-4">
                                <Typography weight={"semibold"}>Government Tax ID Number</Typography>
                                <Typography>{consignee?.tax_id_eori}</Typography>
                              </div>
                            ) : (
                              ""
                            )}
                            {consignee?.contact_name ? (
                              <div className="mt-4">
                                <Typography weight={"semibold"}>Contact</Typography>
                                <Typography>{consignee?.contact_name}</Typography>
                              </div>
                            ) : (
                              ""
                            )}
                          </div>
                        );
                      })()}
                    </TableCell>
                    {/* <TableCell className="px-6 py-7 align-top w-[16%]">
                    <Typography weight={"semibold"}>Actual Notify Party</Typography>
                  </TableCell> */}
                  </TableRow>
                </TableBody>
              </Table>
            ) : (
              ""
            )}

            <Table className="border border-[#D3DAE7] mt-8">
              <TableHeader>
                <TableRow>
                  <TableHead className="px-4 py-4" colSpan={4}>
                    <Typography variant={"h4"} weight={"medium"}>
                      Freight Charges
                    </Typography>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  <TableCell className="px-6 py-7 align-top border-r border-[#D3DAE7] w-[24%]">
                    <Typography weight={"semibold"}>Shipper's Declared Value</Typography>
                    <Typography>
                      {details?.shippers__declared_value} {details?.currency_type_data?.alphabetic_code}
                    </Typography>
                  </TableCell>
                  <TableCell className="px-6 py-7 align-top border-r border-[#D3DAE7] w-[24%]">
                    <Typography weight={"semibold"}>B/L Release Office</Typography>
                    <Typography>City: {details?.bl_release_office__location_data?.location_name}</Typography>
                    <Typography>
                      Country:
                      {details?.bl_release_office__location_data?.country}
                    </Typography>
                    <Typography>Location ID: {details?.bl_release_office__location_data?.locode}</Typography>
                  </TableCell>
                  <TableCell className="px-6 py-7 align-top border-r border-[#D3DAE7] w-[24%]">
                    <Typography weight={"semibold"}>Print on B/L as</Typography>
                    <Typography>City: {details?.print_on_bl_aslocation_data?.location_name}</Typography>
                    <Typography>Country: {details?.print_on_bl_aslocation_data?.country}</Typography>
                    <Typography>Location ID: {details?.print_on_bl_aslocation_data?.locode}</Typography>
                  </TableCell>
                  <TableCell className="px-6 py-7 align-top border-r border-[#D3DAE7] w-[28%]">
                    <Typography weight={"semibold"}>Requested Date of Issue</Typography>
                    <Typography>{details?.requested_date_of_issue}</Typography>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>

            <Table className="border border-[#D3DAE7] mt-8">
              <TableHeader>
                <TableRow>
                  <TableHead className="px-6 py-7 align-top border-r border-r-[#D3DAE7] w-[25%]">
                    <Typography weight={"semibold"}>Charge Type</Typography>
                  </TableHead>
                  <TableHead className="px-6 py-7 align-top border-r border-r-[#D3DAE7] w-[25%]">
                    <Typography weight={"semibold"}>Freight Term</Typography>
                  </TableHead>
                  <TableHead className="px-6 py-7 align-top border-r border-r-[#D3DAE7] w-[25%]">
                    <Typography weight={"semibold"}>Payer</Typography>
                  </TableHead>
                  <TableHead className="px-6 py-7 align-top border-r border-r-[#D3DAE7] w-[25%]">
                    <Typography weight={"semibold"}>Payment Location</Typography>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {details?.freight_charges?.map((charge, index) => (
                  <TableRow key={index}>
                    <TableCell className="px-6 py-7 align-top border-r border-r-[#D3DAE7] w-[25%]">
                      <Typography>{charge.charge_type}</Typography>
                    </TableCell>
                    <TableCell className="px-6 py-7 align-top border-r border-r-[#D3DAE7] w-[25%]">
                      <Typography>{charge.freight_term}</Typography>
                    </TableCell>
                    <TableCell className="px-6 py-7 align-top border-r border-r-[#D3DAE7] w-[25%]">
                      <Typography>{charge.payer}</Typography>
                    </TableCell>
                    <TableCell className="px-6 py-7 align-top border-r border-r-[#D3DAE7] w-[25%]">
                      <Typography>
                        {charge.payment_location_data?.location_name}, {charge.payment_location_data?.locode}
                      </Typography>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>

            <Table className="border border-[#D3DAE7] mt-8">
              <TableHeader>
                <TableRow>
                  <TableHead className="px-4 py-4">
                    <Typography variant={"h4"} weight={"medium"}>
                      Documentation
                    </Typography>
                  </TableHead>
                </TableRow>
                <TableRow>
                  <TableHead className="px-4 py-4">
                    <Typography weight={"medium"}>Clauses</Typography>
                  </TableHead>
                </TableRow>
                <TableRow>
                  <TableHead className="px-4 py-4">
                    <Typography>{details?.documentation_clauses}</Typography>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  <TableCell className="px-6 py-7">
                    <Typography weight={"medium"} className="mb-4">
                      Measure for B/L Production:
                    </Typography>
                    <Typography className="mb-4">Documentation parameters as requested by submitting party:</Typography>
                    <div className="mb-4">
                      <Typography weight={"medium"}>Letter of Credit Reference</Typography>
                      <Typography>{details?.letter_of_credit_reference || "Not Provided"}</Typography>
                      {details?.lcr_issue_date && (
                        <Typography>Issue Date: {formatDate(details?.lcr_issue_date)}</Typography>
                      )}
                      {details?.lcr_expiry_date && (
                        <Typography>Expiry Date: {formatDate(details?.lcr_expiry_date)}</Typography>
                      )}
                    </div>
                    <div className="mb-4">
                      <Typography weight={"medium"}>Export License Number</Typography>
                      <Typography>{details?.export_license_number || "Not Provided"}</Typography>
                      {details?.eln_issue_date && (
                        <Typography>Issue Date: {formatDate(details?.eln_issue_date)}</Typography>
                      )}
                      {details?.eln_expiry_date && (
                        <Typography>Expiry Date: {formatDate(details?.eln_expiry_date)}</Typography>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>

            <Table className="mt-8 border border-[#D3DAE7]">
              <TableHeader>
                <TableRow>
                  <TableHead className="px-4 py-4" colSpan={5}>
                    <Typography variant={"h5"} weight={"medium"}>
                      B/L Print Instructions
                    </Typography>
                  </TableHead>
                </TableRow>
                <TableRow>
                  <TableHead className="p-5 align-top border-r border-r-[#D3DAE7]">
                    <Typography weight={"semibold"}>B/L Type</Typography>
                  </TableHead>
                  <TableHead className="p-5 align-top border-r border-r-[#D3DAE7]">
                    <Typography weight={"semibold"}>Freighted</Typography>
                  </TableHead>
                  <TableHead className="p-5 align-top border-r border-r-[#D3DAE7]">
                    <Typography weight={"semibold"}>Unfreighted</Typography>
                  </TableHead>
                  {/* <TableHead className="p-5 align-top">
                    <Typography weight={"semibold"}>B/L Comments</Typography>
                  </TableHead> */}
                </TableRow>
              </TableHeader>
              <TableBody>
                {details?.bill_type && (
                  <TableRow>
                    <TableCell
                      className="p-5 align-top border border-[#D3DAE7]"
                      // colSpan={1}
                    >
                      <Typography>{details?.bill_type} </Typography>
                    </TableCell>
                    <TableCell className="p-5 align-top border border-[#D3DAE7]">
                      <Typography>{details?.freightedno_of_documents}</Typography>
                    </TableCell>
                    <TableCell className="p-5 align-top border border-[#D3DAE7]">
                      <Typography>{details?.unfreightedno_of_documents}</Typography>
                    </TableCell>
                    {/* <TableCell className="p-5 align-top border border-[#D3DAE7]">
                    <Typography>{details?.bl_comments}</Typography>
                  </TableCell> */}
                  </TableRow>
                )}

                <TableRow>
                  <TableCell
                    className="p-5 align-top border border-[#D3DAE7]"
                    // colSpan={1}
                  >
                    <Typography>{"Non-Negotiable"} :</Typography>
                  </TableCell>
                  <TableCell className="p-5 align-top border border-[#D3DAE7]">
                    <Typography>{details?.non_negotiable_freightedno_of_copies}</Typography>
                  </TableCell>
                  <TableCell className="p-5 align-top border border-[#D3DAE7]">
                    <Typography>{details?.non_negotiable_unfreighted_no_of_copies}</Typography>
                  </TableCell>
                  {/* <TableCell className="p-5 align-top border border-[#D3DAE7]">
                    <Typography>{details?.bl_comments}</Typography>
                  </TableCell> */}
                </TableRow>
                <TableRow>
                  <TableCell className="p-5 align-top" colSpan={5}>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="terms" />
                      <label
                        htmlFor="terms"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        <Typography>This is a Standalone House Bill.</Typography>
                      </label>
                    </div>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>

            <Table className="mt-8 border border-[#D3DAE7]">
              <TableHeader>
                <TableRow>
                  <TableHead className="px-4 py-4">
                    <Typography variant={"h4"} weight={"medium"}>
                      Partner Notification Emails
                    </Typography>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  <TableCell className="px-6 py-7">
                    <Typography>{details?.partner_notification_emails}</Typography>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>

            {/* <Table className="mt-8 border border-[#D3DAE7]">
              <TableHeader>
                <TableRow>
                  <TableHead className="px-4 py-4">
                    <Typography variant={"h4"} weight={"medium"}>
                      House Bill Compliance Information
                    </Typography>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  <TableCell className="p-5 align-top border border-[#D3DAE7]">
                    <Typography className="font-medium">House Bill Information</Typography>
                    <Typography>
                      <strong>HBL Number:</strong> {details?.hbl_number}
                    </Typography>
                    <Typography>
                      <strong>Method of Payment:</strong> {details?.method_of_payment}
                    </Typography>
                    <Typography>
                      <strong>Countries of Routing of Consignment:</strong> {details?.routing_of_consigment}
                    </Typography>
                    <Typography>
                      <strong>To Order Shipment:</strong> {details?.to_order_shipment}
                    </Typography>
                  </TableCell>

                  <TableCell className="p-5 align-top border border-[#D3DAE7]">
                    <Typography className="font-medium">Locations</Typography>
                    <Typography>
                      <strong>Place of Acceptance:</strong> {details?.place_of_acceptance}
                    </Typography>
                    <Typography>
                      <strong>Place of Final Delivery:</strong> {details?.place_of_final_delivery}
                    </Typography>
                  </TableCell>

                  <TableCell className="p-5 align-top border border-[#D3DAE7]">
                    <Typography className="font-medium">Filing Information</Typography>
                    <Typography>
                      <strong>Actual Shipper:</strong> {details?.actual_shipper}
                    </Typography>
                    <Typography>
                      <strong>Actual Consignee:</strong> {details?.actual_consignee}
                    </Typography>
                    <Typography>
                      <strong>Government Tax ID Number:</strong> {details?.government_tax_id}
                    </Typography>
                    <Typography>
                      <strong>Contact:</strong> {details?.contact_name} <br />
                      Phone: {details?.contact_phone} <br />
                      Fax: {details?.contact_fax} <br />
                      Email: {details?.contact_email}
                    </Typography>
                  </TableCell>

                  <TableCell className="p-5 align-top border border-[#D3DAE7]">
                    <Typography className="font-medium">Actual Notify Party</Typography>
                    <Typography>{details?.actual_notify_party}</Typography>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table> */}

            {/* <Table className="mt-8 border border-[#D3DAE7]">
              <TableHeader>
                <TableRow>
                  <TableHead className="px-4 py-4">
                    <Typography variant={"h4"} weight={"medium"}>
                      History
                    </Typography>
                  </TableHead>
                </TableRow>
                <TableRow>
                  <TableHead className="p-5 align-top border-r border-r-[#D3DAE7] w-[25%]">
                    <Typography weight={"semibold"}>Version</Typography>
                  </TableHead>
                  <TableHead className="p-5 align-top border-r border-r-[#D3DAE7] w-[25%]">
                    <Typography weight={"semibold"}>
                      SI Requestor Company
                    </Typography>
                  </TableHead>
                  <TableHead className="p-5 align-top border-r border-r-[#D3DAE7] w-[25%]">
                    <Typography weight={"semibold"}>Modified by</Typography>
                  </TableHead>
                  <TableHead className="p-5 align-top border-r border-r-[#D3DAE7] w-[25%]">
                    <Typography weight={"semibold"}>
                      Modified Date (GMT)
                    </Typography>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow className="bg-[#F9FAFC]">
                  <TableCell className="p-5 align-top border-r border-r-[#D3DAE7] w-[25%]">
                    <Typography>1.0</Typography>
                  </TableCell>
                  <TableCell className="p-5 align-top border-r border-r-[#D3DAE7] w-[25%]">
                    <Typography>WESTSIDE EXPORTS LLC</Typography>
                  </TableCell>
                  <TableCell className="p-5 align-top border-r border-r-[#D3DAE7] w-[25%]">
                    <Typography>Vinu Kaliyar</Typography>
                  </TableCell>
                  <TableCell className="p-5 align-top border-r border-r-[#D3DAE7] w-[25%]">
                    <Typography>Vinu Kaliyar</Typography>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table> */}

            <div className="mt-8">
              <Typography variant={"small"} className="leading-6">
                This shipping instruction is subject to the INTTRA Legal Terms and Conditions which are accessible on
                the INTTRA Website at{" "}
                <a className="text-[#F87343] underline" target="_blank" href="https://www.inttra.com/">
                  www.inttra.com
                </a>{" "}
                under “Legal Terms and Conditions”
              </Typography>
              <Typography variant={"small"} className="leading-6">
                Bookings, booking responses/confirmation, shipping instructions and shipment/carriage are subject to the
                terms, conditions and exceptions of the Carrier's contract of carriage. These terms and conditions and
                exceptions are available upon request from the Carrier or its representatives and may be accessible on
                the carrier's website.
              </Typography>
            </div>
          </div>
        </div>
      ) : (
        <p>No data found</p>
      )}
    </div>
  );
}

export default ShippingInstructionConfirmationView;
