// shadcn
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
// custom component
import { Typography } from "@/components/typography";
import { useFormContext } from "react-hook-form";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

const CustomsComplianceSection = () => {
  const { control, watch, setValue } = useFormContext();
  return (
    <Card className="py-10">
      <CardHeader>
        <Typography variant={"muted"} weight={"medium"} className="text-base">
          Government Tax IDs
        </Typography>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-4 gap-5 ">
          <FormField
            control={control}
            name="shipperTaxId"
            render={({ field }) => (
              <FormItem>
                <FormLabel className=""> Shipper </FormLabel>
                <FormControl>
                  <Input type="text" placeholder="Enter Shipper tax ID..." {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={control}
            name="consigneeTaxId"
            render={({ field }) => (
              <FormItem>
                <FormLabel className=""> Consignee </FormLabel>
                <FormControl>
                  <Input
                    type="text"
                    placeholder="Enter Consignee tax ID..."
                    {...field}
                    onChange={(e) => {
                      field.onChange(e);
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={control}
            name="notifyPartyTaxId"
            render={({ field }) => (
              <FormItem>
                <FormLabel className=""> Notify Party</FormLabel>
                <FormControl>
                  <Input type="text" placeholder="Enter Notify Party Tax ID..." {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {watch("shipperTaxId") || watch("consigneeTaxId") || watch("notifyPartyTaxId") ? (
            <FormField
              control={control}
              name="printTaxIdOnBillOfLaiding"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <RadioGroup onValueChange={field.onChange} value={field.value} className="flex flex-col pt-2">
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value={"1"} />
                        </FormControl>
                        <FormLabel className="font-normal text-sidebar">Print Tax IDs on Bill of Lading </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value={"0"} />
                        </FormControl>
                        <FormLabel className="font-normal text-sidebar ">
                          {" "}
                          <b className="whitespace-nowrap">DO NOT</b> Print Tax IDs on Bill of Lading
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          ) : (
            ""
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default CustomsComplianceSection;
