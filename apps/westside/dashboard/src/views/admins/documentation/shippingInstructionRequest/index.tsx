import { useEffect, useState } from "react";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { Typography } from "@/components/typography";
import { Button } from "@/components/ui/button";
import { Card, CardHeader } from "@/components/ui/card";
import { Form } from "@/components/ui/form";
import { zodResolver } from "@hookform/resolvers/zod";
import { ChevronRight, Plus } from "lucide-react";
import { useForm } from "react-hook-form";
// custom view components
import GeneralDetailSection from "./generalDetailsSection";
import AdditionalPartiesSection from "./additionalPartiesSection";
import TransportSection from "./transportSection";
import ParticularsSection from "./particularsSection";
import CustomsComplianceSection from "./CustomsComplianceSection";
import ICS2EntrySummaryDeclarationSection from "./ICS2EntrySummmaryDeclarationSection";
import ControlTotalsSection from "./controlTotalsSection";
import ShippersDeclaredValueSection from "./shippersDeclaredValueSection";
import FreightChargesSection from "./freightChargesSection";
import DocumentationSection from "./documentationSection";
import NotificationEmailSection from "./notificationEmailSection";
import SaveSection from "./saveSection";
import { useMutation, useQuery } from "@tanstack/react-query";
import {
  amendShippingInstructionRequest,
  fetchCarrierBookingNumbers,
  fetchEquipmentDetails,
  fetchShippingInstructionRequestInitialData,
  fetchShippingInstructionRequestTrackNTraceData,
  fetchSingleShippingInstructionTemplate,
  submitShippingInstructionRequest,
} from "@/services/admin/shippingInstruction";
import { toast } from "sonner";
import BillPrintInstructionSection from "./blPrintInstructionSection";
import { fecthBasicBookingRequestData } from "@/services/admin/booking";
import { SIRequestDefaultValues, SIRequestFormSchema, SIRequestFormType } from "./schema";
import {
  generateSubmitPayload,
  getSIRequestAmendPopulateData,
  getSIRequestPopulatedData,
  populateForEmptyCarrierBookingNumberData,
  populateForTempCarrierBookingNumberData,
} from "./defaults";
import Loader from "@/components/Loader";
import { fetchContractPartyList } from "@/services/admin/common";
import { fetchShippingInstructionDetails } from "@/services/admin/shippingInstructionConfirmation";
import { ShippingInstructionDetailsType } from "@/types/shippingInstructionConfirmation";
import dayjs from "dayjs";

const ShippingInstructionRequestView = () => {
  const { bookingId } = useParams();
  const [searchParams] = useSearchParams();
  const templateId = searchParams.get("template_id") || null;
  const amendId = searchParams.get("amend_id") || null;
  const navigate = useNavigate();
  const [viewAdditionalParties, setViewAdditionalParties] = useState(false);

  // fetching Contract Party List.
  const {
    data: contractPartyData,
    error: contractPartyError,
    isFetching: contractPartyFetching,
  } = useQuery({
    queryKey: ["fetchContractPartyList"],
    queryFn: fetchContractPartyList,
    refetchOnWindowFocus: false,
  });

  // fetching equipment data.
  const {
    data: equipmentData,
    error: equipmentDataError,
    isFetching: equipmentDataFetching,
  } = useQuery({
    queryKey: ["fetchEquipmentData", { bookingId: bookingId || "" }],
    queryFn: fetchEquipmentDetails,
    refetchOnWindowFocus: false,
    enabled: !!bookingId,
  });

  // fetching initial data.
  const {
    data: initialData,
    error: initialDataError,
    isFetching: initialDataFetching,
  } = useQuery({
    queryKey: ["fetchInitalBookigData"],
    queryFn: fecthBasicBookingRequestData,
    refetchOnWindowFocus: false,
  });

  // fetching Initial Booking data.
  const {
    data: bookingData,
    error: bookingDataError,
    isFetching: bookingDataFetching,
  } = useQuery({
    queryKey: [
      "fetchInitialBookingSIData",
      {
        bookingId: bookingId || "",
      },
    ],
    queryFn: fetchShippingInstructionRequestInitialData,
    enabled: Boolean(bookingId),
    refetchOnWindowFocus: false,
  });

  // fetching template data.
  const {
    data: templateData,
    error: templateDataError,
    isFetching: templateDataFetching,
  } = useQuery({
    queryKey: [
      "fetchInitalBookigTemplateData",
      {
        bookingId: templateId || "",
      },
    ],
    queryFn: fetchSingleShippingInstructionTemplate,
    enabled: Boolean(templateId),
    refetchOnWindowFocus: false,
  });

  const {
    data: siDetailData,
    isFetching: siDetailDataFetching,
    error: siDetailDataError,
  } = useQuery({
    queryKey: ["shippingInstructionDetails", { name: String(amendId) || "" }],
    queryFn: fetchShippingInstructionDetails,
    enabled: Boolean(amendId),
  });
  

  const form = useForm<SIRequestFormType>({
    resolver: zodResolver(SIRequestFormSchema),
    defaultValues: SIRequestDefaultValues,
    mode: "onBlur",
  });

  // Autopopulating Whole SI Create page with Amend Data.
  useEffect(() => {
    if (siDetailData?.message?.name && initialData) {
      const resetValue = getSIRequestAmendPopulateData(siDetailData?.message);
      setTimeout(() => {
        form.reset(resetValue);
      }, 0);

      function transformFreightCharges(freight_charges: ShippingInstructionDetailsType["freight_charges"]) {
        const initial = {
          typeOfFreightCharge: "",
          freightTerm: "",
          payer: "",
          paymentLocation: {
            name: "",
            location: "",
            locode: "",
          },
          individualCharges: [
            {
              chargeType: "Basic Freight",
              freightTerm: "",
              payer: "",
              paymentLocation: {
                name: "",
                location: "",
                locode: "",
              },
            },
          ],
        };

        if (!freight_charges || freight_charges?.length === 0) return initial;

        const first = freight_charges[0];

        if (first?.type === "All Charges") {
          return {
            typeOfFreightCharge: "allCharges",
            freightTerm: first.freight_term || "",
            payer: first.payer || "",
            paymentLocation: {
              name: first.payment_location_data?.name || "",
              location: first.payment_location_data?.location_name || "",
              locode: first.payment_location_data?.locode || "",
            },
            individualCharges: [
              {
                chargeType: "Basic Freight",
                freightTerm: "",
                payer: "",
                paymentLocation: {
                  name: "",
                  location: "",
                  locode: "",
                },
              },
            ],
          };
        }

        if (first?.type === "Individual Charges") {
          return {
            ...initial,
            typeOfFreightCharge: "individualCharges",
            individualCharges: freight_charges.map((charge) => ({
              chargeType: charge.charge_type || "",
              freightTerm: charge.freight_term || "",
              payer: charge.payer || "",
              paymentLocation: {
                name: charge.payment_location_data?.name || "",
                location: charge.payment_location_data?.location_name || "",
                locode: charge.payment_location_data?.locode || "",
              },
            })),
          };
        }

        return initial;
      }

      form.reset({ ...resetValue, ...transformFreightCharges(siDetailData?.message?.freight_charges) });
    }
  }, [siDetailData?.message?.name, form, initialData?.message?.data?.carriers]);

  // Autopopulating Whole SI Create page with Template Data.
  useEffect(() => {
    if (templateData?.message?.data?.si_data) {
      setTimeout(() => {
        form.reset(JSON.parse(templateData?.message?.data?.si_data));
      }, 0);
    }
  }, [templateData?.message?.data?.si_data]);

  // Autopopulating whole SI create page and Container Data.
  useEffect(() => {
    if (bookingData?.message?.message?.name && !templateId && equipmentData?.message?.status === "success") {
      const resetValues = getSIRequestPopulatedData(bookingData?.message?.message, equipmentData?.message?.data);
      setTimeout(() => {
        form.reset(resetValues);
      }, 0);
    } else if (bookingData?.message?.message?.name && !templateId) {
      const resetValues = getSIRequestPopulatedData(bookingData?.message?.message);
      setTimeout(() => {
        form.reset(resetValues);
      }, 0);
    }
  }, [bookingData, equipmentData]);

  // Submit Shipping Instruction.
  const { mutate, isPending, isError, isSuccess } = useMutation({
    mutationFn: submitShippingInstructionRequest,
    onSuccess: (data) => {
      if (data?.message?.message?.status_code === 200) {
        toast.success("Shipping Instruction Submitted Successfully.", {
          style: {
            background: "#50C878",
            color: "#fff",
          },
          description: "Shipping Instruction Submitted Successfully!",
        });
        navigate(
          `/dashboard/documentation/my-shipping-instruction/shipping-confirmation/${data?.message?.message?.data}`
        );
      } else {
        toast.error(data?.message?.message?.message || "Failed to submit Shipping Instruction. Please try again.");
      }
    },
    onError: () => {
      toast.error("Failed to Submit. Please try again.");
    },
  });

  // Amend Shipping Instruction.
  const { mutate: amendMutate, isPending: amendIsPending } = useMutation({
    mutationFn: amendShippingInstructionRequest,
    onSuccess: (data) => {
      if (data?.message?.message?.status_code === 200) {
        toast.success("Shipping Instruction Amend Submitted Successfully.", {
          style: {
            background: "#50C878",
            color: "#fff",
          },
          description: "Shipping Instruction Amend Submitted Successfully!",
        });
        navigate(
          `/dashboard/documentation/my-shipping-instruction/shipping-confirmation/${data?.message?.message?.data}`
        );
      } else {
        toast.error(data?.message?.message?.message || "Failed to amend Shipping Instruction. Please try again.");
      }
    },
    onError: () => {
      toast.error("Failed to Submit. Please try again.");
    },
  });
  

  function onSubmit(formData: SIRequestFormType) {
    const payloadData = generateSubmitPayload(formData);
    const validatedData = SIRequestFormSchema.parse(formData);
    const isOlderThan15Min = dayjs().diff(dayjs(siDetailData?.message?.creationdate), "minute") < 15;

    if (
      Boolean(amendId) &&
      siDetailData?.message?.name &&
      isOlderThan15Min &&
      siDetailData?.message?.inttra_status?.si_inttra_status === "Pending"
    ) {
      mutate({
        payload: { ...payloadData, si_id: siDetailData?.message?.name, si_type: "reuse" },
      });
    } else if (Boolean(amendId) && siDetailData?.message?.name) {
      amendMutate({
        payload: { ...payloadData, si_id: siDetailData?.message?.name, si_type: "amend" },
      });
    } else {
      mutate({ payload: payloadData });
    }
  }

  const {
    mutate: mutateTrackNTrace,
    isPending: isPendingTNT,
    isError: isErrorTNT,
    isSuccess: isSuccessTNT,
  } = useMutation({
    mutationFn: fetchShippingInstructionRequestTrackNTraceData,
    onSuccess: async (data) => {
      if (data?.message?.message?.length === 0) {
        toast.error("No data found in Track and Trace.");
      }

      await mutateCarrierBookingFetch({
        bookingId: form.watch("carrierBookingNumber") || "",
      });

      setTimeout(() => {
        const containers = form.getValues("particularContainers");
        let noOfChanges = 0;

        data?.message?.message?.forEach((trackingItem) => {
          const equipmentRef = trackingItem?.equipmentReference;

          const exists = containers?.some((container) => container?.containerNumber === equipmentRef);

          if (exists) return;

          // Finding an empty container slot
          const emptyContainer = containers?.find((container) => !container?.containerNumber);

          if (emptyContainer) {
            emptyContainer.containerNumber = equipmentRef;
            noOfChanges += 1;
          } else {
            // No empty container, so append a new one
            toast.error("Reached the limit of container number.");
            // ? Appended new object in an array.
            // containers.push({
            //   // container details.
            //   containerNumber: equipmentRef || "",
            //   // cargo details.
            //   cargoPackageCount: "",
            // });
          }
        });

        if (noOfChanges > 0) {
          toast.success(`${noOfChanges} Container are retrieved from Track and Trace.`);
        }

        form.reset({
          ...form.getValues(),
          particularContainers: containers,
        });
      }, 0);
    },
    onError: () => {
      toast.error("Failed to fetch Track and Trace. Please try again.");
    },
  });

  const handleTrackAndTrace = () => {
    const careerBookingNumber = form.watch("carrierBookingNumber") || "";
    mutateTrackNTrace({
      intraBookingReferenceNumber: careerBookingNumber || "",
    });
  };

  // handle Fetching data from Carrier Booking Number and update data => TEMP&RAW
  const {
    mutateAsync: mutateCarrierBookingFetch,
    isPending: isPendingCBF,
    isError: isErrorCBF,
    isSuccess: isSuccessCBF,
  } = useMutation({
    mutationFn: fetchCarrierBookingNumbers,
    onSuccess: (data) => {
      const previousValues = form.getValues();

      if (templateId && !amendId) {
        setTimeout(() => {
          form.reset({
            ...previousValues,
            ...populateForTempCarrierBookingNumberData(data?.message?.data, previousValues),
          });
        }, 0);
      } else if (!amendId && !templateId) {
        setTimeout(() => {
          form.reset({ ...previousValues, ...populateForEmptyCarrierBookingNumberData(data?.message?.data) });
        }, 0);
      }
      if (data?.message?.status_code === 200) {
        toast.success("Booking Data retrieved successfully.");
      }

      if (data?.message?.status_code !== 200) {
        toast.error("No data found in this Carrier Booking Number.");
      }
    },
    onError: () => {
      toast.error("Failed to fetch data in Carrier Booking Number. Please try again.");
    },
  });

  const handleCarrierBookingNumberFetch = () => {
    const careerBookingNumber = form.watch("carrierBookingNumber") || "";
    mutateCarrierBookingFetch({
      bookingId: careerBookingNumber || "",
    });
  };

  if (!initialData) {
    return (
      <div className="flex min-h-[70dvh] h-full w-full justify-center items-center">
        <Loader />
      </div>
    );
  }

  return (
    <div className="py-6">
      {isPending || amendIsPending ? (
        <div className="fixed z-50 inset-0 bg-black/40">
          <div className="flex min-h-[100dvh] h-full w-full justify-center items-center">
            <Loader />
          </div>
        </div>
      ) : (
        ""
      )}
      {/* <div className="flex flex-col gap-2">
                <Typography variant={"p"} className="">
                    <span className="font-semibold">SI Requestor Company: </span>
                    <span className=""> westside exports llc</span>
                </Typography>
                <Typography variant={"p"} className="">
                    <span className="font-semibold ">Created by: chaitanya  uppalapati  On: </span>
                    <span className="">10 Feb 2025 01:48:30 (GMT) </span>
                </Typography>
            </div> */}
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          {/* General Details Section */}
          <div className="flex flex-col gap-5 py-5">
            <Typography variant={"h5"} weight={"semibold"}>
              General Details{" "}
            </Typography>
            <GeneralDetailSection
              amendId={amendId}
              isPendingCBF={isPendingCBF}
              handleCarrierBookingNumberFetch={handleCarrierBookingNumberFetch}
              handleTrackAndTrace={handleTrackAndTrace}
              isPending={isPendingTNT}
              initialData={initialData?.message?.data}
            />
          </div>
          {/* Additional Parties */}
          <div className="">
            <Card className="py-0">
              <CardHeader onClick={() => setViewAdditionalParties((prev) => !prev)} className="bg-[#D3DAE7] py-4">
                <Typography variant={"p"} weight={"semibold"} className="flex items-center gap-1">
                  <span className="">
                    <Plus strokeWidth={2.5} />
                  </span>
                  <span className="underline">Additional Parties</span>
                </Typography>
              </CardHeader>
              {viewAdditionalParties ? (
                <AdditionalPartiesSection
                  contractPartyList={contractPartyData?.message?.data}
                  initialData={initialData?.message?.data}
                />
              ) : (
                ""
              )}
            </Card>
          </div>
          {/* Transport Section */}
          <div className="flex flex-col gap-5 py-5">
            <Typography variant={"h5"} weight={"semibold"}>
              Transport
            </Typography>
            <TransportSection />
          </div>
          {/* Customs Compliance Section */}
          <div className="flex flex-col gap-5 py-5">
            <Typography variant={"h5"} weight={"semibold"}>
              Customs Compliance
            </Typography>
            <div>
              <CustomsComplianceSection />
            </div>
          </div>
          {/* ICS2 Entry Summary Declaration Section */}
          <div className="flex flex-col gap-5 py-5">
            <Typography variant={"h5"} weight={"semibold"}>
              ICS2 Entry Summary Declaration{" "}
            </Typography>
            <div>
              <ICS2EntrySummaryDeclarationSection initialData={initialData?.message?.data} />
            </div>
          </div>
          {/* Particulars Section */}
          <div className="flex flex-col gap-5 py-5">
            <Typography variant={"h5"} weight={"semibold"}>
              Particulars
            </Typography>
            <div>
              <ParticularsSection initialData={initialData?.message} />
            </div>
          </div>
          {/* Control Totals Section */}
          <div className="flex flex-col gap-5 py-5">
            <Typography variant={"h5"} weight={"semibold"}>
              Control Totals
            </Typography>
            <div>
              <ControlTotalsSection />
            </div>
          </div>
          {/* Shippers Declared Value Section */}
          <div className="flex flex-col gap-5 py-5">
            <Typography variant={"h5"} weight={"semibold"}>
              Shipper's Declared Value
            </Typography>
            <div>
              <ShippersDeclaredValueSection />
            </div>
          </div>
          {/* Freight Charges Section */}
          <div className="flex flex-col gap-5 py-5">
            <Typography variant={"h5"} weight={"semibold"}>
              Freight Charges
            </Typography>
            <div>
              <FreightChargesSection />
            </div>
          </div>
          {/* Documentation Section */}
          <div className="flex flex-col gap-5 py-5">
            <Typography variant={"h5"} weight={"semibold"}>
              Documentation
            </Typography>
            <div>
              <DocumentationSection />
            </div>
          </div>
          {/* Documentation Section */}
          <div className="flex flex-col gap-5 py-5">
            <Typography variant={"h5"} weight={"semibold"}>
              B/L Print Instructions  
              <span className="text-sm font-normal">(Provided to the Carrier, not printed on Bill of Lading)</span>
            </Typography>
            <div>
              <BillPrintInstructionSection />
            </div>
          </div>
          {/* Notification Email Section */}
          <div className="flex flex-col gap-5 py-5">
            <Typography variant={"h5"} weight={"semibold"}>
              Notification Emails
            </Typography>
            <div className="">
              <NotificationEmailSection />
            </div>
          </div>
          <div className="flex items-end justify-between gap-5 py-10">
            {/* Save Section */}
            <SaveSection bookingId={bookingId} />
            <div className="">
              <Button type="submit" className="h-11" variant={"secondary"}>
                {isPending || amendIsPending ? (
                  <div role="status">
                    <svg
                      aria-hidden="true"
                      className="w-8 h-8 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600"
                      viewBox="0 0 100 101"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                        fill="currentColor"
                      />
                      <path
                        d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                        fill="currentFill"
                      />
                    </svg>
                    <span className="sr-only">Loading...</span>
                  </div>
                ) : (
                  <>
                    Submit SI <ChevronRight />
                  </>
                )}
              </Button>
            </div>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default ShippingInstructionRequestView;
