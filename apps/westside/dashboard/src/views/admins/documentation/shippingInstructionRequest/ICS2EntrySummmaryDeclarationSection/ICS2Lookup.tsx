// shadcn
import { ChevronDown, User, Users } from "lucide-react";
import { FC, useState } from "react";
import { BookingCustomersGeneralType, BookingShipperGeneralType } from "@/types/booking";
import { useFormContext } from "react-hook-form";
import { toast } from "sonner";
import { ContractPartyTypes } from "@/types/common";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Typography } from "@/components/typography";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { COUNTRYLISTISODATA } from "@/constants/countries";

interface ICS2LookupProps {
  shippers?: BookingShipperGeneralType[];
  customers?: BookingCustomersGeneralType[];
  contractParty?: ContractPartyTypes[];
  inputName: "Shipper" | "Consignee" | "Notify Party";
}
const ICS2Lookup: FC<ICS2LookupProps> = ({ shippers, customers, inputName }) => {
  const [open, setOpen] = useState(false);
  const { setValue, watch } = useFormContext();
  const renderMyPartners = (nameOfType: typeof inputName) => {
    switch (nameOfType) {
      case "Shipper":
        return shippers?.length ? (
          shippers?.map((item, ind) => {
            if (item?.my_role !== 1) {
              return (
                <TableRow
                  onClick={() => {
                    setValue(`shipperPartyInvolved.actualPartyInformationSameAs`, "", {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true,
                    });
                    setValue("shipperPartyInvolved.actualParty", item?.shipper_name || "", { shouldValidate: true });
                    setValue("shipperPartyInvolved.partyId", item?.name || "", {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true,
                    });
                    setValue(`shipperPartyInvolved.country`, item?.country_code || "", {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true,
                    });
                    setValue(`shipperPartyInvolved.streetNumber`, item?.street_number || "", {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true,
                    });
                    setValue(`shipperPartyInvolved.streetName`, item?.street_name || "", {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true,
                    });
                    setValue(`shipperPartyInvolved.poBOX`, item?.po_box || "", {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true,
                    });
                    setValue(`shipperPartyInvolved.city`, item?.city || "", {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true,
                    });
                    setValue(`shipperPartyInvolved.state`, item?.state || "", {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true,
                    });
                    setValue(`shipperPartyInvolved.contactName`, item?.contact_name || "", {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true,
                    });
                    setValue(`shipperPartyInvolved.postalCode`, item?.postal_code || "", {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true,
                    });

                    setOpen(false);
                  }}
                  className="cursor-pointer even:bg-muted"
                  key={ind}
                >
                  <TableCell className="font-medium">{item?.shipper_name}</TableCell>
                  <TableCell>-</TableCell>
                  <TableCell className="">{item?.postal_code || "-"}</TableCell>
                  <TableCell className="whitespace-normal ">{item?.custom_address || "-"}</TableCell>
                </TableRow>
              );
            }
          })
        ) : (
          <Typography variant={"blockquote"}>No Data Found!</Typography>
        );
      case "Consignee":
        return customers?.length ? (
          customers?.map((item, ind) => {
            if (item?.my_role !== 1) {
              return (
                <TableRow
                  onClick={() => {
                    setValue(`consigneePartyInvolved.actualPartyInformationSameAs`, "", {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true,
                    });
                    setValue(`consigneePartyInvolved.partyId`, item?.name || "", {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true,
                    });
                    setValue(`consigneePartyInvolved.actualParty`, item?.customer_name || "", {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true,
                    });
                    setValue(`consigneePartyInvolved.streetName`, item?.street_name || "", {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true,
                    });
                    setValue(`consigneePartyInvolved.streetNumber`, item?.street_number || "", {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true,
                    });
                    setValue(`consigneePartyInvolved.poBOX`, item?.po_box || "", {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true,
                    });
                    setValue(`consigneePartyInvolved.state`, item?.customer_state || "", {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true,
                    });
                    setValue(`consigneePartyInvolved.city`, item?.customer_city || "", {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true,
                    });
                    setValue(`consigneePartyInvolved.country`, item?.country_code || "", {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true,
                    });
                    setValue(`consigneePartyInvolved.contactName`, item?.contact || "", {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true,
                    });
                    setValue("consigneePartyInvolved.taxID", watch("consigneeTaxId"), {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true,
                    });
                    setOpen(false);
                  }}
                  className="cursor-pointer even:bg-muted"
                  key={ind}
                >
                  <TableCell className="font-medium">{item?.customer_name}</TableCell>
                  <TableCell>{item?.customer_country || "-"}</TableCell>
                  <TableCell className="whitespace-normal ">{item?.customer_address || "-"}</TableCell>
                </TableRow>
              );
            }
          })
        ) : (
          <Typography variant={"blockquote"}>No Data Found!</Typography>
        );
      case "Notify Party":
        return customers?.length ? (
          customers?.map((item, ind) => {
            if (item?.my_role !== 1) {
              return (
                <TableRow
                  onClick={() => {
                    setValue(`notifyPartyInvolved.actualPartyInformationSameAs`, "", {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true,
                    });
                    setValue(`notifyPartyInvolved.partyId`, item?.name || "", {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true,
                    });
                    setValue(`notifyPartyInvolved.actualParty`, item?.customer_name || "", {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true,
                    });
                    setValue(`notifyPartyInvolved.streetName`, item?.street_name || "", {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true,
                    });
                    setValue(`notifyPartyInvolved.streetNumber`, item?.street_number || "", {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true,
                    });
                    setValue(`notifyPartyInvolved.poBOX`, item?.po_box || "", {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true,
                    });
                    setValue(`notifyPartyInvolved.state`, item?.customer_state || "", {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true,
                    });
                    setValue(`notifyPartyInvolved.city`, item?.customer_city || "", {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true,
                    });
                    setValue(`notifyPartyInvolved.country`, item?.customer_country || "", {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true,
                    });
                    setOpen(false);
                  }}
                  className="cursor-pointer even:bg-muted"
                  key={ind}
                >
                  <TableCell className="font-medium">{item?.customer_name}</TableCell>
                  <TableCell>{item?.customer_country || "-"}</TableCell>
                  <TableCell className="whitespace-normal ">{item?.customer_address || "-"}</TableCell>
                </TableRow>
              );
            }
          })
        ) : (
          <Typography variant={"blockquote"}>No Data Found!</Typography>
        );
      default:
        return <Typography variant={"blockquote"}>No Data Found!</Typography>;
    }
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button variant={"secondary"} className="text-white h-7">
          <span className="">Lookup</span>
          <ChevronDown />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-36 p-2">
        <div className="flex flex-col gap-2">
          <Button
            onClick={() => {
              if (shippers?.length && inputName === "Shipper") {
                const myRoleIndex = shippers?.findIndex((item) => item?.my_role === 1);
                if (myRoleIndex !== -1) {
                  setValue("shipperPartyInvolved.partyId", shippers[myRoleIndex]?.name || "", { shouldValidate: true });
                  setValue("shipperPartyInvolved.actualParty", shippers[myRoleIndex]?.shipper_name || "", {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true,
                  });
                  setValue(`shipperPartyInvolved.country`, shippers[myRoleIndex]?.country_code || "", {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true,
                  });
                  setValue(`shipperPartyInvolved.streetNumber`, shippers[myRoleIndex]?.street_number || "", {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true,
                  });
                  setValue(`shipperPartyInvolved.streetName`, shippers[myRoleIndex]?.street_name || "", {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true,
                  });
                  setValue(`shipperPartyInvolved.poBOX`, shippers[myRoleIndex]?.po_box || "", {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true,
                  });
                  setValue(`shipperPartyInvolved.city`, shippers[myRoleIndex]?.city || "", {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true,
                  });
                  setValue(`shipperPartyInvolved.state`, shippers[myRoleIndex]?.state || "", {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true,
                  });
                  setValue(`shipperPartyInvolved.contactName`, shippers[myRoleIndex]?.contact_name || "", {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true,
                  });
                  setValue(
                    "shipperPartyInvolved.postalCode",
                    shippers[myRoleIndex]?.postal_code ? shippers[myRoleIndex]?.postal_code : "-",
                    { shouldValidate: true, shouldDirty: true, shouldTouch: true }
                  );
                  setValue(`shipperPartyInvolved.actualPartyInformationSameAs`, "", {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true,
                  });
                } else {
                  toast.error("My role is not found!");
                }
              }
              if (customers?.length && inputName === "Consignee") {
                const myRoleIndex = customers?.findIndex((item) => item?.my_role === 1);
                if (myRoleIndex !== -1) {
                  setValue(`consigneePartyInvolved.partyId`, customers[myRoleIndex]?.name || "", {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true,
                  });
                  setValue(`consigneePartyInvolved.actualParty`, customers[myRoleIndex]?.customer_name || "", {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true,
                  });
                  setValue(`consigneePartyInvolved.streetName`, customers[myRoleIndex]?.street_name || "", {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true,
                  });
                  setValue(`consigneePartyInvolved.streetNumber`, customers[myRoleIndex]?.street_number || "", {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true,
                  });
                  setValue(`consigneePartyInvolved.poBOX`, customers[myRoleIndex]?.po_box || "", {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true,
                  });
                  setValue(`consigneePartyInvolved.state`, customers[myRoleIndex]?.customer_state || "", {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true,
                  });
                  setValue(`consigneePartyInvolved.city`, customers[myRoleIndex]?.customer_city || "", {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true,
                  });
                  setValue(`consigneePartyInvolved.country`, customers[myRoleIndex]?.country_code || "", {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true,
                  });
                  setValue(`consigneePartyInvolved.contactName`, customers[myRoleIndex]?.contact || "", {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true,
                  });
                  setValue(`consigneePartyInvolved.actualPartyInformationSameAs`, "", {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true,
                  });
                  setValue("consigneePartyInvolved.taxID", watch("consigneeTaxId"), {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true,
                  });
                } else {
                  toast.error("My role is not found!");
                }
              }
              if (customers?.length && inputName === "Notify Party") {
                const myRoleIndex = customers?.findIndex((item) => item?.my_role === 1);
                if (myRoleIndex !== -1) {
                  setValue(`notifyPartyInvolved.partyId`, customers[myRoleIndex]?.name || "", {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true,
                  });
                  setValue(`notifyPartyInvolved.actualParty`, customers[myRoleIndex]?.customer_name || "", {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true,
                  });
                  setValue(`notifyPartyInvolved.streetName`, customers[myRoleIndex]?.street_name || "", {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true,
                  });
                  setValue(`notifyPartyInvolved.streetNumber`, customers[myRoleIndex]?.street_number || "", {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true,
                  });
                  setValue(`notifyPartyInvolved.poBOX`, customers[myRoleIndex]?.po_box || "", {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true,
                  });
                  setValue(`notifyPartyInvolved.state`, customers[myRoleIndex]?.customer_state || "", {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true,
                  });
                  setValue(`notifyPartyInvolved.city`, customers[myRoleIndex]?.customer_city || "", {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true,
                  });
                  setValue(`notifyPartyInvolved.country`, customers[myRoleIndex]?.customer_country || "", {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true,
                  });
                  setValue(`notifyPartyInvolved.actualPartyInformationSameAs`, "", {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true,
                  });
                } else {
                  toast.error("My role is not found!");
                }
              }
              setOpen(false);
            }}
            variant={"ghost"}
            type="button"
            className="flex gap-1 items-center justify-start"
          >
            <span className="">
              <User size={16} />
            </span>
            <Typography variant={"small"} weight={"normal"}>
              My Role
            </Typography>
          </Button>
          <Dialog>
            <DialogTrigger asChild>
              <Button type="button" variant={"ghost"} className="flex gap-1 items-center justify-start">
                <span className="">
                  <Users size={16} />
                </span>
                <Typography variant={"small"} weight={"normal"}>
                  My Partners
                </Typography>
              </Button>
            </DialogTrigger>
            <DialogContent onInteractOutside={(e) => e.preventDefault()} className="max-w-full lg:max-w-5xl">
              <DialogHeader>
                <DialogTitle>My Partners</DialogTitle>
              </DialogHeader>
              <div className="w-full">
                <Table className="">
                  <ScrollArea className="max-w-full lg:max-w-5xl max-h-[60vh]">
                    <TableHeader>
                      <TableRow className="bg-muted-foreground/50">
                        <TableHead className="">Company Name</TableHead>
                        <TableHead>Country</TableHead>
                        {inputName === "Shipper" ? <TableHead>Postal Code</TableHead> : ""}
                        <TableHead className="text-right">Address</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>{renderMyPartners(inputName)}</TableBody>
                  </ScrollArea>
                </Table>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default ICS2Lookup;
