import { Typography } from "@/components/typography";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { CalendarIcon, GripVertical } from "lucide-react";
import { useState, useEffect } from "react";
import { ComboBox } from "../shippingInstruction/ComboBox";
import { fetchListOfEvgm } from "@/services/admin/evgm";
import { useQuery } from "@tanstack/react-query";
import {
  Eye,
  FileEdit,
  XCircle,
  RefreshCcw,
  Search,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import { useNavigate, useSearchParams } from "react-router-dom";
import dayjs from "dayjs";

function EVGMView() {
  const navigate = useNavigate();
  const [fromDate, setFromDate] = useState<Date>();
  const [toDate, setToDate] = useState<Date>();
  const [searchParams, setSearchParams] = useSearchParams();
  const [searchKey, setSearchKey] = useState(searchParams.get("search") || "");
  const [currentPage, setCurrentPage] = useState(
    parseInt(searchParams.get("page") || "1", 10) || 1
  );

  const {
    data: fetchEvgmList,
    error: fetchEvgmListError,
    isFetching: fetchEvgmListFetching,
    refetch: fetchEvgmListRefetch,
  } = useQuery({
    queryKey: ["fetchListOfEvgm", currentPage, searchKey],
    queryFn: () => fetchListOfEvgm(currentPage, searchKey),
  });
  const itemsPerPage = fetchEvgmList?.message?.pagination?.page_length;
  const totalCount = fetchEvgmList?.message?.pagination?.total_count || 0;
  const totalPages = Math.ceil(totalCount / itemsPerPage);

  useEffect(() => {
    const newParams = new URLSearchParams();
    if (searchKey) newParams.set("search", searchKey);
    newParams.set("page", currentPage.toString());

    setSearchParams(newParams);
  }, [searchKey, currentPage]);
  useEffect(() => {
    const pageParam = parseInt(searchParams.get("page") || "1", 10);
    setCurrentPage(isNaN(pageParam) ? 1 : pageParam);
  }, []);
  const getStatusColorGradient = (status: string) => {
    switch (status) {
      case "New":
        return "#DBEAFE";
      case "Rejected":
        return "#FEE2E2";
      case "Accepted":
        return "#DCFCE7";
      case "Acknowledged":
        return "#FFEDD5";
      case "Reopen":
        return "#FEF9C3";
      case "Open":
        return "#EDE9FE";
      case "Sent":
        return "#E9E294";
      case "Revised":
        return "#E0F7FA";
      case "Requested Cancellation":
        return "#fccd67ff";
      case "Cancelled":
        return "#ec5034ff";
      case "Approved":
        return "#D1FAE5";
      case "Rejected Cancellation":
        return "#FEE2E2";
      case "Requested":
        return "#EDE9FE";
      default:
        return "#F3F4F6";
    }
  };

  const getStatusTextColor = (status: string) => {
    switch (status) {
      case "New":
        return "#1D4ED8";
      case "Rejected":
        return "#B91C1C";
      case "Accepted":
        return "#15803D";
      case "Acknowledged":
        return "#C2410C";
      case "Reopen":
        return "#A16207";
      case "Open":
        return "#6D28D9";
      case "Sent":
        return "#2F855A";
      case "Revised":
        return "#17A2B8";
      case "Requested Cancellation":
        return "#b45309ff";
      case "Cancelled":
        return "#9a3412ff";
      case "Approved":
        return "#047857";
      case "Rejected Cancellation":
        return "#B91C1C";
      case "Requested":
        return "#6D28D9";
      default:
        return "#374151";
    }
  };

  const getStatusBorderColor = (status: string) => {
    switch (status) {
      case "New":
        return "#3B82F6";
      case "Rejected":
        return "#EF4444";
      case "Accepted":
        return "#22C55E";
      case "Acknowledged":
        return "#F97316";
      case "Reopen":
        return "#EAB308";
      case "Open":
        return "#8B5CF6";
      case "Sent":
        return "#2F855A";
      case "Revised":
        return "#17A2B8";
      case "Requested Cancellation":
        return "#b45309ff";
      case "Cancelled":
        return "#9a3412ff";
      case "Approved":
        return "#047857";
      case "Rejected Cancellation":
        return "#EF4444";
      case "Requested":
        return "#8B5CF6";
      default:
        return "#9CA3AF";
    }
  };

  return (
    <div>
      <div className="mt-1 flex items-center justify-between mr-8">
        {/* Left side buttons */}
        <div className="flex gap-2 items-center">
          {/* <Button variant="outline" className="flex items-center gap-2">
    <Eye className="w-4 h-4" />
    View
  </Button>

  <Button variant="outline" className="flex items-center gap-2">
    <FileEdit className="w-4 h-4" />
    Amend
  </Button>

  <Button variant="outline" className="flex items-center gap-2">
    <XCircle className="w-4 h-4" />
    Cancel
  </Button>

  <Button variant="outline" className="flex items-center gap-2">
    <Plus className="w-4 h-4" />
    New
  </Button>

  <Button variant="outline" className="flex items-center gap-2">
    <FileText className="w-4 h-4" />
    Export
  </Button> */}
        </div>

        {/* Right side controls */}
        {/* <div className="flex gap-4 items-center">
    <button className="text-orange-500 text-sm">⟳ Restore Defaults</button>
    <Button className="bg-[#111827] text-white">Create New eVGM</Button>
    
  </div> */}
      </div>
      <div className="mt-8 flex items-center justify-between ">
        {/* Left side buttons */}
        <div className="flex items-center gap-2">
          {/* <span className="text-sm text-gray-600">{fetchEvgmList?.message?.data.length} Results Found</span> */}
          <p className="text-sm sm:text-base mt-2 sm:mt-0">
            {fetchEvgmList?.message?.pagination?.total_count}{" "}
            <span className="text-gray-500">
              Result
              {fetchEvgmList?.message?.pagination?.total_count > 1 ? "s" : ""}{" "}
              Found
            </span>
          </p>
        </div>

        {/* Right side controls */}

        <div className="flex gap-4 items-center ml-auto">
          <div className="relative">
            <Input
              className="pr-10"
              placeholder="Search here"
              value={searchKey}
              onChange={(e) => {
                setSearchKey(e.target.value);
                setCurrentPage(1);
                setSearchParams((prev) => ({
                  ...prev,
                  search: e.target.value,
                  page: "1",
                }));
              }}
            />
            <Search className="absolute right-4 top-[14px] h-4 w-4 text-gray-400" />
          </div>
          <div className="relative flex">
            {searchKey && (
              <Button
                variant="outline"
                className="h-11"
                onClick={() => {
                  setSearchKey("");
                  setSearchParams({});
                  setCurrentPage(1);
                }}
              >
                <RefreshCcw /> Clear Filters
              </Button>
            )}
          </div>
          <div className="relative">
            <Button
              className="bg-[#111827] text-white"
              onClick={() => navigate("/dashboard/documentation/create-eVGM")}
            >
              Create New eVGM
            </Button>
          </div>
        </div>
      </div>
      <div className="mt-10 overflow-x-auto overflow-y-auto">
        <ScrollArea className="min-w-full">
          <Table className="table-auto border-1 border-[#D3DAE7] w-full">
            <TableHeader className="font-bold text-[#191C36]">
              <TableRow className="bg-[#E5E8EF]">
                <TableHead className="p-3 w-[10%] font-bold text-[#191C36]">
                  <div className="flex flex-col">Inttra eVGM #</div>
                </TableHead>
                <TableHead className="p-3 w-[10%] font-bold text-[#191C36]">
                  <div className="flex flex-col">
                    Carrier Booking #{/* <GripVertical /> */}
                  </div>
                </TableHead>
                <TableHead className="p-3 w-[10%] font-bold text-[#191C36]">
                  <div className="flex flex-col">
                    Carrier
                    {/* <GripVertical /> */}
                  </div>
                </TableHead>
                <TableHead className="p-3 w-[15%] font-bold text-[#191C36]">
                  <div className="flex flex-col">
                    {/* <ComboBox headTitle="EQUIPMENT NAME" /> */}
                    Equipment Name
                    {/* <GripVertical /> */}
                  </div>
                </TableHead>
                <TableHead className="p-3 w-[10%] font-bold text-[#191C36]">
                  <div className="flex flex-col">
                    Verified Gross Mass
                    {/* <GripVertical /> */}
                  </div>
                </TableHead>
                <TableHead className="p-3 w-[10%] font-bold text-[#191C36]">
                  <div className="flex flex-col">
                    Transaction State
                    {/* <GripVertical /> */}
                  </div>
                </TableHead>
                <TableHead className="p-3 w-[10%] font-bold text-[#191C36]">
                  <div className="flex flex-col">
                    Status
                    {/* <GripVertical /> */}
                  </div>
                </TableHead>
                <TableHead className="p-3 w-[10%] font-bold text-[#191C36]">
                  Created Date
                  {/* <GripVertical /> */}
                </TableHead>
                <TableHead className="p-3 w-[10%] font-bold text-[#191C36]">
                  Action
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody className="border-1 border-[#D3DAE7]">
              {fetchEvgmList?.message?.data?.length ? (
                fetchEvgmList.message.data.map((item: any, index: number) => (
                  <TableRow key={index} className="h-0">
                    <TableCell className="p-3">
                      {item.inttra_evgmid ?? "--"}
                    </TableCell>
                    <TableCell className="p-3">
                      {item.carrier_booking_number ?? "--"}
                    </TableCell>
                    <TableCell className="p-3">
                      {item.carrier ?? "--"}
                    </TableCell>
                    <TableCell className="p-3">
                      {item.equipment_name ?? "--"}
                    </TableCell>
                    <TableCell className="p-3">
                      {item.verified_gross_mass ?? "--"}
                    </TableCell>
                    <TableCell className="p-3">{item.state ?? "--"}</TableCell>
                    <TableCell className="p-3">
                      <div className="flex justify-left">
                        <span
                          className={`px-2 py-1 rounded-sm text-sm font-normal`}
                          style={{
                            color: getStatusTextColor(item.status),
                            borderColor: getStatusBorderColor(item.status),
                            backgroundColor: getStatusColorGradient(
                              item.status
                            ),
                            borderWidth: "2px",
                            borderStyle: "solid",
                          }}
                        >
                          {item.status === "Acknowledged"
                            ? "Acknowledged"
                            : item.status === "Reopen"
                            ? "Reopened"
                            : item.status || "N/A"}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell className="p-3">
                      {item.date_created
                        ? dayjs(item.date_created).format("MMM-DD-YYYY hh:mm A")
                        : ""}
                    </TableCell>
                    <TableCell className="p-3 flex items-center gap-2">
                      <Button
                        variant={"outline"}
                        className="border-2 border-primary bg-primary hover:bg-orange-600 text-white rounded-sm"
                        onClick={() =>
                          navigate(
                            `/dashboard/documentation/view-eVGM/${item.equipment_id}`
                          )
                        }
                      >
                        <Eye />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow className="h-0">
                  <TableCell className="p-3"></TableCell>
                  <TableCell className="p-3"></TableCell>
                  <TableCell className="p-3"></TableCell>
                  <TableCell className="p-3"></TableCell>
                  <TableCell className="text-[#929FB8]">
                    No rows to show
                  </TableCell>
                  <TableCell className="p-3"></TableCell>
                  <TableCell className="p-3"></TableCell>
                  <TableCell className="p-3"></TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
          <ScrollBar orientation="horizontal" />
        </ScrollArea>
      </div>

      <div className="flex items-center justify-between mt-6">
        <div className="flex items-center gap-2 mx-auto sm:mx-0 sm:ml-auto">
          <Button
            className="rounded-lg px-3 py-2"
            variant="outline"
            onClick={() => {
              setCurrentPage((prev) => Math.max(prev - 1, 1));
              setSearchParams((prev) => {
                const newParams = new URLSearchParams(prev);
                newParams.set("page", String(currentPage - 1));
                return newParams;
              });
            }}
            disabled={currentPage === 1}
            size="sm"
          >
            <ChevronLeft className="text-black h-4 w-4" />
          </Button>
          <div className="flex gap-1">
            {Array.from({ length: Math.min(totalPages, 5) }).map((_, index) => {
              let page;
              if (totalPages <= 5) {
                page = index + 1;
              } else if (currentPage <= 3) {
                page = index + 1;
              } else if (currentPage >= totalPages - 2) {
                page = totalPages - 4 + index;
              } else {
                page = currentPage - 2 + index;
              }

              return (
                <Button
                  key={page}
                  onClick={() => {
                    setCurrentPage(page);
                    setSearchParams((prev) => {
                      const newParams = new URLSearchParams(prev);
                      newParams.set("page", String(page));
                      return newParams;
                    });
                  }}
                  className={`rounded-lg px-3 py-2 ${
                    page === currentPage
                      ? "bg-white text-black border"
                      : "text-gray-500"
                  }`}
                  variant="outline"
                  size="sm"
                >
                  {page}
                </Button>
              );
            })}
            {totalPages > 5 && currentPage < totalPages - 2 && (
              <>
                <span className="flex items-center px-2">...</span>
                <Button
                  onClick={() => {
                    setCurrentPage(totalPages);
                    setSearchParams((prev) => {
                      const newParams = new URLSearchParams(prev);
                      newParams.set("page", String(totalPages));
                      return newParams;
                    });
                  }}
                  className={`rounded-lg px-3 py-2 ${
                    totalPages === currentPage
                      ? "bg-white text-black border"
                      : "text-gray-500"
                  }`}
                  variant="outline"
                  size="sm"
                >
                  {totalPages}
                </Button>
              </>
            )}
          </div>
          <Button
            className="rounded-lg px-3 py-2"
            variant="outline"
            onClick={() =>
              setCurrentPage((prev) => Math.min(prev + 1, totalPages))
            }
            disabled={currentPage === totalPages}
            size="sm"
          >
            <ChevronRight className="text-black h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}

export default EVGMView;
