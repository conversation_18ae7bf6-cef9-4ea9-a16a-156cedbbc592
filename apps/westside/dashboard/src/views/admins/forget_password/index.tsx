import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Link, useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { generateOtp, verifyOtp } from "@/services/signin";
import { useMutation } from "@tanstack/react-query";

const ForgotPasswordPage = () => {
  const navigate = useNavigate();
  const [username, setUsername] = useState("");
  const [otp, setOtp] = useState(["", "", "", "", "", ""]);
  const [otpSent, setOtpSent] = useState(false);

  //Mutation for Generate OTP
  const generateOtpMutation = useMutation({
    mutationFn: (username: string) => generateOtp(username),
    onSuccess: (res) => {
      if (res?.message?.status_code === 200) {
        toast.success("OTP sent successfully!");
        setOtpSent(true);
      } else {
        toast.error(res?.message?.message || "Failed to send OTP");
      }
    },
    onError: () => {
      toast.error("Something went wrong. Please try again.");
    },
  });

  // Mutation for Verify OTP
  const verifyOtpMutation = useMutation({
    mutationFn: ({ username, otp }: { username: string; otp: string }) =>
      verifyOtp(username, otp),
    onSuccess: (res) => {
      if (res?.message?.status_code === 200) {
        toast.success(res?.message?.message);
        navigate("/reset-password", { state: { username } });
      } else {
        toast.error(res?.message?.message || "Invalid OTP");
      }
    },
    onError: () => {
      toast.error("Verification failed. Try again.");
    },
  });

  // Handle OTP box change
  const handleOtpChange = (value: string, index: number) => {
    if (/^\d?$/.test(value)) {
      const newOtp = [...otp];
      newOtp[index] = value;
      setOtp(newOtp);

      // Auto-focus next box if filled
      if (value && index < 5) {
        const next = document.getElementById(`otp-${index + 1}`);
        next?.focus();
      }
    }
  };

  // Handle backspace navigation
  const handleOtpKeyDown = (
    e: React.KeyboardEvent<HTMLInputElement>,
    index: number
  ) => {
    if (e.key === "Backspace" && !otp[index] && index > 0) {
      const prev = document.getElementById(`otp-${index - 1}`);
      prev?.focus();
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const otpCode = otp.join("");

    if (!otpSent) {
      if (!username.trim()) {
        toast.error("Please enter your User ID first.");
        return;
      }
      generateOtpMutation.mutate(username);
      return;
    }

    if (otpCode.length !== 6) {
      toast.error("Please enter a valid 6-digit OTP.");
      return;
    }

    verifyOtpMutation.mutate({ username, otp: otpCode });
  };

  return (
    <div className="flex h-[100dvh] items-center justify-center bg-gray-50 px-4">
      <div className="w-full max-w-md rounded-lg bg-white p-8 shadow-lg">
        <h1 className="text-2xl font-bold text-center text-[#1a1e3a] mb-6">
          Forgot Password
        </h1>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* User ID */}
          <Input
            type="text"
            placeholder="Enter User ID or Email"
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            required
            disabled={otpSent} // disable once OTP is sent
            className="h-12 rounded-md border border-gray-300 px-4 text-sm focus:border-gray-400 focus:outline-none"
          />

          {!otpSent && (
            <Button
              type="button"
              onClick={() => generateOtpMutation.mutate(username)}
              className="w-full bg-[#1a1e3a] py-3 text-white hover:bg-[#272c4e] text-sm flex justify-center items-center"
              disabled={generateOtpMutation.isPending}
            >
              {generateOtpMutation.isPending ? (
                <svg
                  className="animate-spin h-5 w-5 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8v8H4z"
                  ></path>
                </svg>
              ) : (
                "Generate OTP"
              )}
            </Button>
          )}

          {/* OTP Inputs (only show after OTP is sent) */}
          {otpSent && (
            <>
              <div className="flex justify-between">
                {otp.map((digit, index) => (
                  <Input
                    key={index}
                    id={`otp-${index}`}
                    type="text"
                    maxLength={1}
                    value={digit}
                    onChange={(e) => handleOtpChange(e.target.value, index)}
                    onKeyDown={(e) => handleOtpKeyDown(e, index)}
                    className="h-12 w-12 text-center text-lg border border-gray-300 rounded-md focus:border-gray-400 focus:outline-none"
                  />
                ))}
              </div>

              <Button
                type="submit"
                disabled={otp.join("").length !== 6 || verifyOtpMutation.isPending}
                className={`w-full py-3 text-sm text-white ${
                  otp.join("").length === 6
                    ? "bg-[#1a1e3a] hover:bg-[#272c4e]"
                    : "bg-gray-400 cursor-not-allowed"
                }`}
              >
                {verifyOtpMutation.isPending ? "Verifying..." : "Verify OTP"}
              </Button>
            </>
          )}

          <p className="text-center text-sm text-gray-600">
            <Link to="/dashboard" className="hover:text-gray-800">
              Back to Login
            </Link>
          </p>
        </form>
      </div>
    </div>
  );
};

export default ForgotPasswordPage;
