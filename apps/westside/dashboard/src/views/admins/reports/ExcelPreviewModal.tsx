import {
  <PERSON>alog,
  DialogContent,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { useEffect, useState } from "react";
import { ExcelRenderer } from "react-excel-renderer";
import dayjs from 'dayjs'

type Props = {
  fileUrl?: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  fileTitle: string;
};
function excelSerialToDate(serial: number) {
  if (!serial || isNaN(serial)) return "";
  const utc_days = Math.floor(serial - 25569);
  const utc_value = utc_days * 86400;
  const date_info = new Date(utc_value * 1000);

  const month = String(date_info.getUTCMonth() + 1).padStart(2, "0");
  const day = String(date_info.getUTCDate()).padStart(2, "0");
  const year = date_info.getUTCFullYear();

  const date=dayjs(date_info,"YYYY-MM-DD HH:mm:ss").format("MMM-DD-YYYY")
  
// return `${month}-${day}-${year}`;
return date;
  
}

export function ExcelPreviewModal({
  fileUrl,
  open,
  onOpenChange,
  fileTitle,
}: Props) {
  const [data, setData] = useState<any[][]>([]);
  const [reportTitle, setReportTitle] = useState<string>("");
  const [dateRange, setDateRange] = useState<string>("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>("");

  useEffect(() => {
    if (!open || !fileUrl) return;

    const loadExcel = async () => {
      try {
        setLoading(true);
        setError("");

        // Fetch the file
        const response = await fetch(fileUrl);
        if (!response.ok)
          throw new Error(`HTTP error! status: ${response.status}`);

        const blob = await response.blob();
        const file = new File([blob], "excel-file.xlsx", {
          type:
            blob.type ||
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        });

        // Use ExcelRenderer to parse the file
        ExcelRenderer(file, (err: any, resp: any) => {
          if (err) {
            console.error("Excel parsing error:", err);
            setError(err.message || "Failed to parse Excel file");
            setLoading(false);
            return;
          }

          if (resp && resp.rows) {
            const allRows = resp.rows;
            console.log("Raw Excel data:", allRows);

            // Extract metadata from first few rows
            let titleFound = "";
            let dateFound = "";

            // Look for report title and date range in first 6 rows
            for (let i = 0; i < Math.min(6, allRows.length); i++) {
              const row = allRows[i];
              if (row && Array.isArray(row)) {
                const rowText = row.join(" ").toString();

                if (rowText.includes(fileTitle) && !titleFound) {
                  titleFound = rowText;
                }
                if (
                  rowText.includes("Date Range") ||
                  rowText.includes("2025-")
                ) {
                  dateFound = rowText;
                }
              }
            }

            setReportTitle(titleFound || `Shipment Report By ${fileTitle}`);
            setDateRange(dateFound || "");

            // Find data start row (look for headers like "Sno.", "S.No", etc.)
            let dataStartIndex = -1;
            for (let i = 0; i < allRows.length; i++) {
              const row = allRows[i];
              if (row && Array.isArray(row) && row.length > 0) {
                const firstCell = row[0]?.toString().toLowerCase() || "";
                if (
                  firstCell.includes("sno") ||
                  firstCell.includes("s.no") ||
                  firstCell.includes("serial")
                ) {
                  dataStartIndex = i;
                  break;
                }
              }
            }

            // If no header row found, try to find first meaningful data row
            if (dataStartIndex === -1) {
              for (let i = 0; i < allRows.length; i++) {
                const row = allRows[i];
                if (
                  row &&
                  Array.isArray(row) &&
                  row.filter((cell) => cell && cell.toString().trim()).length >
                    2
                ) {
                  dataStartIndex = i;
                  break;
                }
              }
            }

            // Fallback to row 3 if still not found
            if (dataStartIndex === -1) {
              dataStartIndex = Math.min(3, allRows.length - 1);
            }

            // Extract relevant data (from header row onwards)
            const relevantData = allRows.slice(dataStartIndex);
            setData(relevantData);
          } else {
            setError("No data found in Excel file");
          }

          setLoading(false);
        });
      } catch (err) {
        console.error("Error loading Excel:", err);
        setError(
          err instanceof Error ? err.message : "Failed to load Excel file"
        );
        setLoading(false);
      }
    };

    loadExcel();
  }, [fileUrl, open]);

  // Get headers (first row of data) and data rows
  const headers = data.length > 0 ? data[0] : [];
  const dataRows = data.slice(1).filter(row => {
  const firstCell = row[0]?.toString().toLowerCase() || "";
  return !firstCell.includes("total"); // remove totals row
  });


  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        onInteractOutside={(e) => e.preventDefault()}
        className="min-w-6xl max-h-[80dvh] overflow-auto"
      >
        <DialogHeader>
          <DialogTitle className="text-lg font-semibold">
            {reportTitle}
          </DialogTitle>
          {dateRange && (
            <p className="text-sm text-gray-600 mt-1">{dateRange}</p>
          )}
        </DialogHeader>

        {loading ? (
          <div className="flex items-center justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2">Loading Excel preview...</span>
          </div>
        ) : error ? (
          <div className="text-red-600 p-4 bg-red-50 rounded border">
            <p className="font-medium">Error loading Excel file:</p>
            <p className="text-sm mt-1">{error}</p>
          </div>
        ) : data.length === 0 ? (
          <div className="text-gray-500 p-4 text-center">
            <p>
              No data found in the Excel file or the file structure couldn't be
              parsed.
            </p>
            <p className="text-sm mt-2">
              Please check if the file contains the expected data format.
            </p>
          </div>
        ) : (
          <div className="overflow-auto max-h-[70vh]">
            <div className="mb-4 text-sm text-gray-600">
              Showing {dataRows.length} record{dataRows.length !== 1 ? "s" : ""}
            </div>

            <table className="table-auto w-full border-collapse border text-sm">
              <thead className="bg-gray-50">
                <tr>
                  {headers.map((header: any, index: number) => (
                    <th
                      key={index}
                      className="border border-gray-300 px-3 py-2 text-left font-medium text-gray-700 min-w-[100px]"
                    >
                      {header?.toString() || `Column ${index + 1}`}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {dataRows.map((row: any[], rowIndex: number) => (
                  <tr
                    key={rowIndex}
                    className={rowIndex % 2 === 0 ? "bg-white" : "bg-gray-50"}
                  >
                    {headers.map((_: any, colIndex: number) => (
                      <td
                        key={colIndex}
                        className="border border-gray-200 px-3 py-2 text-gray-800"
                      >
                        {(() => {
                          const value = row[colIndex];
                          if (!value) return "-";

                          // Convert if column looks like a date
                          const colHeader = (
                            headers[colIndex] || ""
                          ).toLowerCase();
                          if (
                            colHeader.includes("date") ||
                            colHeader.includes("eta")
                          ) {
                            if (!isNaN(Number(value))) {
                              return excelSerialToDate(Number(value));
                            }
                          }

                          return value.toString();
                        })()}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
