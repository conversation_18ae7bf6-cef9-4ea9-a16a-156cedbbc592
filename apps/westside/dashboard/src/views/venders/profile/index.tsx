import React, { useEffect, useState } from "react";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useForm } from "react-hook-form";
import { useQuery } from "@tanstack/react-query";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  getVendorDetails,
  addPassKey,
  removePassKey,
  regeneratePassKey,
} from "@/services/admin/vendorProfileDetails";
import { toast } from "sonner";
import { Plus, RefreshCw, Trash2, Loader2 } from "lucide-react";
import { ConfirmDialog } from "./confirmDialogue";

export default function VendorProfileDetails() {
  const [activeTab, setActiveTab] = useState<"details" | "passkeys">("details");
  const [loading, setLoading] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [passKeyToDelete, setPassKeyToDelete] = useState<string | null>(null);
  const [regenerateLoading, setRegenerateLoading] = useState(false);
  const [dialogType, setDialogType] = useState<
    "add" | "delete" | "regenerate" | null
  >(null);

  const vendorSchema = z.object({
    vendor_name: z.string().optional(),
    last_name: z.string().optional(),
    vendor_details: z.string().optional(),
    vendor_code: z.string().optional(),
    company_id: z.string().optional(),
    email: z.string().email().optional(),
    phone: z.string().optional(),
    address: z.string().optional(),
    parent_company: z.string().optional(),
    postal_code: z.string().optional(),
  });

  const {
    data: vendorData,
    isSuccess,
    isError,
    isLoading,
    refetch: vendorRefetch,
  } = useQuery({
    queryKey: ["vendorDetails"],
    queryFn: getVendorDetails,
  });

  const form = useForm<z.infer<typeof vendorSchema>>({
    resolver: zodResolver(vendorSchema),
    defaultValues: {
      vendor_name: "",
      last_name: "",
      vendor_details: "",
      vendor_code: "",
      company_id: "",
      email: "",
      phone: "",
      address: "",
      parent_company: "",
      postal_code: "",
    },
  });

  useEffect(() => {
    if (isSuccess && vendorData?.message?.data) {
      const apiData = vendorData.message.data;
      form.reset({
        vendor_name: apiData.first_name || apiData.vendor_name || "",
        last_name: apiData.last_name || "",
        vendor_details: apiData.vendor_details || "",
        vendor_code: apiData.vendor_code || "",
        company_id: apiData.parent_company || "",
        email: apiData.email_id || "",
        phone: apiData.phone || "",
        address: apiData.vendor_address || "",
        parent_company: apiData.parent_company || "",
        postal_code: apiData.zip || "",
      });
    }
  }, [isSuccess, vendorData, form]);

  const handleAddPassKey = async () => {
    setLoading(true);

    try {
      const res = await addPassKey();
      if (res?.message?.status_code !== 200) {
        toast.error(res?.message?.message || "Failed to add pass key");
        return;
      }
      toast.success("Pass key added successfully!");
      vendorRefetch();
      console.log("New passkey response:", res);
    } catch (err: any) {
      toast.error(err?.message || "Failed to add pass key");
    } finally {
      setLoading(false);
      setConfirmDialogOpen(false);
    }
  };

  const handleRegenerateAllPassKeys = async () => {
    setRegenerateLoading(true);

    try {
      const res = await regeneratePassKey();
      if (res?.message?.status_code !== 200) {
        toast.error(res?.message?.message || "Failed to regenerate pass key");
        return;
      }
      toast.success("Pass key regenerated successfully!");
      vendorRefetch();
      console.log("New passkey response:", res);
    } catch (err: any) {
      toast.error(err?.message || "Failed to regenerate pass key");
    } finally {
      setRegenerateLoading(false);
      setConfirmDialogOpen(false);
    }
  };
  const confirmDeletePassKey = async () => {
    if (!passKeyToDelete) return;
    setDeleteLoading(true);

    try {
      const res = await removePassKey(passKeyToDelete);
      if (res?.message?.status_code !== 200) {
        toast.error(res?.message?.message || "Failed to remove pass key");
        return;
      }
      toast.success("Pass key removed successfully!");
      vendorRefetch();
    } catch (err: any) {
      toast.error(err?.message || "Failed to remove pass key");
    } finally {
      setDeleteLoading(false);
      setPassKeyToDelete(null);
      setConfirmDialogOpen(false);
    }
  };
  const handleDeletePassKey = (key: string) => {
    setPassKeyToDelete(key);
    setConfirmDialogOpen(true);
  };
  if (isLoading) return <div>Loading vendor details...</div>;
  if (isError) return <div>Error loading vendor details</div>;

  return (
    <div className="max-w-4xl mx-auto p-4">
      {/* Tabs */}
      <div className="flex border-b border-gray-200 mb-4">
        <button
          onClick={() => setActiveTab("details")}
          className={`px-4 py-2 -mb-px border-b-2 font-medium ${
            activeTab === "details"
              ? "border-blue-600 text-blue-600"
              : "border-transparent text-gray-500"
          }`}
        >
          Vendor Details
        </button>
        <button
          onClick={() => setActiveTab("passkeys")}
          className={`px-4 py-2 -mb-px border-b-2 font-medium ${
            activeTab === "passkeys"
              ? "border-blue-600 text-blue-600"
              : "border-transparent text-gray-500"
          }`}
        >
          Pass Keys
        </button>
      </div>

      {/* Tab Content */}
      {activeTab === "details" && (
        <Form {...form}>
          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="vendor_name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Vendor Name</FormLabel>
                  <FormControl>
                    <Input {...field} readOnly className="bg-gray-100" />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="last_name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Last Name</FormLabel>
                  <FormControl>
                    <Input {...field} readOnly className="bg-gray-100" />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="vendor_code"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Vendor Code</FormLabel>
                  <FormControl>
                    <Input {...field} readOnly className="bg-gray-100" />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="company_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Company Name</FormLabel>
                  <FormControl>
                    <Input {...field} readOnly className="bg-gray-100" />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input {...field} readOnly className="bg-gray-100" />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Phone</FormLabel>
                  <FormControl>
                    <Input {...field} readOnly className="bg-gray-100" />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <FormItem className="col-span-2">
                  <FormLabel>Address</FormLabel>
                  <FormControl>
                    <Textarea {...field} readOnly className="bg-gray-100" />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="parent_company"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Parent Company</FormLabel>
                  <FormControl>
                    <Input {...field} readOnly className="bg-gray-100" />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="postal_code"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Postal Code</FormLabel>
                  <FormControl>
                    <Input {...field} readOnly className="bg-gray-100" />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
        </Form>
      )}

      {activeTab === "passkeys" && (
        <>
          <div className="flex items-center justify-between px-4 py-3 border-b">
            <h3 className="text-lg font-semibold text-gray-900"></h3>
            <div className="flex gap-2">
              <button
                onClick={() => {
                  setDialogType("add");
                  setConfirmDialogOpen(true);
                }}
                disabled={loading}
                className="flex items-center gap-1 px-3 py-1.5 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
              >
                {loading ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Plus className="w-4 h-4" />
                )}
                {loading ? "Adding..." : "Add Pass Key"}
              </button>
              <button
                onClick={() => {
                  setDialogType("regenerate");
                  setConfirmDialogOpen(true);
                }}
                disabled={regenerateLoading}
                className="flex items-center gap-1 px-3 py-1.5 text-sm bg-orange-600 text-white rounded hover:bg-orange-700"
              >
                {regenerateLoading ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <RefreshCw className="w-4 h-4" />
                )}
                {regenerateLoading ? "Regenerating..." : "Regenerate All"}
              </button>
            </div>
          </div>

          <div>
            {isLoading ? (
              <div>Loading Pass Keys...</div>
            ) : (
              <ul className="space-y-2 border rounded-md p-4 bg-gray-50">
                {vendorData?.message?.data?.passkeys?.length ? (
                  vendorData.message.data.passkeys.map(
                    (key: any, index: number) => (
                      <li
                        key={index}
                        className="flex items-center justify-between bg-gray-50 px-3 py-2 rounded border text-gray-700"
                      >
                        <span className="break-all">{key}</span>
                        <button
                          onClick={() => {
                            setDialogType("delete");
                            handleDeletePassKey(key);
                          }}
                          className="text-red-600 hover:text-red-800 transition-colors"
                          disabled={deleteLoading}
                        >
                          {deleteLoading && passKeyToDelete === key ? (
                            <Loader2 className="w-4 h-4 animate-spin" />
                          ) : null}
                          {!(deleteLoading && passKeyToDelete === key) ? (
                            <Trash2 className="w-4 h-4" />
                          ) : null}
                        </button>
                      </li>
                    )
                  )
                ) : (
                  <li>No pass keys available</li>
                )}
              </ul>
            )}
          </div>
          <ConfirmDialog
            open={confirmDialogOpen}
            onClose={() => {
              setConfirmDialogOpen(false);
              setPassKeyToDelete(null);
              setDialogType(null);
            }}
            onConfirm={
              dialogType === "delete"
                ? confirmDeletePassKey
                : dialogType === "add"
                ? handleAddPassKey
                : handleRegenerateAllPassKeys
            }
            title={
              dialogType === "delete"
                ? "Remove pass key?"
                : dialogType === "add"
                ? "Add a new pass key?"
                : "Regenerate all pass keys?"
            }
            description={
              dialogType === "delete"
                ? "Are you sure you want to remove this pass key?"
                : dialogType === "add"
                ? "Are you sure you want to add a new pass key?"
                : "Are you sure you want to regenerate all pass keys? This will replace existing ones."
            }
          />
        </>
      )}
    </div>
  );
}
