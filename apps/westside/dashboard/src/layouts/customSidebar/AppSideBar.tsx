import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import { MenuItems } from "@/config/sidebar";
import { Link, useLocation } from "react-router-dom";
import { useState } from "react";
import { useAuthContext } from "@/lib/providers/context/AuthContext";
import { ChevronDown, ChevronRight } from "lucide-react";

export function AppSidebar() {
  const { pathname } = useLocation();

  const [activeMenu, setActiveMenu] = useState<string | null>(null);

  const { role } = useAuthContext();

  // Handle submenu toggling
  const handleSubMenuToggle = (menuTitle: string) => {
    setActiveMenu((prev) => (prev === menuTitle ? null : menuTitle));
  };

  // Handle logout logic
  // const handleConfirmLogout = () => {
  //     localStorage.clear(); // Clear local storage
  //     window.location.href = "/auth/login";
  // };

  return (
    <Sidebar className=" top-[110px] bottom-0 border-none shadow-none print:hidden">
      <SidebarContent className="py-2 overflow-y-auto max-h-[calc(100vh-120px)]">
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              {MenuItems.map((item) => {
                if (item?.roles.includes((role as "Admin" | "Vendor" | "Customer") || "Customer")) {
                  return (
                    <div key={item.title}>
                      <SidebarMenuItem>
                        <SidebarMenuButton
                          isActive={pathname?.startsWith(item?.url)}
                          onClick={() => item?.subMenu && handleSubMenuToggle(item?.title)}
                          asChild
                          className="text-white"
                        >
                          <Link to={item.url} className="flex justify-between items-center">
                            <p className="inline-flex gap-2 items-center">
                              <item.icon size={20} />
                              <span className="text-[15px] ">{item.title}</span>
                            </p>
                            {item.subMenu &&
                              (activeMenu === item.title ? (
                                <ChevronDown className="w-4 h-4 transition-transform duration-200" />
                              ) : (
                                <ChevronRight className="w-4 h-4 transition-transform duration-200" />
                              ))}
                          </Link>
                        </SidebarMenuButton>
                      </SidebarMenuItem>
                      {item?.subMenu && activeMenu === item?.title && (
                        <SidebarMenu>
                          {item?.subMenu?.map((subItem) => {
                            if (!subItem?.roles) {
                              return (
                                <SidebarMenuItem className="" key={subItem?.title}>
                                  <SidebarMenuButton
                                    className="pl-8 text-white"
                                    asChild
                                    isActive={subItem?.url === pathname}
                                  >
                                    <Link to={subItem?.url}>
                                      <span className="text-[15px]">{subItem?.title}</span>
                                    </Link>
                                  </SidebarMenuButton>
                                </SidebarMenuItem>
                              );
                            } else if (
                              subItem?.roles &&
                              subItem?.roles?.includes((role as "Admin" | "Vendor" | "Customer") || "Customer")
                            ) {
                              return (
                                <SidebarMenuItem key={subItem?.title}>
                                  <SidebarMenuButton className="pl-8" asChild isActive={subItem?.url === pathname}>
                                    <Link to={subItem?.url}>
                                      <span>{subItem?.title}</span>
                                    </Link>
                                  </SidebarMenuButton>
                                </SidebarMenuItem>
                              );
                            }
                          })}
                        </SidebarMenu>
                      )}
                    </div>
                  );
                }
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}
