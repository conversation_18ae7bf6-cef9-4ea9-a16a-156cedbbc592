// Icons
import { ChevronDown, Search } from "lucide-react";
// custom component
import { Typography } from "@/components/typography";
import { <PERSON><PERSON>r, MenubarContent, MenubarItem, MenubarMenu, MenubarTrigger } from "@/components/ui/menubar";
import { MenuItems } from "@/config/sidebar";
import { useAuthContext } from "@/lib/providers/context/AuthContext";
import { Link } from "react-router-dom";

const BottomHeader = () => {
  const { role } = useAuthContext();

  return (
    <div className="h-10 bg-sidebar px-2">
      <div className="h-full flex justify-between gap-2 items-center">
        <Menubar className="bg-transparent text-sidebar-foreground border-0  divide-sidebar-foreground">
          {MenuItems?.map((item) => {
            if (item?.roles.includes((role as "Admin" | "Vendor" | "Customer") || "Customer")) {
              return (
                <MenubarMenu key={item?.title}>
                  <MenubarTrigger className="">
                    <Link className="text-white text-[15px]" to={item.url}>{item?.title}</Link>
                  </MenubarTrigger>
                  {item?.subMenu && (
                    <MenubarContent>
                      {item?.subMenu?.map((subItem) => {
                        if (!subItem?.roles) {
                          return (
                            <MenubarItem>
                              <Link to={subItem?.url}>{subItem?.title}</Link>
                            </MenubarItem>
                          );
                        } else if (
                          subItem?.roles &&
                          subItem?.roles?.includes((role as "Admin" | "Vendor" | "Customer") || "Customer")
                        ) {
                          return (
                            <MenubarItem>
                              <Link to={subItem?.url}>{subItem?.title}</Link>
                            </MenubarItem>
                          );
                        }
                      })}
                    </MenubarContent>
                  )}
                </MenubarMenu>
              );
            }
          })}
        </Menubar>
        {/* Right Part */}
        <div className="flex items-center gap-4 divide-x divide-sidebar-foreground">
          <Typography variant={"p"} weight={"medium"} className="text-sidebar-primary-foreground cursor-pointer pr-4">
            <Search size={20} />
          </Typography>
          <Typography
            variant={"p"}
            weight={"medium"}
            className="text-sidebar-primary-foreground cursor-pointer flex gap-0.5 items-center"
          >
            <span className="text-primary">EN</span> <ChevronDown size={10} />
          </Typography>
        </div>
      </div>
    </div>
  );
};

export default BottomHeader;
