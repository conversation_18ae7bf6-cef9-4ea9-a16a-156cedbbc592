2025-09-30 11:03:44,213 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocket DB` ADD COLUMN `shipper_id` varchar(140)
2025-09-30 11:05:21,161 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-10-03 18:59:20,407 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-10-03 18:59:21,277 WARNING database DDL Query made to DB:
ALTER TABLE `tabVendor` ADD COLUMN `vendor_code` varchar(140)
2025-10-03 18:59:21,427 WARNING database DDL Query made to DB:
create table `tabPassword Reset OTP` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user_id` varchar(140),
`otp` varchar(140),
`expiry_on` datetime(6),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
