set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250324_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-03-24 18:00:03.522336
Config  : ./westside.local/private/backups/20250324_180002-westside_local-site_config_backup.json 94.0B
Database: ./westside.local/private/backups/20250324_180002-westside_local-database.sql.gz         6.9MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250325_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-03-25 12:00:03.250976
Config  : ./westside.local/private/backups/20250325_120002-westside_local-site_config_backup.json 94.0B
Database: ./westside.local/private/backups/20250325_120002-westside_local-database.sql.gz         6.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250325_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-03-25 18:00:02.924323
Config  : ./westside.local/private/backups/20250325_180002-westside_local-site_config_backup.json 94.0B
Database: ./westside.local/private/backups/20250325_180002-westside_local-database.sql.gz         3.6MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250326_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-03-26 12:00:02.294529
Config  : ./westside.local/private/backups/20250326_120001-westside_local-site_config_backup.json 94.0B
Database: ./westside.local/private/backups/20250326_120001-westside_local-database.sql.gz         3.5MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250326_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-03-26 18:00:03.436812
Config  : ./westside.local/private/backups/20250326_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250326_180001-westside_local-database.sql.gz         3.6MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250327_000001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-03-27 00:00:03.547731
Config  : ./westside.local/private/backups/20250327_000001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250327_000001-westside_local-database.sql.gz         3.6MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250327_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-03-27 12:00:03.694044
Config  : ./westside.local/private/backups/20250327_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250327_120002-westside_local-database.sql.gz         3.6MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250327_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-03-27 18:00:02.367600
Config  : ./westside.local/private/backups/20250327_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250327_180001-westside_local-database.sql.gz         3.6MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250328_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-03-28 12:00:02.548428
Config  : ./westside.local/private/backups/20250328_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250328_120001-westside_local-database.sql.gz         3.6MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250401_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-01 12:00:03.643387
Config  : ./westside.local/private/backups/20250401_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250401_120002-westside_local-database.sql.gz         3.6MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250401_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-01 18:00:02.171862
Config  : ./westside.local/private/backups/20250401_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250401_180001-westside_local-database.sql.gz         3.6MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250402_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-02 12:00:02.983726
Config  : ./westside.local/private/backups/20250402_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250402_120002-westside_local-database.sql.gz         3.6MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250403_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-03 12:00:02.916227
Config  : ./westside.local/private/backups/20250403_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250403_120002-westside_local-database.sql.gz         3.6MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250403_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-03 18:00:03.070232
Config  : ./westside.local/private/backups/20250403_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250403_180001-westside_local-database.sql.gz         3.6MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250404_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-04 12:00:03.863257
Config  : ./westside.local/private/backups/20250404_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250404_120002-westside_local-database.sql.gz         3.6MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250404_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-04 18:00:03.726916
Config  : ./westside.local/private/backups/20250404_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250404_180001-westside_local-database.sql.gz         3.6MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250407_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-07 12:00:02.738555
Config  : ./westside.local/private/backups/20250407_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250407_120001-westside_local-database.sql.gz         3.6MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250407_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-07 18:00:03.317285
Config  : ./westside.local/private/backups/20250407_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250407_180001-westside_local-database.sql.gz         3.6MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250408_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-08 12:00:03.408753
Config  : ./westside.local/private/backups/20250408_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250408_120001-westside_local-database.sql.gz         3.6MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250408_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-08 18:00:03.219445
Config  : ./westside.local/private/backups/20250408_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250408_180002-westside_local-database.sql.gz         3.7MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250409_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-09 12:00:03.696998
Config  : ./westside.local/private/backups/20250409_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250409_120002-westside_local-database.sql.gz         3.7MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250409_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-09 18:00:03.899861
Config  : ./westside.local/private/backups/20250409_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250409_180002-westside_local-database.sql.gz         3.7MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250410_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-10 12:00:03.695082
Config  : ./westside.local/private/backups/20250410_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250410_120002-westside_local-database.sql.gz         3.7MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250410_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-10 18:00:03.178933
Config  : ./westside.local/private/backups/20250410_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250410_180002-westside_local-database.sql.gz         3.7MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250411_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-11 12:00:03.010929
Config  : ./westside.local/private/backups/20250411_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250411_120002-westside_local-database.sql.gz         3.7MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250415_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-15 12:00:02.677074
Config  : ./westside.local/private/backups/20250415_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250415_120001-westside_local-database.sql.gz         3.6MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250415_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-15 18:00:02.888031
Config  : ./westside.local/private/backups/20250415_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250415_180002-westside_local-database.sql.gz         3.7MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250416_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-16 12:00:02.729975
Config  : ./westside.local/private/backups/20250416_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250416_120001-westside_local-database.sql.gz         3.7MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250416_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-16 18:00:03.030519
Config  : ./westside.local/private/backups/20250416_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250416_180001-westside_local-database.sql.gz         3.7MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250417_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-17 12:00:02.809532
Config  : ./westside.local/private/backups/20250417_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250417_120001-westside_local-database.sql.gz         3.7MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250417_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-17 18:00:02.618259
Config  : ./westside.local/private/backups/20250417_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250417_180001-westside_local-database.sql.gz         3.7MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250421_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-21 12:00:03.779313
Config  : ./westside.local/private/backups/20250421_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250421_120002-westside_local-database.sql.gz         3.7MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250422_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-22 12:00:03.147594
Config  : ./westside.local/private/backups/20250422_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250422_120002-westside_local-database.sql.gz         3.7MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250422_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-22 18:00:03.640496
Config  : ./westside.local/private/backups/20250422_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250422_180002-westside_local-database.sql.gz         3.7MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250423_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-23 12:00:02.542753
Config  : ./westside.local/private/backups/20250423_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250423_120001-westside_local-database.sql.gz         3.7MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250423_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-23 18:00:02.949137
Config  : ./westside.local/private/backups/20250423_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250423_180002-westside_local-database.sql.gz         3.7MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250424_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-24 12:00:04.111774
Config  : ./westside.local/private/backups/20250424_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250424_120002-westside_local-database.sql.gz         3.7MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250424_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-24 18:00:03.057792
Config  : ./westside.local/private/backups/20250424_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250424_180002-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250425_000002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-25 00:00:04.319444
Config  : ./westside.local/private/backups/20250425_000002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250425_000002-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250425_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-25 12:00:03.574962
Config  : ./westside.local/private/backups/20250425_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250425_120002-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250428_000002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-28 00:00:04.271445
Config  : ./westside.local/private/backups/20250428_000002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250428_000002-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250428_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-28 12:00:02.603856
Config  : ./westside.local/private/backups/20250428_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250428_120001-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250428_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-28 18:00:02.996554
Config  : ./westside.local/private/backups/20250428_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250428_180001-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250429_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-29 12:00:03.308828
Config  : ./westside.local/private/backups/20250429_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250429_120002-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250429_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-29 18:00:03.389785
Config  : ./westside.local/private/backups/20250429_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250429_180002-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250430_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-30 12:00:02.885785
Config  : ./westside.local/private/backups/20250430_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250430_120001-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250430_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-04-30 18:00:03.208678
Config  : ./westside.local/private/backups/20250430_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250430_180002-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250502_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-02 12:00:02.687215
Config  : ./westside.local/private/backups/20250502_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250502_120001-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250502_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-02 18:00:02.885869
Config  : ./westside.local/private/backups/20250502_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250502_180002-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250505_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-05 12:00:04.689831
Config  : ./westside.local/private/backups/20250505_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250505_120002-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250505_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-05 18:00:02.896719
Config  : ./westside.local/private/backups/20250505_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250505_180002-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250506_000002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-06 00:00:06.333786
Config  : ./westside.local/private/backups/20250506_000002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250506_000002-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250506_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-06 12:00:02.875756
Config  : ./westside.local/private/backups/20250506_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250506_120001-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250506_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-06 18:00:02.313038
Config  : ./westside.local/private/backups/20250506_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250506_180001-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250507_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-07 12:00:03.909755
Config  : ./westside.local/private/backups/20250507_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250507_120001-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250507_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-07 18:00:02.427556
Config  : ./westside.local/private/backups/20250507_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250507_180001-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250508_000002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-08 00:00:03.862277
Config  : ./westside.local/private/backups/20250508_000002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250508_000002-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250508_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-08 12:00:03.209603
Config  : ./westside.local/private/backups/20250508_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250508_120001-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250508_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-08 18:00:03.185048
Config  : ./westside.local/private/backups/20250508_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250508_180001-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250509_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-09 12:00:03.818950
Config  : ./westside.local/private/backups/20250509_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250509_120002-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250512_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-12 12:00:02.744943
Config  : ./westside.local/private/backups/20250512_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250512_120001-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250512_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-12 18:00:03.102186
Config  : ./westside.local/private/backups/20250512_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250512_180002-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250513_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-13 12:00:02.682830
Config  : ./westside.local/private/backups/20250513_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250513_120001-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250513_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-13 18:00:02.791368
Config  : ./westside.local/private/backups/20250513_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250513_180001-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250514_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-14 12:00:03.050558
Config  : ./westside.local/private/backups/20250514_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250514_120002-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250514_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-14 18:00:03.187745
Config  : ./westside.local/private/backups/20250514_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250514_180002-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250515_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-15 12:00:03.305384
Config  : ./westside.local/private/backups/20250515_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250515_120001-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250515_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-15 18:00:02.862069
Config  : ./westside.local/private/backups/20250515_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250515_180001-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250516_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-16 12:00:03.721441
Config  : ./westside.local/private/backups/20250516_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250516_120002-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250520_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-20 12:00:03.661465
Config  : ./westside.local/private/backups/20250520_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250520_120001-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250520_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-20 18:00:02.244216
Config  : ./westside.local/private/backups/20250520_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250520_180001-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250521_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-21 12:00:02.298308
Config  : ./westside.local/private/backups/20250521_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250521_120001-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250521_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-21 18:00:02.358365
Config  : ./westside.local/private/backups/20250521_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250521_180001-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250522_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-22 12:00:02.530822
Config  : ./westside.local/private/backups/20250522_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250522_120001-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250522_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-22 18:00:02.203642
Config  : ./westside.local/private/backups/20250522_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250522_180001-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250523_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-23 12:00:02.773992
Config  : ./westside.local/private/backups/20250523_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250523_120001-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250526_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-26 12:00:03.576753
Config  : ./westside.local/private/backups/20250526_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250526_120002-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250526_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-26 18:00:03.656923
Config  : ./westside.local/private/backups/20250526_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250526_180002-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250527_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-27 12:00:03.731160
Config  : ./westside.local/private/backups/20250527_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250527_120002-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250527_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-27 18:00:04.302006
Config  : ./westside.local/private/backups/20250527_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250527_180002-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250528_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-28 12:00:03.443708
Config  : ./westside.local/private/backups/20250528_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250528_120002-westside_local-database.sql.gz         3.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250529_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-29 12:00:03.533644
Config  : ./westside.local/private/backups/20250529_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250529_120002-westside_local-database.sql.gz         3.9MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250529_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-29 18:00:03.339335
Config  : ./westside.local/private/backups/20250529_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250529_180002-westside_local-database.sql.gz         3.9MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250530_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-05-30 12:00:03.169977
Config  : ./westside.local/private/backups/20250530_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250530_120002-westside_local-database.sql.gz         3.9MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250602_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-02 12:00:03.470820
Config  : ./westside.local/private/backups/20250602_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250602_120002-westside_local-database.sql.gz         3.9MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250602_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-02 18:00:03.033869
Config  : ./westside.local/private/backups/20250602_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250602_180001-westside_local-database.sql.gz         3.9MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250603_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-03 12:00:02.724016
Config  : ./westside.local/private/backups/20250603_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250603_120001-westside_local-database.sql.gz         3.9MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250603_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-03 18:00:04.045817
Config  : ./westside.local/private/backups/20250603_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250603_180002-westside_local-database.sql.gz         8.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250604_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-04 12:00:05.515364
Config  : ./westside.local/private/backups/20250604_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250604_120002-westside_local-database.sql.gz         8.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250604_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-04 18:00:05.493939
Config  : ./westside.local/private/backups/20250604_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250604_180002-westside_local-database.sql.gz         8.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250605_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-05 12:00:06.149809
Config  : ./westside.local/private/backups/20250605_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250605_120001-westside_local-database.sql.gz         8.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250605_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-05 18:00:05.346172
Config  : ./westside.local/private/backups/20250605_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250605_180002-westside_local-database.sql.gz         8.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250609_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-09 12:00:03.440678
Config  : ./westside.local/private/backups/20250609_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250609_120001-westside_local-database.sql.gz         7.9MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250609_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-09 18:00:04.011020
Config  : ./westside.local/private/backups/20250609_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250609_180002-westside_local-database.sql.gz         7.9MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250610_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-10 12:00:04.044089
Config  : ./westside.local/private/backups/20250610_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250610_120002-westside_local-database.sql.gz         7.9MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250610_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-10 18:00:04.150597
Config  : ./westside.local/private/backups/20250610_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250610_180002-westside_local-database.sql.gz         8.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250611_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-11 12:00:03.965847
Config  : ./westside.local/private/backups/20250611_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250611_120002-westside_local-database.sql.gz         7.9MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250611_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-11 18:00:03.126721
Config  : ./westside.local/private/backups/20250611_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250611_180001-westside_local-database.sql.gz         8.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250612_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-12 12:00:03.627492
Config  : ./westside.local/private/backups/20250612_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250612_120001-westside_local-database.sql.gz         8.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250612_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-12 18:00:04.018568
Config  : ./westside.local/private/backups/20250612_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250612_180002-westside_local-database.sql.gz         8.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250613_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-13 12:00:03.984494
Config  : ./westside.local/private/backups/20250613_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250613_120002-westside_local-database.sql.gz         8.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250613_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-13 18:00:05.480642
Config  : ./westside.local/private/backups/20250613_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250613_180002-westside_local-database.sql.gz         8.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250616_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-16 12:00:03.956813
Config  : ./westside.local/private/backups/20250616_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250616_120001-westside_local-database.sql.gz         8.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250616_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-16 18:00:05.247769
Config  : ./westside.local/private/backups/20250616_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250616_180002-westside_local-database.sql.gz         8.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250617_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-17 12:00:03.972212
Config  : ./westside.local/private/backups/20250617_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250617_120001-westside_local-database.sql.gz         8.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250617_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-17 18:00:05.266903
Config  : ./westside.local/private/backups/20250617_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250617_180002-westside_local-database.sql.gz         8.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250618_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-18 12:00:04.431854
Config  : ./westside.local/private/backups/20250618_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250618_120001-westside_local-database.sql.gz         8.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250618_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-18 18:00:03.247809
Config  : ./westside.local/private/backups/20250618_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250618_180001-westside_local-database.sql.gz         8.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250619_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-19 12:00:03.803018
Config  : ./westside.local/private/backups/20250619_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250619_120002-westside_local-database.sql.gz         8.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250619_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-19 18:00:03.772706
Config  : ./westside.local/private/backups/20250619_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250619_180001-westside_local-database.sql.gz         8.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250620_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-20 12:00:06.016724
Config  : ./westside.local/private/backups/20250620_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250620_120002-westside_local-database.sql.gz         8.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250621_000001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-21 00:00:04.170547
Config  : ./westside.local/private/backups/20250621_000001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250621_000001-westside_local-database.sql.gz         8.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250623_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-23 12:00:03.512621
Config  : ./westside.local/private/backups/20250623_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250623_120001-westside_local-database.sql.gz         8.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250623_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-23 18:00:03.932613
Config  : ./westside.local/private/backups/20250623_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250623_180002-westside_local-database.sql.gz         8.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250624_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-24 12:00:04.943631
Config  : ./westside.local/private/backups/20250624_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250624_120001-westside_local-database.sql.gz         8.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250624_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-24 18:00:03.627287
Config  : ./westside.local/private/backups/20250624_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250624_180001-westside_local-database.sql.gz         8.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250625_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-25 12:00:03.422880
Config  : ./westside.local/private/backups/20250625_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250625_120001-westside_local-database.sql.gz         8.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250625_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-25 18:00:04.071289
Config  : ./westside.local/private/backups/20250625_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250625_180001-westside_local-database.sql.gz         8.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250626_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-26 12:00:03.429088
Config  : ./westside.local/private/backups/20250626_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250626_120001-westside_local-database.sql.gz         8.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250626_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-26 18:00:04.127443
Config  : ./westside.local/private/backups/20250626_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250626_180002-westside_local-database.sql.gz         8.2MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250627_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-27 12:00:03.801828
Config  : ./westside.local/private/backups/20250627_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250627_120002-westside_local-database.sql.gz         6.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250630_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-30 12:00:03.272208
Config  : ./westside.local/private/backups/20250630_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250630_120001-westside_local-database.sql.gz         6.3MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250630_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-06-30 18:00:03.064987
Config  : ./westside.local/private/backups/20250630_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250630_180001-westside_local-database.sql.gz         6.4MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250701_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-01 12:00:05.040814
Config  : ./westside.local/private/backups/20250701_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250701_120002-westside_local-database.sql.gz         6.3MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250701_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-01 18:00:02.921457
Config  : ./westside.local/private/backups/20250701_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250701_180001-westside_local-database.sql.gz         6.4MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250702_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-02 12:00:03.783650
Config  : ./westside.local/private/backups/20250702_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250702_120002-westside_local-database.sql.gz         6.4MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250702_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-02 18:00:03.542607
Config  : ./westside.local/private/backups/20250702_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250702_180002-westside_local-database.sql.gz         6.4MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250703_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-03 12:00:03.442817
Config  : ./westside.local/private/backups/20250703_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250703_120001-westside_local-database.sql.gz         6.4MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250703_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-03 18:00:04.011987
Config  : ./westside.local/private/backups/20250703_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250703_180001-westside_local-database.sql.gz         6.4MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250704_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-04 12:00:03.430023
Config  : ./westside.local/private/backups/20250704_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250704_120001-westside_local-database.sql.gz         6.4MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250707_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-07 12:00:03.658823
Config  : ./westside.local/private/backups/20250707_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250707_120002-westside_local-database.sql.gz         6.4MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250707_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-07 18:00:03.220622
Config  : ./westside.local/private/backups/20250707_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250707_180001-westside_local-database.sql.gz         6.4MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250708_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-08 12:00:04.376584
Config  : ./westside.local/private/backups/20250708_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250708_120002-westside_local-database.sql.gz         6.4MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250708_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-08 18:00:03.735526
Config  : ./westside.local/private/backups/20250708_180002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250708_180002-westside_local-database.sql.gz         6.4MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250709_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-09 12:00:04.434987
Config  : ./westside.local/private/backups/20250709_120002-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250709_120002-westside_local-database.sql.gz         6.4MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250710_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-10 12:00:03.111732
Config  : ./westside.local/private/backups/20250710_120001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250710_120001-westside_local-database.sql.gz         6.4MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250710_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-10 18:00:03.329812
Config  : ./westside.local/private/backups/20250710_180001-westside_local-site_config_backup.json 161.0B
Database: ./westside.local/private/backups/20250710_180001-westside_local-database.sql.gz         6.4MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250711_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-11 12:00:05.139629
Config  : ./westside.local/private/backups/20250711_120002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250711_120002-westside_local-database.sql.gz         13.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250711_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-11 18:00:05.096619
Config  : ./westside.local/private/backups/20250711_180001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250711_180001-westside_local-database.sql.gz         13.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250714_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-14 12:00:04.555418
Config  : ./westside.local/private/backups/20250714_120002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250714_120002-westside_local-database.sql.gz         11.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250714_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-14 18:00:03.806982
Config  : ./westside.local/private/backups/20250714_180001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250714_180001-westside_local-database.sql.gz         11.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250716_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-16 12:00:04.076401
Config  : ./westside.local/private/backups/20250716_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250716_120001-westside_local-database.sql.gz         10.9MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250716_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-16 18:00:04.373228
Config  : ./westside.local/private/backups/20250716_180001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250716_180001-westside_local-database.sql.gz         11.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250717_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-17 12:00:04.017488
Config  : ./westside.local/private/backups/20250717_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250717_120001-westside_local-database.sql.gz         11.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250717_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-17 18:00:04.161241
Config  : ./westside.local/private/backups/20250717_180001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250717_180001-westside_local-database.sql.gz         11.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250718_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-18 12:00:04.974215
Config  : ./westside.local/private/backups/20250718_120002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250718_120002-westside_local-database.sql.gz         11.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250721_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-21 12:00:04.538036
Config  : ./westside.local/private/backups/20250721_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250721_120001-westside_local-database.sql.gz         10.7MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250721_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-21 18:00:06.650678
Config  : ./westside.local/private/backups/20250721_180002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250721_180002-westside_local-database.sql.gz         10.7MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250723_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-23 12:00:05.261827
Config  : ./westside.local/private/backups/20250723_120002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250723_120002-westside_local-database.sql.gz         10.7MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250723_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-23 18:00:04.062150
Config  : ./westside.local/private/backups/20250723_180001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250723_180001-westside_local-database.sql.gz         10.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250724_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-24 12:00:05.693602
Config  : ./westside.local/private/backups/20250724_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250724_120001-westside_local-database.sql.gz         10.7MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250724_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-24 18:00:05.042475
Config  : ./westside.local/private/backups/20250724_180001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250724_180001-westside_local-database.sql.gz         10.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250725_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-25 12:00:06.148376
Config  : ./westside.local/private/backups/20250725_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250725_120001-westside_local-database.sql.gz         10.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250725_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-25 18:00:04.944163
Config  : ./westside.local/private/backups/20250725_180002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250725_180002-westside_local-database.sql.gz         10.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250728_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-28 12:00:04.458644
Config  : ./westside.local/private/backups/20250728_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250728_120001-westside_local-database.sql.gz         10.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250728_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-28 18:00:05.960358
Config  : ./westside.local/private/backups/20250728_180002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250728_180002-westside_local-database.sql.gz         10.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250729_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-29 12:00:04.417817
Config  : ./westside.local/private/backups/20250729_120002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250729_120002-westside_local-database.sql.gz         10.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250729_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-29 18:00:06.489100
Config  : ./westside.local/private/backups/20250729_180002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250729_180002-westside_local-database.sql.gz         10.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250731_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-07-31 12:00:04.361525
Config  : ./westside.local/private/backups/20250731_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250731_120001-westside_local-database.sql.gz         10.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250801_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-01 12:00:05.811865
Config  : ./westside.local/private/backups/20250801_120002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250801_120002-westside_local-database.sql.gz         10.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250801_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-01 18:00:05.068696
Config  : ./westside.local/private/backups/20250801_180001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250801_180001-westside_local-database.sql.gz         10.8MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250804_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-04 12:00:03.776157
Config  : ./westside.local/private/backups/20250804_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250804_120001-westside_local-database.sql.gz         10.9MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250804_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-04 18:00:03.654579
Config  : ./westside.local/private/backups/20250804_180001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250804_180001-westside_local-database.sql.gz         10.9MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250805_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-05 12:00:04.836466
Config  : ./westside.local/private/backups/20250805_120002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250805_120002-westside_local-database.sql.gz         10.9MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250805_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-05 18:00:05.863336
Config  : ./westside.local/private/backups/20250805_180002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250805_180002-westside_local-database.sql.gz         10.9MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250806_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-06 12:00:05.434822
Config  : ./westside.local/private/backups/20250806_120002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250806_120002-westside_local-database.sql.gz         10.9MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250806_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-06 18:00:06.103645
Config  : ./westside.local/private/backups/20250806_180001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250806_180001-westside_local-database.sql.gz         10.9MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250807_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-07 12:00:04.294562
Config  : ./westside.local/private/backups/20250807_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250807_120001-westside_local-database.sql.gz         10.9MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250807_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-07 18:00:06.611005
Config  : ./westside.local/private/backups/20250807_180002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250807_180002-westside_local-database.sql.gz         10.9MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250808_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-08 12:00:04.450279
Config  : ./westside.local/private/backups/20250808_120002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250808_120002-westside_local-database.sql.gz         10.9MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250808_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-08 18:00:04.397895
Config  : ./westside.local/private/backups/20250808_180002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250808_180002-westside_local-database.sql.gz         10.9MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250811_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-11 12:00:05.814990
Config  : ./westside.local/private/backups/20250811_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250811_120001-westside_local-database.sql.gz         10.9MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250811_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-11 18:00:03.888090
Config  : ./westside.local/private/backups/20250811_180001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250811_180001-westside_local-database.sql.gz         11.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250812_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-12 12:00:04.905757
Config  : ./westside.local/private/backups/20250812_120002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250812_120002-westside_local-database.sql.gz         11.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250812_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-12 18:00:04.543068
Config  : ./westside.local/private/backups/20250812_180001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250812_180001-westside_local-database.sql.gz         11.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250813_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-13 12:00:04.205130
Config  : ./westside.local/private/backups/20250813_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250813_120001-westside_local-database.sql.gz         11.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250813_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-13 18:00:04.544141
Config  : ./westside.local/private/backups/20250813_180001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250813_180001-westside_local-database.sql.gz         11.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250814_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-14 12:00:05.371779
Config  : ./westside.local/private/backups/20250814_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250814_120001-westside_local-database.sql.gz         11.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250818_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-18 12:00:04.280955
Config  : ./westside.local/private/backups/20250818_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250818_120001-westside_local-database.sql.gz         11.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250818_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-18 18:00:04.500500
Config  : ./westside.local/private/backups/20250818_180002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250818_180002-westside_local-database.sql.gz         11.0MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250819_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-19 12:00:04.757795
Config  : ./westside.local/private/backups/20250819_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250819_120001-westside_local-database.sql.gz         11.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250819_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-19 18:00:03.815190
Config  : ./westside.local/private/backups/20250819_180001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250819_180001-westside_local-database.sql.gz         11.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250820_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-20 12:00:04.337659
Config  : ./westside.local/private/backups/20250820_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250820_120001-westside_local-database.sql.gz         11.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250820_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-20 18:00:05.944513
Config  : ./westside.local/private/backups/20250820_180001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250820_180001-westside_local-database.sql.gz         11.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250822_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-22 12:00:08.477424
Config  : ./westside.local/private/backups/20250822_120002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250822_120002-westside_local-database.sql.gz         11.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250822_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-22 18:00:03.729149
Config  : ./westside.local/private/backups/20250822_180001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250822_180001-westside_local-database.sql.gz         11.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250826_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-26 12:00:04.956263
Config  : ./westside.local/private/backups/20250826_120002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250826_120002-westside_local-database.sql.gz         11.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250826_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-26 18:00:04.583768
Config  : ./westside.local/private/backups/20250826_180002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250826_180002-westside_local-database.sql.gz         11.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250827_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-27 12:00:06.067594
Config  : ./westside.local/private/backups/20250827_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250827_120001-westside_local-database.sql.gz         11.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250828_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-28 12:00:04.520665
Config  : ./westside.local/private/backups/20250828_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250828_120001-westside_local-database.sql.gz         11.2MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250828_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-28 18:00:04.902788
Config  : ./westside.local/private/backups/20250828_180002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250828_180002-westside_local-database.sql.gz         11.2MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250831_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-31 12:00:05.927346
Config  : ./westside.local/private/backups/20250831_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250831_120001-westside_local-database.sql.gz         11.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250831_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-08-31 18:00:06.594690
Config  : ./westside.local/private/backups/20250831_180001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250831_180001-westside_local-database.sql.gz         11.2MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250901_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-09-01 12:00:04.380719
Config  : ./westside.local/private/backups/20250901_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250901_120001-westside_local-database.sql.gz         11.2MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250901_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-09-01 18:00:04.512279
Config  : ./westside.local/private/backups/20250901_180001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250901_180001-westside_local-database.sql.gz         11.2MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250902_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-09-02 12:00:05.145794
Config  : ./westside.local/private/backups/20250902_120002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250902_120002-westside_local-database.sql.gz         11.2MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250902_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-09-02 18:00:05.140498
Config  : ./westside.local/private/backups/20250902_180002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250902_180002-westside_local-database.sql.gz         11.2MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250903_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-09-03 12:00:03.775775
Config  : ./westside.local/private/backups/20250903_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250903_120001-westside_local-database.sql.gz         11.2MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250908_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-09-08 12:00:05.924568
Config  : ./westside.local/private/backups/20250908_120002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250908_120002-westside_local-database.sql.gz         11.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250908_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-09-08 18:00:04.389337
Config  : ./westside.local/private/backups/20250908_180002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250908_180002-westside_local-database.sql.gz         11.2MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250909_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-09-09 12:00:04.177019
Config  : ./westside.local/private/backups/20250909_120002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250909_120002-westside_local-database.sql.gz         11.1MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250909_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-09-09 18:00:04.291389
Config  : ./westside.local/private/backups/20250909_180002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250909_180002-westside_local-database.sql.gz         11.2MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250910_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-09-10 12:00:03.840807
Config  : ./westside.local/private/backups/20250910_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250910_120001-westside_local-database.sql.gz         11.2MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250910_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-09-10 18:00:05.940986
Config  : ./westside.local/private/backups/20250910_180001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250910_180001-westside_local-database.sql.gz         11.2MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250911_060002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-09-11 06:00:04.430893
Config  : ./westside.local/private/backups/20250911_060002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250911_060002-westside_local-database.sql.gz         11.2MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250911_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-09-11 12:00:04.248963
Config  : ./westside.local/private/backups/20250911_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250911_120001-westside_local-database.sql.gz         11.2MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250911_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-09-11 18:00:03.789899
Config  : ./westside.local/private/backups/20250911_180001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250911_180001-westside_local-database.sql.gz         11.2MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250912_120148-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-09-12 12:01:53.197433
Config  : ./westside.local/private/backups/20250912_120148-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250912_120148-westside_local-database.sql.gz         11.2MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250913_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-09-13 12:00:05.820803
Config  : ./westside.local/private/backups/20250913_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250913_120001-westside_local-database.sql.gz         11.2MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250913_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-09-13 18:00:04.145312
Config  : ./westside.local/private/backups/20250913_180002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250913_180002-westside_local-database.sql.gz         11.2MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250915_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-09-15 12:00:03.524768
Config  : ./westside.local/private/backups/20250915_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250915_120001-westside_local-database.sql.gz         11.2MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250915_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-09-15 18:00:04.176121
Config  : ./westside.local/private/backups/20250915_180001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250915_180001-westside_local-database.sql.gz         11.3MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250916_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-09-16 12:00:03.550359
Config  : ./westside.local/private/backups/20250916_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250916_120001-westside_local-database.sql.gz         11.3MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250916_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-09-16 18:00:07.769337
Config  : ./westside.local/private/backups/20250916_180002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250916_180002-westside_local-database.sql.gz         11.3MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250917_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-09-17 12:00:05.265157
Config  : ./westside.local/private/backups/20250917_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250917_120001-westside_local-database.sql.gz         11.3MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250917_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-09-17 18:00:03.789905
Config  : ./westside.local/private/backups/20250917_180001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250917_180001-westside_local-database.sql.gz         11.3MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250918_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-09-18 12:00:07.635292
Config  : ./westside.local/private/backups/20250918_120002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250918_120002-westside_local-database.sql.gz         11.3MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250918_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-09-18 18:00:04.550999
Config  : ./westside.local/private/backups/20250918_180002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250918_180002-westside_local-database.sql.gz         11.3MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250919_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-09-19 12:00:04.972958
Config  : ./westside.local/private/backups/20250919_120002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250919_120002-westside_local-database.sql.gz         11.3MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250919_180001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-09-19 18:00:05.538763
Config  : ./westside.local/private/backups/20250919_180001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250919_180001-westside_local-database.sql.gz         11.3MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250922_120001-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-09-22 12:00:03.915570
Config  : ./westside.local/private/backups/20250922_120001-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250922_120001-westside_local-database.sql.gz         11.3MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250922_180002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-09-22 18:00:04.484316
Config  : ./westside.local/private/backups/20250922_180002-westside_local-site_config_backup.json 180.0B
Database: ./westside.local/private/backups/20250922_180002-westside_local-database.sql.gz         11.3MiB
Backup for Site westside.local has been successfully completed
set -o pipefail; /usr/bin/mariadb-dump --user=_9daf192e2a9c7a7e --host=127.0.0.1 --port=3306 --password=********** --single-transaction --quick --lock-tables=false _9daf192e2a9c7a7e | /usr/bin/gzip >> ./westside.local/private/backups/20250923_120002-westside_local-database.sql.gz

Backup Summary for westside.local at 2025-09-23 12:00:04.557384
Config  : ./westside.local/private/backups/20250923_120002-westside_local-site_config_backup.json 231.0B
Database: ./westside.local/private/backups/20250923_120002-westside_local-database.sql.gz         10.3MiB
Backup for Site westside.local has been successfully completed
Backup failed for Site local.westside. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['local.westside', 'westside.local'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7f96c4477420>
      exit_code = 0
      rollback_callback = None
      site = 'local.westside'
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7f96c56823e0>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7f96c56823e0>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7f96c3928140>
      db_name = '_07fdbf6ba2f0be13'
      user = '_07fdbf6ba2f0be13'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7f96c56823e0>
      site = 'local.westside'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7f96c3928140>
  File "apps/frappe/frappe/database/mariadb/database.py", line 483, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7f96c33c1fa0>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x7f96c3219550>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_07fdbf6ba2f0be13'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x7f96c33eba60>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x7f96c33ebb00>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7f96c33c1fa0>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_07fdbf6ba2f0be13'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7f96c33c1fa0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7f96c33c1fa0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7f96c33c1fa0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7f96c33c1fa0>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x7f96c34e05f0>
      user = '_07fdbf6ba2f0be13'
      password = ********
      host = '127.0.0.1'
      database = '_07fdbf6ba2f0be13'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x7f96c34f36a0>, <class 'int'>: <function escape_int at 0x7f96c34f3740>, <class 'float'>: <function escape_float at 0x7f96c34f37e0>, <class 'str'>: <function escape_str at 0x7f96c34f3a60>, <class 'bytes'>: <function escape_bytes at 0x7f96c34f39c0>, <class 'tuple'>: <function escape_sequence at 0x7f96c34f3560>, <class 'list'>: <function escape_sequence at 0x7f96c34f3560>, <class 'set'>: <function escape_sequence at 0x7f96c34f3560>, <class 'frozenset'>: <function escape_sequence at 0x7f96c34f3560>, <class 'dict'>: <function escape_dict at 0x7f96c34f34c0>, <class 'NoneType'>: <function escape_None at 0x7f96c34f3b00>, <class 'datetime.date'>: <function escape_date at 0x7f96c34f3d80>, <class 'datetime.datetime'>: <function escape_datetime at 0x7f96c34f3ce0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x7f96c34f3ba0>, <class 'datetime.time'>: <function escape_time at 0x7f96c34f3c40>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x7f96c34e05f0>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x7f96c34e05f0>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_07fdbf6ba2f0be13\x00\x14\xd33\xeb_i_O)U\xaa\xb4/[\xcbx\xff\xd7o\xf7\xb2_07fdbf6ba2f0be13\x00mysql_native_password\x007\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x06940512'
      authresp = b'\xd33\xeb_i_O)U\xaa\xb4/[\xcbx\xff\xd7o\xf7\xb2'
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x06940512'
      k = b'_pid'
      v = b'940512'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x7f96c34e05f0>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x15\x04#28000Access denied for user \'_07fdbf6ba2f0be13\'@\'localhost\' (using password: YES)")
      packet_header = b'U\x00\x00\x02'
      btrl = 85
      btrh = 0
      packet_number = 2
      bytes_to_read = 85
      recv_data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      packet = <pymysql.protocol.MysqlPacket object at 0x7f96c32195d0>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x7f96c32195d0>
      errno = 1045
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errno = 1045
      errval = "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
Backup failed for Site westside.local. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['local.westside', 'westside.local'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7f96c4477420>
      exit_code = 1
      rollback_callback = None
      site = 'westside.local'
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7f96c33c1f90>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7f96c33c1f90>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7f96c34e05f0>
      db_name = '_07fdbf6ba2f0be13'
      user = '_07fdbf6ba2f0be13'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7f96c33c1f90>
      site = 'local.westside'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7f96c34e05f0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 483, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7f96c56823f0>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x7f96c34e04d0>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_07fdbf6ba2f0be13'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x7f96c33eba60>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x7f96c33ebb00>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7f96c56823f0>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_07fdbf6ba2f0be13'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7f96c56823f0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7f96c56823f0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7f96c56823f0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7f96c56823f0>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x7f96c34ad340>
      user = '_07fdbf6ba2f0be13'
      password = ********
      host = '127.0.0.1'
      database = '_07fdbf6ba2f0be13'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x7f96c34f36a0>, <class 'int'>: <function escape_int at 0x7f96c34f3740>, <class 'float'>: <function escape_float at 0x7f96c34f37e0>, <class 'str'>: <function escape_str at 0x7f96c34f3a60>, <class 'bytes'>: <function escape_bytes at 0x7f96c34f39c0>, <class 'tuple'>: <function escape_sequence at 0x7f96c34f3560>, <class 'list'>: <function escape_sequence at 0x7f96c34f3560>, <class 'set'>: <function escape_sequence at 0x7f96c34f3560>, <class 'frozenset'>: <function escape_sequence at 0x7f96c34f3560>, <class 'dict'>: <function escape_dict at 0x7f96c34f34c0>, <class 'NoneType'>: <function escape_None at 0x7f96c34f3b00>, <class 'datetime.date'>: <function escape_date at 0x7f96c34f3d80>, <class 'datetime.datetime'>: <function escape_datetime at 0x7f96c34f3ce0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x7f96c34f3ba0>, <class 'datetime.time'>: <function escape_time at 0x7f96c34f3c40>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x7f96c34ad340>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x7f96c34ad340>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_07fdbf6ba2f0be13\x00\x14ma\xf5\xde~nl-$a\xbf\x81H\x1aE#\xd1\x04!t_07fdbf6ba2f0be13\x00mysql_native_password\x007\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x06940512'
      authresp = b'ma\xf5\xde~nl-$a\xbf\x81H\x1aE#\xd1\x04!t'
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x06940512'
      k = b'_pid'
      v = b'940512'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x7f96c34ad340>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x15\x04#28000Access denied for user \'_07fdbf6ba2f0be13\'@\'localhost\' (using password: YES)")
      packet_header = b'U\x00\x00\x02'
      btrl = 85
      btrh = 0
      packet_number = 2
      bytes_to_read = 85
      recv_data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      packet = <pymysql.protocol.MysqlPacket object at 0x7f96c32194b0>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x7f96c32194b0>
      errno = 1045
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errno = 1045
      errval = "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
Backup failed for Site local.westside. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['local.westside', 'westside.local'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x734bbd26f420>
      exit_code = 0
      rollback_callback = None
      site = 'local.westside'
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x734bbc211390>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x734bbc211390>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x734bbc211430>
      db_name = '_07fdbf6ba2f0be13'
      user = '_07fdbf6ba2f0be13'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x734bbc211390>
      site = 'local.westside'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x734bbc211430>
  File "apps/frappe/frappe/database/mariadb/database.py", line 483, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x734bbc237800>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x734bbc281eb0>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_07fdbf6ba2f0be13'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x734bbc1e7a60>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x734bbc1e7b00>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x734bbc237800>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_07fdbf6ba2f0be13'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x734bbc237800>
  File "apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x734bbc237800>
  File "apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x734bbc237800>
  File "apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x734bbc237800>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x734bbc471d30>
      user = '_07fdbf6ba2f0be13'
      password = ********
      host = '127.0.0.1'
      database = '_07fdbf6ba2f0be13'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x734bbc2ef6a0>, <class 'int'>: <function escape_int at 0x734bbc2ef740>, <class 'float'>: <function escape_float at 0x734bbc2ef7e0>, <class 'str'>: <function escape_str at 0x734bbc2efa60>, <class 'bytes'>: <function escape_bytes at 0x734bbc2ef9c0>, <class 'tuple'>: <function escape_sequence at 0x734bbc2ef560>, <class 'list'>: <function escape_sequence at 0x734bbc2ef560>, <class 'set'>: <function escape_sequence at 0x734bbc2ef560>, <class 'frozenset'>: <function escape_sequence at 0x734bbc2ef560>, <class 'dict'>: <function escape_dict at 0x734bbc2ef4c0>, <class 'NoneType'>: <function escape_None at 0x734bbc2efb00>, <class 'datetime.date'>: <function escape_date at 0x734bbc2efd80>, <class 'datetime.datetime'>: <function escape_datetime at 0x734bbc2efce0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x734bbc2efba0>, <class 'datetime.time'>: <function escape_time at 0x734bbc2efc40>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x734bbc471d30>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x734bbc471d30>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_07fdbf6ba2f0be13\x00\x14\xe3y*\xad\xe9\xb8\xab\x8a\x03\xfe\xbf7U|\xf6\xfd\x15\x14?%_07fdbf6ba2f0be13\x00mysql_native_password\x008\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x071079856'
      authresp = b'\xe3y*\xad\xe9\xb8\xab\x8a\x03\xfe\xbf7U|\xf6\xfd\x15\x14?%'
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x071079856'
      k = b'_pid'
      v = b'1079856'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x734bbc471d30>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x15\x04#28000Access denied for user \'_07fdbf6ba2f0be13\'@\'localhost\' (using password: YES)")
      packet_header = b'U\x00\x00\x02'
      btrl = 85
      btrh = 0
      packet_number = 2
      bytes_to_read = 85
      recv_data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      packet = <pymysql.protocol.MysqlPacket object at 0x734bbc211510>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x734bbc211510>
      errno = 1045
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errno = 1045
      errval = "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
Backup failed for Site westside.local. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['local.westside', 'westside.local'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x734bbd26f420>
      exit_code = 1
      rollback_callback = None
      site = 'westside.local'
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x734bbc2377f0>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x734bbc2377f0>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x734bbc2aa480>
      db_name = '_07fdbf6ba2f0be13'
      user = '_07fdbf6ba2f0be13'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x734bbc2377f0>
      site = 'local.westside'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x734bbc2aa480>
  File "apps/frappe/frappe/database/mariadb/database.py", line 483, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x734bbc281eb0>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x734bbc211490>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_07fdbf6ba2f0be13'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x734bbc1e7a60>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x734bbc1e7b00>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x734bbc281eb0>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_07fdbf6ba2f0be13'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x734bbc281eb0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x734bbc281eb0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x734bbc281eb0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x734bbc281eb0>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x734bbd3cb3b0>
      user = '_07fdbf6ba2f0be13'
      password = ********
      host = '127.0.0.1'
      database = '_07fdbf6ba2f0be13'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x734bbc2ef6a0>, <class 'int'>: <function escape_int at 0x734bbc2ef740>, <class 'float'>: <function escape_float at 0x734bbc2ef7e0>, <class 'str'>: <function escape_str at 0x734bbc2efa60>, <class 'bytes'>: <function escape_bytes at 0x734bbc2ef9c0>, <class 'tuple'>: <function escape_sequence at 0x734bbc2ef560>, <class 'list'>: <function escape_sequence at 0x734bbc2ef560>, <class 'set'>: <function escape_sequence at 0x734bbc2ef560>, <class 'frozenset'>: <function escape_sequence at 0x734bbc2ef560>, <class 'dict'>: <function escape_dict at 0x734bbc2ef4c0>, <class 'NoneType'>: <function escape_None at 0x734bbc2efb00>, <class 'datetime.date'>: <function escape_date at 0x734bbc2efd80>, <class 'datetime.datetime'>: <function escape_datetime at 0x734bbc2efce0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x734bbc2efba0>, <class 'datetime.time'>: <function escape_time at 0x734bbc2efc40>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x734bbd3cb3b0>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x734bbd3cb3b0>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_07fdbf6ba2f0be13\x00\x14c\x8dq/\x0e\xd4\x126\xad\xe4L\x10@\x18,T\x13\xc8l\xea_07fdbf6ba2f0be13\x00mysql_native_password\x008\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x071079856'
      authresp = b'c\x8dq/\x0e\xd4\x126\xad\xe4L\x10@\x18,T\x13\xc8l\xea'
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x071079856'
      k = b'_pid'
      v = b'1079856'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x734bbd3cb3b0>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x15\x04#28000Access denied for user \'_07fdbf6ba2f0be13\'@\'localhost\' (using password: YES)")
      packet_header = b'U\x00\x00\x02'
      btrl = 85
      btrh = 0
      packet_number = 2
      bytes_to_read = 85
      recv_data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      packet = <pymysql.protocol.MysqlPacket object at 0x734bbc03b250>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x734bbc03b250>
      errno = 1045
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errno = 1045
      errval = "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
Backup failed for Site local.westside. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['local.westside', 'westside.local'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x76b5f3b7b420>
      exit_code = 0
      rollback_callback = None
      site = 'local.westside'
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x76b5f291d3c0>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x76b5f291d3c0>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x76b5f2d46ea0>
      db_name = '_07fdbf6ba2f0be13'
      user = '_07fdbf6ba2f0be13'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x76b5f291d3c0>
      site = 'local.westside'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x76b5f2d46ea0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 483, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x76b5f3a6f800>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x76b5f5153320>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_07fdbf6ba2f0be13'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x76b5f2aefa60>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x76b5f2aefb00>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x76b5f3a6f800>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_07fdbf6ba2f0be13'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x76b5f3a6f800>
  File "apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x76b5f3a6f800>
  File "apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x76b5f3a6f800>
  File "apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x76b5f3a6f800>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x76b5f51e3a70>
      user = '_07fdbf6ba2f0be13'
      password = ********
      host = '127.0.0.1'
      database = '_07fdbf6ba2f0be13'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x76b5f2bf76a0>, <class 'int'>: <function escape_int at 0x76b5f2bf7740>, <class 'float'>: <function escape_float at 0x76b5f2bf77e0>, <class 'str'>: <function escape_str at 0x76b5f2bf7a60>, <class 'bytes'>: <function escape_bytes at 0x76b5f2bf79c0>, <class 'tuple'>: <function escape_sequence at 0x76b5f2bf7560>, <class 'list'>: <function escape_sequence at 0x76b5f2bf7560>, <class 'set'>: <function escape_sequence at 0x76b5f2bf7560>, <class 'frozenset'>: <function escape_sequence at 0x76b5f2bf7560>, <class 'dict'>: <function escape_dict at 0x76b5f2bf74c0>, <class 'NoneType'>: <function escape_None at 0x76b5f2bf7b00>, <class 'datetime.date'>: <function escape_date at 0x76b5f2bf7d80>, <class 'datetime.datetime'>: <function escape_datetime at 0x76b5f2bf7ce0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x76b5f2bf7ba0>, <class 'datetime.time'>: <function escape_time at 0x76b5f2bf7c40>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x76b5f51e3a70>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x76b5f51e3a70>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_07fdbf6ba2f0be13\x00\x14]\x88r\xe3F%\xb4\xf6\xe5N\x0c\x02\xd1$\xbfM_m\x98\x9f_07fdbf6ba2f0be13\x00mysql_native_password\x008\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x071331486'
      authresp = b']\x88r\xe3F%\xb4\xf6\xe5N\x0c\x02\xd1$\xbfM_m\x98\x9f'
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x071331486'
      k = b'_pid'
      v = b'1331486'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x76b5f51e3a70>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x15\x04#28000Access denied for user \'_07fdbf6ba2f0be13\'@\'localhost\' (using password: YES)")
      packet_header = b'U\x00\x00\x02'
      btrl = 85
      btrh = 0
      packet_number = 2
      bytes_to_read = 85
      recv_data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      packet = <pymysql.protocol.MysqlPacket object at 0x76b5f291d540>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x76b5f291d540>
      errno = 1045
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errno = 1045
      errval = "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
Backup failed for Site westside.local. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['local.westside', 'westside.local'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x76b5f3b7b420>
      exit_code = 1
      rollback_callback = None
      site = 'westside.local'
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x76b5f3a6f7f0>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x76b5f3a6f7f0>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x76b5f2aeb290>
      db_name = '_07fdbf6ba2f0be13'
      user = '_07fdbf6ba2f0be13'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x76b5f3a6f7f0>
      site = 'local.westside'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x76b5f2aeb290>
  File "apps/frappe/frappe/database/mariadb/database.py", line 483, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x76b5f5153320>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x76b5f291d3d0>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_07fdbf6ba2f0be13'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x76b5f2aefa60>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x76b5f2aefb00>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x76b5f5153320>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_07fdbf6ba2f0be13'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x76b5f5153320>
  File "apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x76b5f5153320>
  File "apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x76b5f5153320>
  File "apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x76b5f5153320>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x76b5f291d4f0>
      user = '_07fdbf6ba2f0be13'
      password = ********
      host = '127.0.0.1'
      database = '_07fdbf6ba2f0be13'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x76b5f2bf76a0>, <class 'int'>: <function escape_int at 0x76b5f2bf7740>, <class 'float'>: <function escape_float at 0x76b5f2bf77e0>, <class 'str'>: <function escape_str at 0x76b5f2bf7a60>, <class 'bytes'>: <function escape_bytes at 0x76b5f2bf79c0>, <class 'tuple'>: <function escape_sequence at 0x76b5f2bf7560>, <class 'list'>: <function escape_sequence at 0x76b5f2bf7560>, <class 'set'>: <function escape_sequence at 0x76b5f2bf7560>, <class 'frozenset'>: <function escape_sequence at 0x76b5f2bf7560>, <class 'dict'>: <function escape_dict at 0x76b5f2bf74c0>, <class 'NoneType'>: <function escape_None at 0x76b5f2bf7b00>, <class 'datetime.date'>: <function escape_date at 0x76b5f2bf7d80>, <class 'datetime.datetime'>: <function escape_datetime at 0x76b5f2bf7ce0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x76b5f2bf7ba0>, <class 'datetime.time'>: <function escape_time at 0x76b5f2bf7c40>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x76b5f291d4f0>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x76b5f291d4f0>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_07fdbf6ba2f0be13\x00\x145\x85y@\xf9e\xe4SV \xbc]\xc5\xec\x0bI\xffy\xc1\xdd_07fdbf6ba2f0be13\x00mysql_native_password\x008\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x071331486'
      authresp = b'5\x85y@\xf9e\xe4SV \xbc]\xc5\xec\x0bI\xffy\xc1\xdd'
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x071331486'
      k = b'_pid'
      v = b'1331486'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x76b5f291d4f0>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x15\x04#28000Access denied for user \'_07fdbf6ba2f0be13\'@\'localhost\' (using password: YES)")
      packet_header = b'U\x00\x00\x02'
      btrl = 85
      btrh = 0
      packet_number = 2
      bytes_to_read = 85
      recv_data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      packet = <pymysql.protocol.MysqlPacket object at 0x76b5f2d44c40>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x76b5f2d44c40>
      errno = 1045
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errno = 1045
      errval = "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
Backup failed for Site local.westside. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['local.westside', 'westside.local'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7fd2a9f63420>
      exit_code = 0
      rollback_callback = None
      site = 'local.westside'
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7fd2a8f05330>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7fd2a8f05330>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7fd2aa5beea0>
      db_name = '_07fdbf6ba2f0be13'
      user = '_07fdbf6ba2f0be13'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7fd2a8f05330>
      site = 'local.westside'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7fd2aa5beea0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 483, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7fd2a8ed5a90>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x7fd2ab553320>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_07fdbf6ba2f0be13'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x7fd2a8edba60>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x7fd2a8edbb00>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7fd2a8ed5a90>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_07fdbf6ba2f0be13'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7fd2a8ed5a90>
  File "apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7fd2a8ed5a90>
  File "apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7fd2a8ed5a90>
  File "apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7fd2a8ed5a90>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x7fd2a8f79d30>
      user = '_07fdbf6ba2f0be13'
      password = ********
      host = '127.0.0.1'
      database = '_07fdbf6ba2f0be13'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x7fd2a8fe36a0>, <class 'int'>: <function escape_int at 0x7fd2a8fe3740>, <class 'float'>: <function escape_float at 0x7fd2a8fe37e0>, <class 'str'>: <function escape_str at 0x7fd2a8fe3a60>, <class 'bytes'>: <function escape_bytes at 0x7fd2a8fe39c0>, <class 'tuple'>: <function escape_sequence at 0x7fd2a8fe3560>, <class 'list'>: <function escape_sequence at 0x7fd2a8fe3560>, <class 'set'>: <function escape_sequence at 0x7fd2a8fe3560>, <class 'frozenset'>: <function escape_sequence at 0x7fd2a8fe3560>, <class 'dict'>: <function escape_dict at 0x7fd2a8fe34c0>, <class 'NoneType'>: <function escape_None at 0x7fd2a8fe3b00>, <class 'datetime.date'>: <function escape_date at 0x7fd2a8fe3d80>, <class 'datetime.datetime'>: <function escape_datetime at 0x7fd2a8fe3ce0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x7fd2a8fe3ba0>, <class 'datetime.time'>: <function escape_time at 0x7fd2a8fe3c40>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x7fd2a8f79d30>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x7fd2a8f79d30>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_07fdbf6ba2f0be13\x00\x14\xf0h\x0f\x0b\xf9\xeb\xf2\x91^\xfd\xe4\xb8\x9d\x97\xabu\xa7\x97_\xec_07fdbf6ba2f0be13\x00mysql_native_password\x008\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x071509517'
      authresp = b'\xf0h\x0f\x0b\xf9\xeb\xf2\x91^\xfd\xe4\xb8\x9d\x97\xabu\xa7\x97_\xec'
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x071509517'
      k = b'_pid'
      v = b'1509517'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x7fd2a8f79d30>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x15\x04#28000Access denied for user \'_07fdbf6ba2f0be13\'@\'localhost\' (using password: YES)")
      packet_header = b'U\x00\x00\x02'
      btrl = 85
      btrh = 0
      packet_number = 2
      bytes_to_read = 85
      recv_data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      packet = <pymysql.protocol.MysqlPacket object at 0x7fd2a8f05420>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x7fd2a8f05420>
      errno = 1045
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errno = 1045
      errval = "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
Backup failed for Site westside.local. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['local.westside', 'westside.local'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7fd2a9f63420>
      exit_code = 1
      rollback_callback = None
      site = 'westside.local'
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7fd2a8ed5a80>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7fd2a8ed5a80>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7fd2a8f79d30>
      db_name = '_07fdbf6ba2f0be13'
      user = '_07fdbf6ba2f0be13'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7fd2a8ed5a80>
      site = 'local.westside'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7fd2a8f79d30>
  File "apps/frappe/frappe/database/mariadb/database.py", line 483, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7fd2aa5beea0>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x7fd2a9164740>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_07fdbf6ba2f0be13'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x7fd2a8edba60>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x7fd2a8edbb00>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7fd2aa5beea0>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_07fdbf6ba2f0be13'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7fd2aa5beea0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7fd2aa5beea0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7fd2aa5beea0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7fd2aa5beea0>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x7fd2a8f79d90>
      user = '_07fdbf6ba2f0be13'
      password = ********
      host = '127.0.0.1'
      database = '_07fdbf6ba2f0be13'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x7fd2a8fe36a0>, <class 'int'>: <function escape_int at 0x7fd2a8fe3740>, <class 'float'>: <function escape_float at 0x7fd2a8fe37e0>, <class 'str'>: <function escape_str at 0x7fd2a8fe3a60>, <class 'bytes'>: <function escape_bytes at 0x7fd2a8fe39c0>, <class 'tuple'>: <function escape_sequence at 0x7fd2a8fe3560>, <class 'list'>: <function escape_sequence at 0x7fd2a8fe3560>, <class 'set'>: <function escape_sequence at 0x7fd2a8fe3560>, <class 'frozenset'>: <function escape_sequence at 0x7fd2a8fe3560>, <class 'dict'>: <function escape_dict at 0x7fd2a8fe34c0>, <class 'NoneType'>: <function escape_None at 0x7fd2a8fe3b00>, <class 'datetime.date'>: <function escape_date at 0x7fd2a8fe3d80>, <class 'datetime.datetime'>: <function escape_datetime at 0x7fd2a8fe3ce0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x7fd2a8fe3ba0>, <class 'datetime.time'>: <function escape_time at 0x7fd2a8fe3c40>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x7fd2a8f79d90>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x7fd2a8f79d90>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_07fdbf6ba2f0be13\x00\x14Q\xc69&\x1a\xd1\xb1\x98Q\\\rp;e(\xaa\x9f&*`_07fdbf6ba2f0be13\x00mysql_native_password\x008\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x071509517'
      authresp = b'Q\xc69&\x1a\xd1\xb1\x98Q\\\rp;e(\xaa\x9f&*`'
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x071509517'
      k = b'_pid'
      v = b'1509517'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x7fd2a8f79d90>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x15\x04#28000Access denied for user \'_07fdbf6ba2f0be13\'@\'localhost\' (using password: YES)")
      packet_header = b'U\x00\x00\x02'
      btrl = 85
      btrh = 0
      packet_number = 2
      bytes_to_read = 85
      recv_data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      packet = <pymysql.protocol.MysqlPacket object at 0x7fd2a8fe85b0>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x7fd2a8fe85b0>
      errno = 1045
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errno = 1045
      errval = "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
Backup failed for Site local.westside. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['local.westside', 'westside.local'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7dae16d73420>
      exit_code = 0
      rollback_callback = None
      site = 'local.westside'
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7dae15c914e0>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7dae15c914e0>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7dae15c91580>
      db_name = '_07fdbf6ba2f0be13'
      user = '_07fdbf6ba2f0be13'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7dae15c914e0>
      site = 'local.westside'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7dae15c91580>
  File "apps/frappe/frappe/database/mariadb/database.py", line 483, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7dae17407800>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x7dae18353320>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_07fdbf6ba2f0be13'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x7dae15c67a60>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x7dae15c67b00>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7dae17407800>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_07fdbf6ba2f0be13'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7dae17407800>
  File "apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7dae17407800>
  File "apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7dae17407800>
  File "apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7dae17407800>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x7dae1736f260>
      user = '_07fdbf6ba2f0be13'
      password = ********
      host = '127.0.0.1'
      database = '_07fdbf6ba2f0be13'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x7dae15def6a0>, <class 'int'>: <function escape_int at 0x7dae15def740>, <class 'float'>: <function escape_float at 0x7dae15def7e0>, <class 'str'>: <function escape_str at 0x7dae15defa60>, <class 'bytes'>: <function escape_bytes at 0x7dae15def9c0>, <class 'tuple'>: <function escape_sequence at 0x7dae15def560>, <class 'list'>: <function escape_sequence at 0x7dae15def560>, <class 'set'>: <function escape_sequence at 0x7dae15def560>, <class 'frozenset'>: <function escape_sequence at 0x7dae15def560>, <class 'dict'>: <function escape_dict at 0x7dae15def4c0>, <class 'NoneType'>: <function escape_None at 0x7dae15defb00>, <class 'datetime.date'>: <function escape_date at 0x7dae15defd80>, <class 'datetime.datetime'>: <function escape_datetime at 0x7dae15defce0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x7dae15defba0>, <class 'datetime.time'>: <function escape_time at 0x7dae15defc40>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x7dae1736f260>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x7dae1736f260>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_07fdbf6ba2f0be13\x00\x145|L\x04\xd6\x9f\xa6I#\xe42%\x9d\xd17\xad\xcb\xf8\xdaW_07fdbf6ba2f0be13\x00mysql_native_password\x008\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x071739645'
      authresp = b'5|L\x04\xd6\x9f\xa6I#\xe42%\x9d\xd17\xad\xcb\xf8\xdaW'
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x071739645'
      k = b'_pid'
      v = b'1739645'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x7dae1736f260>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x15\x04#28000Access denied for user \'_07fdbf6ba2f0be13\'@\'localhost\' (using password: YES)")
      packet_header = b'U\x00\x00\x02'
      btrl = 85
      btrh = 0
      packet_number = 2
      bytes_to_read = 85
      recv_data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      packet = <pymysql.protocol.MysqlPacket object at 0x7dae15c91690>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x7dae15c91690>
      errno = 1045
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errno = 1045
      errval = "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
Backup failed for Site westside.local. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['local.westside', 'westside.local'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7dae16d73420>
      exit_code = 1
      rollback_callback = None
      site = 'westside.local'
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7dae174077f0>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7dae174077f0>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7dae15b99c40>
      db_name = '_07fdbf6ba2f0be13'
      user = '_07fdbf6ba2f0be13'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7dae174077f0>
      site = 'local.westside'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7dae15b99c40>
  File "apps/frappe/frappe/database/mariadb/database.py", line 483, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7dae18353320>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x7dae15df4560>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_07fdbf6ba2f0be13'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x7dae15c67a60>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x7dae15c67b00>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7dae18353320>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_07fdbf6ba2f0be13'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7dae18353320>
  File "apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7dae18353320>
  File "apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7dae18353320>
  File "apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7dae18353320>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x7dae15c91550>
      user = '_07fdbf6ba2f0be13'
      password = ********
      host = '127.0.0.1'
      database = '_07fdbf6ba2f0be13'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x7dae15def6a0>, <class 'int'>: <function escape_int at 0x7dae15def740>, <class 'float'>: <function escape_float at 0x7dae15def7e0>, <class 'str'>: <function escape_str at 0x7dae15defa60>, <class 'bytes'>: <function escape_bytes at 0x7dae15def9c0>, <class 'tuple'>: <function escape_sequence at 0x7dae15def560>, <class 'list'>: <function escape_sequence at 0x7dae15def560>, <class 'set'>: <function escape_sequence at 0x7dae15def560>, <class 'frozenset'>: <function escape_sequence at 0x7dae15def560>, <class 'dict'>: <function escape_dict at 0x7dae15def4c0>, <class 'NoneType'>: <function escape_None at 0x7dae15defb00>, <class 'datetime.date'>: <function escape_date at 0x7dae15defd80>, <class 'datetime.datetime'>: <function escape_datetime at 0x7dae15defce0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x7dae15defba0>, <class 'datetime.time'>: <function escape_time at 0x7dae15defc40>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x7dae15c91550>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x7dae15c91550>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_07fdbf6ba2f0be13\x00\x14:\x89S\x9c\xe2\x01I\x9b]Q\n{\x8b\xceY\xe8\x89p4|_07fdbf6ba2f0be13\x00mysql_native_password\x008\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x071739645'
      authresp = b':\x89S\x9c\xe2\x01I\x9b]Q\n{\x8b\xceY\xe8\x89p4|'
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x071739645'
      k = b'_pid'
      v = b'1739645'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x7dae15c91550>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x15\x04#28000Access denied for user \'_07fdbf6ba2f0be13\'@\'localhost\' (using password: YES)")
      packet_header = b'U\x00\x00\x02'
      btrl = 85
      btrh = 0
      packet_number = 2
      bytes_to_read = 85
      recv_data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      packet = <pymysql.protocol.MysqlPacket object at 0x7dae15abb2e0>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x7dae15abb2e0>
      errno = 1045
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errno = 1045
      errval = "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
Backup failed for Site local.westside. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['local.westside', 'westside.local'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7b85c6e77420>
      exit_code = 0
      rollback_callback = None
      site = 'local.westside'
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7b85c5c193c0>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7b85c5c193c0>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7b85c5c19460>
      db_name = '_07fdbf6ba2f0be13'
      user = '_07fdbf6ba2f0be13'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7b85c5c193c0>
      site = 'local.westside'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7b85c5c19460>
  File "apps/frappe/frappe/database/mariadb/database.py", line 483, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7b85c5dc2d50>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x7b85c7791820>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_07fdbf6ba2f0be13'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x7b85c5deba60>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x7b85c5debb00>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7b85c5dc2d50>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_07fdbf6ba2f0be13'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7b85c5dc2d50>
  File "apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7b85c5dc2d50>
  File "apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7b85c5dc2d50>
  File "apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7b85c5dc2d50>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x7b85c7809d30>
      user = '_07fdbf6ba2f0be13'
      password = ********
      host = '127.0.0.1'
      database = '_07fdbf6ba2f0be13'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x7b85c5ef36a0>, <class 'int'>: <function escape_int at 0x7b85c5ef3740>, <class 'float'>: <function escape_float at 0x7b85c5ef37e0>, <class 'str'>: <function escape_str at 0x7b85c5ef3a60>, <class 'bytes'>: <function escape_bytes at 0x7b85c5ef39c0>, <class 'tuple'>: <function escape_sequence at 0x7b85c5ef3560>, <class 'list'>: <function escape_sequence at 0x7b85c5ef3560>, <class 'set'>: <function escape_sequence at 0x7b85c5ef3560>, <class 'frozenset'>: <function escape_sequence at 0x7b85c5ef3560>, <class 'dict'>: <function escape_dict at 0x7b85c5ef34c0>, <class 'NoneType'>: <function escape_None at 0x7b85c5ef3b00>, <class 'datetime.date'>: <function escape_date at 0x7b85c5ef3d80>, <class 'datetime.datetime'>: <function escape_datetime at 0x7b85c5ef3ce0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x7b85c5ef3ba0>, <class 'datetime.time'>: <function escape_time at 0x7b85c5ef3c40>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x7b85c7809d30>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x7b85c7809d30>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b"\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_07fdbf6ba2f0be13\x00\x146\x005\x19\x96\x03\xcc1\xb7\xb0\x01-\x1ai'\x99\x02\x8eu;_07fdbf6ba2f0be13\x00mysql_native_password\x008\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x072025369"
      authresp = b"6\x005\x19\x96\x03\xcc1\xb7\xb0\x01-\x1ai'\x99\x02\x8eu;"
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x072025369'
      k = b'_pid'
      v = b'2025369'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x7b85c7809d30>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x15\x04#28000Access denied for user \'_07fdbf6ba2f0be13\'@\'localhost\' (using password: YES)")
      packet_header = b'U\x00\x00\x02'
      btrl = 85
      btrh = 0
      packet_number = 2
      bytes_to_read = 85
      recv_data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      packet = <pymysql.protocol.MysqlPacket object at 0x7b85c5c195d0>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x7b85c5c195d0>
      errno = 1045
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errno = 1045
      errval = "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
Backup failed for Site westside.local. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['local.westside', 'westside.local'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7b85c6e77420>
      exit_code = 1
      rollback_callback = None
      site = 'westside.local'
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7b85c5dc2d40>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7b85c5dc2d40>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7b85c5de70e0>
      db_name = '_07fdbf6ba2f0be13'
      user = '_07fdbf6ba2f0be13'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7b85c5dc2d40>
      site = 'local.westside'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7b85c5de70e0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 483, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7b85c80d7fb0>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x7b85c5ead8b0>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_07fdbf6ba2f0be13'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x7b85c5deba60>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x7b85c5debb00>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7b85c80d7fb0>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_07fdbf6ba2f0be13'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7b85c80d7fb0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7b85c80d7fb0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7b85c80d7fb0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7b85c80d7fb0>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x7b85c5c195e0>
      user = '_07fdbf6ba2f0be13'
      password = ********
      host = '127.0.0.1'
      database = '_07fdbf6ba2f0be13'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x7b85c5ef36a0>, <class 'int'>: <function escape_int at 0x7b85c5ef3740>, <class 'float'>: <function escape_float at 0x7b85c5ef37e0>, <class 'str'>: <function escape_str at 0x7b85c5ef3a60>, <class 'bytes'>: <function escape_bytes at 0x7b85c5ef39c0>, <class 'tuple'>: <function escape_sequence at 0x7b85c5ef3560>, <class 'list'>: <function escape_sequence at 0x7b85c5ef3560>, <class 'set'>: <function escape_sequence at 0x7b85c5ef3560>, <class 'frozenset'>: <function escape_sequence at 0x7b85c5ef3560>, <class 'dict'>: <function escape_dict at 0x7b85c5ef34c0>, <class 'NoneType'>: <function escape_None at 0x7b85c5ef3b00>, <class 'datetime.date'>: <function escape_date at 0x7b85c5ef3d80>, <class 'datetime.datetime'>: <function escape_datetime at 0x7b85c5ef3ce0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x7b85c5ef3ba0>, <class 'datetime.time'>: <function escape_time at 0x7b85c5ef3c40>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x7b85c5c195e0>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x7b85c5c195e0>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_07fdbf6ba2f0be13\x00\x14\x96\xb0\xbcGO[\x7f\xb3\x83\xd6\xac\xc4}r\x95H\x12\xbf\xd6x_07fdbf6ba2f0be13\x00mysql_native_password\x008\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x072025369'
      authresp = b'\x96\xb0\xbcGO[\x7f\xb3\x83\xd6\xac\xc4}r\x95H\x12\xbf\xd6x'
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x072025369'
      k = b'_pid'
      v = b'2025369'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x7b85c5c195e0>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x15\x04#28000Access denied for user \'_07fdbf6ba2f0be13\'@\'localhost\' (using password: YES)")
      packet_header = b'U\x00\x00\x02'
      btrl = 85
      btrh = 0
      packet_number = 2
      bytes_to_read = 85
      recv_data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      packet = <pymysql.protocol.MysqlPacket object at 0x7b85c5c193f0>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x7b85c5c193f0>
      errno = 1045
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errno = 1045
      errval = "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
Backup failed for Site local.westside. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['local.westside', 'westside.local'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7e61ae46f420>
      exit_code = 0
      rollback_callback = None
      site = 'local.westside'
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7e61ad411210>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7e61ad411210>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7e61ad436ea0>
      db_name = '_07fdbf6ba2f0be13'
      user = '_07fdbf6ba2f0be13'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7e61ad411210>
      site = 'local.westside'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7e61ad436ea0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 483, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7e61ad3e1a30>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x7e61ad411310>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_07fdbf6ba2f0be13'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x7e61ad3e7a60>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x7e61ad3e7b00>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7e61ad3e1a30>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_07fdbf6ba2f0be13'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7e61ad3e1a30>
  File "apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7e61ad3e1a30>
  File "apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7e61ad3e1a30>
  File "apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7e61ad3e1a30>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x7e61ad485dc0>
      user = '_07fdbf6ba2f0be13'
      password = ********
      host = '127.0.0.1'
      database = '_07fdbf6ba2f0be13'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x7e61ad4ef6a0>, <class 'int'>: <function escape_int at 0x7e61ad4ef740>, <class 'float'>: <function escape_float at 0x7e61ad4ef7e0>, <class 'str'>: <function escape_str at 0x7e61ad4efa60>, <class 'bytes'>: <function escape_bytes at 0x7e61ad4ef9c0>, <class 'tuple'>: <function escape_sequence at 0x7e61ad4ef560>, <class 'list'>: <function escape_sequence at 0x7e61ad4ef560>, <class 'set'>: <function escape_sequence at 0x7e61ad4ef560>, <class 'frozenset'>: <function escape_sequence at 0x7e61ad4ef560>, <class 'dict'>: <function escape_dict at 0x7e61ad4ef4c0>, <class 'NoneType'>: <function escape_None at 0x7e61ad4efb00>, <class 'datetime.date'>: <function escape_date at 0x7e61ad4efd80>, <class 'datetime.datetime'>: <function escape_datetime at 0x7e61ad4efce0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x7e61ad4efba0>, <class 'datetime.time'>: <function escape_time at 0x7e61ad4efc40>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x7e61ad485dc0>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x7e61ad485dc0>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_07fdbf6ba2f0be13\x00\x14\xeb\x7f\xb8\xc4\xd9\xa2\xab\xa3\x8cP\xf0vo\xbb \xe8\x94\x02\x0fj_07fdbf6ba2f0be13\x00mysql_native_password\x007\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x06116774'
      authresp = b'\xeb\x7f\xb8\xc4\xd9\xa2\xab\xa3\x8cP\xf0vo\xbb \xe8\x94\x02\x0fj'
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x06116774'
      k = b'_pid'
      v = b'116774'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x7e61ad485dc0>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x15\x04#28000Access denied for user \'_07fdbf6ba2f0be13\'@\'localhost\' (using password: YES)")
      packet_header = b'U\x00\x00\x02'
      btrl = 85
      btrh = 0
      packet_number = 2
      bytes_to_read = 85
      recv_data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      packet = <pymysql.protocol.MysqlPacket object at 0x7e61ad411390>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x7e61ad411390>
      errno = 1045
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errno = 1045
      errval = "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
Backup failed for Site westside.local. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['local.westside', 'westside.local'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7e61ae46f420>
      exit_code = 1
      rollback_callback = None
      site = 'westside.local'
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7e61aec3c370>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7e61aec3c370>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7e61ad485dc0>
      db_name = '_07fdbf6ba2f0be13'
      user = '_07fdbf6ba2f0be13'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7e61aec3c370>
      site = 'local.westside'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7e61ad485dc0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 483, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7e61ad436ea0>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x7e61ad23f080>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_07fdbf6ba2f0be13'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x7e61ad3e7a60>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x7e61ad3e7b00>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7e61ad436ea0>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_07fdbf6ba2f0be13'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7e61ad436ea0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7e61ad436ea0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7e61ad436ea0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7e61ad436ea0>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x7e61ad899730>
      user = '_07fdbf6ba2f0be13'
      password = ********
      host = '127.0.0.1'
      database = '_07fdbf6ba2f0be13'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x7e61ad4ef6a0>, <class 'int'>: <function escape_int at 0x7e61ad4ef740>, <class 'float'>: <function escape_float at 0x7e61ad4ef7e0>, <class 'str'>: <function escape_str at 0x7e61ad4efa60>, <class 'bytes'>: <function escape_bytes at 0x7e61ad4ef9c0>, <class 'tuple'>: <function escape_sequence at 0x7e61ad4ef560>, <class 'list'>: <function escape_sequence at 0x7e61ad4ef560>, <class 'set'>: <function escape_sequence at 0x7e61ad4ef560>, <class 'frozenset'>: <function escape_sequence at 0x7e61ad4ef560>, <class 'dict'>: <function escape_dict at 0x7e61ad4ef4c0>, <class 'NoneType'>: <function escape_None at 0x7e61ad4efb00>, <class 'datetime.date'>: <function escape_date at 0x7e61ad4efd80>, <class 'datetime.datetime'>: <function escape_datetime at 0x7e61ad4efce0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x7e61ad4efba0>, <class 'datetime.time'>: <function escape_time at 0x7e61ad4efc40>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x7e61ad899730>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x7e61ad899730>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_07fdbf6ba2f0be13\x00\x14\xcef\x04\x1b\xc8\xef\r\x90wM^\x85h\xb8\x17\x91\x8bg\r\xec_07fdbf6ba2f0be13\x00mysql_native_password\x007\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x06116774'
      authresp = b'\xcef\x04\x1b\xc8\xef\r\x90wM^\x85h\xb8\x17\x91\x8bg\r\xec'
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x06116774'
      k = b'_pid'
      v = b'116774'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x7e61ad899730>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x15\x04#28000Access denied for user \'_07fdbf6ba2f0be13\'@\'localhost\' (using password: YES)")
      packet_header = b'U\x00\x00\x02'
      btrl = 85
      btrh = 0
      packet_number = 2
      bytes_to_read = 85
      recv_data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      packet = <pymysql.protocol.MysqlPacket object at 0x7e61ad411450>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x7e61ad411450>
      errno = 1045
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errno = 1045
      errval = "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
Backup failed for Site local.westside. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['local.westside', 'westside.local'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x719e07e4f420>
      exit_code = 0
      rollback_callback = None
      site = 'local.westside'
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x719e06ba1360>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x719e06ba1360>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x719e06ba1400>
      db_name = '_07fdbf6ba2f0be13'
      user = '_07fdbf6ba2f0be13'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x719e06ba1360>
      site = 'local.westside'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x719e06ba1400>
  File "apps/frappe/frappe/database/mariadb/database.py", line 483, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x719e07e911f0>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x719e07301a60>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_07fdbf6ba2f0be13'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x719e06d73a60>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x719e06d73b00>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x719e07e911f0>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_07fdbf6ba2f0be13'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x719e07e911f0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x719e07e911f0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x719e07e911f0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x719e07e911f0>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x719e06cc9d30>
      user = '_07fdbf6ba2f0be13'
      password = ********
      host = '127.0.0.1'
      database = '_07fdbf6ba2f0be13'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x719e06efb6a0>, <class 'int'>: <function escape_int at 0x719e06efb740>, <class 'float'>: <function escape_float at 0x719e06efb7e0>, <class 'str'>: <function escape_str at 0x719e06efba60>, <class 'bytes'>: <function escape_bytes at 0x719e06efb9c0>, <class 'tuple'>: <function escape_sequence at 0x719e06efb560>, <class 'list'>: <function escape_sequence at 0x719e06efb560>, <class 'set'>: <function escape_sequence at 0x719e06efb560>, <class 'frozenset'>: <function escape_sequence at 0x719e06efb560>, <class 'dict'>: <function escape_dict at 0x719e06efb4c0>, <class 'NoneType'>: <function escape_None at 0x719e06efbb00>, <class 'datetime.date'>: <function escape_date at 0x719e06efbd80>, <class 'datetime.datetime'>: <function escape_datetime at 0x719e06efbce0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x719e06efbba0>, <class 'datetime.time'>: <function escape_time at 0x719e06efbc40>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x719e06cc9d30>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x719e06cc9d30>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_07fdbf6ba2f0be13\x00\x14\x11\xa8\xa9\xbb\xe9b\xde\x1f\xc5&F/\xa8,\t\xf9\xcf|\xed(_07fdbf6ba2f0be13\x00mysql_native_password\x007\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x06135616'
      authresp = b'\x11\xa8\xa9\xbb\xe9b\xde\x1f\xc5&F/\xa8,\t\xf9\xcf|\xed('
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x06135616'
      k = b'_pid'
      v = b'135616'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x719e06cc9d30>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x15\x04#28000Access denied for user \'_07fdbf6ba2f0be13\'@\'localhost\' (using password: YES)")
      packet_header = b'U\x00\x00\x02'
      btrl = 85
      btrh = 0
      packet_number = 2
      bytes_to_read = 85
      recv_data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      packet = <pymysql.protocol.MysqlPacket object at 0x719e06ba1480>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x719e06ba1480>
      errno = 1045
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errno = 1045
      errval = "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
Backup failed for Site westside.local. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['local.westside', 'westside.local'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x719e07e4f420>
      exit_code = 1
      rollback_callback = None
      site = 'westside.local'
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x719e07e911e0>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x719e07e911e0>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x719e07fa23c0>
      db_name = '_07fdbf6ba2f0be13'
      user = '_07fdbf6ba2f0be13'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x719e07e911e0>
      site = 'local.westside'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x719e07fa23c0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 483, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x719e06ee84a0>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x719e070a9c40>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_07fdbf6ba2f0be13'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x719e06d73a60>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x719e06d73b00>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x719e06ee84a0>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_07fdbf6ba2f0be13'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x719e06ee84a0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x719e06ee84a0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x719e06ee84a0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x719e06ee84a0>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x719e06eb7770>
      user = '_07fdbf6ba2f0be13'
      password = ********
      host = '127.0.0.1'
      database = '_07fdbf6ba2f0be13'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x719e06efb6a0>, <class 'int'>: <function escape_int at 0x719e06efb740>, <class 'float'>: <function escape_float at 0x719e06efb7e0>, <class 'str'>: <function escape_str at 0x719e06efba60>, <class 'bytes'>: <function escape_bytes at 0x719e06efb9c0>, <class 'tuple'>: <function escape_sequence at 0x719e06efb560>, <class 'list'>: <function escape_sequence at 0x719e06efb560>, <class 'set'>: <function escape_sequence at 0x719e06efb560>, <class 'frozenset'>: <function escape_sequence at 0x719e06efb560>, <class 'dict'>: <function escape_dict at 0x719e06efb4c0>, <class 'NoneType'>: <function escape_None at 0x719e06efbb00>, <class 'datetime.date'>: <function escape_date at 0x719e06efbd80>, <class 'datetime.datetime'>: <function escape_datetime at 0x719e06efbce0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x719e06efbba0>, <class 'datetime.time'>: <function escape_time at 0x719e06efbc40>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x719e06eb7770>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x719e06eb7770>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_07fdbf6ba2f0be13\x00\x14|\x89Id\x04\x04\x1f|\xbai5\x16\x7f\x9f\xd8\x8e\x8bK\xd8\x8d_07fdbf6ba2f0be13\x00mysql_native_password\x007\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x06135616'
      authresp = b'|\x89Id\x04\x04\x1f|\xbai5\x16\x7f\x9f\xd8\x8e\x8bK\xd8\x8d'
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x06135616'
      k = b'_pid'
      v = b'135616'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x719e06eb7770>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x15\x04#28000Access denied for user \'_07fdbf6ba2f0be13\'@\'localhost\' (using password: YES)")
      packet_header = b'U\x00\x00\x02'
      btrl = 85
      btrh = 0
      packet_number = 2
      bytes_to_read = 85
      recv_data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      packet = <pymysql.protocol.MysqlPacket object at 0x719e0711aa70>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x719e0711aa70>
      errno = 1045
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errno = 1045
      errval = "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
Backup failed for Site local.westside. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['local.westside', 'westside.local'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x70aa55473420>
      exit_code = 0
      rollback_callback = None
      site = 'local.westside'
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x70aa54219390>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x70aa54219390>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x70aa543e6ea0>
      db_name = '_07fdbf6ba2f0be13'
      user = '_07fdbf6ba2f0be13'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x70aa54219390>
      site = 'local.westside'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x70aa543e6ea0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 483, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x70aa543e5b20>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x70aa542194c0>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_07fdbf6ba2f0be13'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x70aa543eba60>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x70aa543ebb00>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x70aa543e5b20>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_07fdbf6ba2f0be13'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x70aa543e5b20>
  File "apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x70aa543e5b20>
  File "apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x70aa543e5b20>
  File "apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x70aa543e5b20>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x70aa547165a0>
      user = '_07fdbf6ba2f0be13'
      password = ********
      host = '127.0.0.1'
      database = '_07fdbf6ba2f0be13'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x70aa544f36a0>, <class 'int'>: <function escape_int at 0x70aa544f3740>, <class 'float'>: <function escape_float at 0x70aa544f37e0>, <class 'str'>: <function escape_str at 0x70aa544f3a60>, <class 'bytes'>: <function escape_bytes at 0x70aa544f39c0>, <class 'tuple'>: <function escape_sequence at 0x70aa544f3560>, <class 'list'>: <function escape_sequence at 0x70aa544f3560>, <class 'set'>: <function escape_sequence at 0x70aa544f3560>, <class 'frozenset'>: <function escape_sequence at 0x70aa544f3560>, <class 'dict'>: <function escape_dict at 0x70aa544f34c0>, <class 'NoneType'>: <function escape_None at 0x70aa544f3b00>, <class 'datetime.date'>: <function escape_date at 0x70aa544f3d80>, <class 'datetime.datetime'>: <function escape_datetime at 0x70aa544f3ce0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x70aa544f3ba0>, <class 'datetime.time'>: <function escape_time at 0x70aa544f3c40>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x70aa547165a0>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x70aa547165a0>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_07fdbf6ba2f0be13\x00\x14\n\xba*\x1btH@\xdf\xa1O\xd3\x142m\xeb\xe4\x02i\x07\x17_07fdbf6ba2f0be13\x00mysql_native_password\x007\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x06382408'
      authresp = b'\n\xba*\x1btH@\xdf\xa1O\xd3\x142m\xeb\xe4\x02i\x07\x17'
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x06382408'
      k = b'_pid'
      v = b'382408'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x70aa547165a0>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x15\x04#28000Access denied for user \'_07fdbf6ba2f0be13\'@\'localhost\' (using password: YES)")
      packet_header = b'U\x00\x00\x02'
      btrl = 85
      btrh = 0
      packet_number = 2
      bytes_to_read = 85
      recv_data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      packet = <pymysql.protocol.MysqlPacket object at 0x70aa554982e0>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x70aa554982e0>
      errno = 1045
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errno = 1045
      errval = "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
Backup failed for Site westside.local. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['local.westside', 'westside.local'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x70aa55473420>
      exit_code = 1
      rollback_callback = None
      site = 'westside.local'
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x70aa54219360>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x70aa54219360>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x70aa543417c0>
      db_name = '_07fdbf6ba2f0be13'
      user = '_07fdbf6ba2f0be13'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x70aa54219360>
      site = 'local.westside'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x70aa543417c0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 483, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x70aa542193a0>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x70aa549bf5f0>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_07fdbf6ba2f0be13'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x70aa543eba60>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x70aa543ebb00>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x70aa542193a0>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_07fdbf6ba2f0be13'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x70aa542193a0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x70aa542193a0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x70aa542193a0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x70aa542193a0>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x70aa54675640>
      user = '_07fdbf6ba2f0be13'
      password = ********
      host = '127.0.0.1'
      database = '_07fdbf6ba2f0be13'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x70aa544f36a0>, <class 'int'>: <function escape_int at 0x70aa544f3740>, <class 'float'>: <function escape_float at 0x70aa544f37e0>, <class 'str'>: <function escape_str at 0x70aa544f3a60>, <class 'bytes'>: <function escape_bytes at 0x70aa544f39c0>, <class 'tuple'>: <function escape_sequence at 0x70aa544f3560>, <class 'list'>: <function escape_sequence at 0x70aa544f3560>, <class 'set'>: <function escape_sequence at 0x70aa544f3560>, <class 'frozenset'>: <function escape_sequence at 0x70aa544f3560>, <class 'dict'>: <function escape_dict at 0x70aa544f34c0>, <class 'NoneType'>: <function escape_None at 0x70aa544f3b00>, <class 'datetime.date'>: <function escape_date at 0x70aa544f3d80>, <class 'datetime.datetime'>: <function escape_datetime at 0x70aa544f3ce0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x70aa544f3ba0>, <class 'datetime.time'>: <function escape_time at 0x70aa544f3c40>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x70aa54675640>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x70aa54675640>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_07fdbf6ba2f0be13\x00\x14,\xdd\x10\xc9\x15ts\x10\x9c\x9d\x9d\xbd\xe1\xd9^"\x98E\xf4\x7f_07fdbf6ba2f0be13\x00mysql_native_password\x007\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x06382408'
      authresp = b',\xdd\x10\xc9\x15ts\x10\x9c\x9d\x9d\xbd\xe1\xd9^"\x98E\xf4\x7f'
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x06382408'
      k = b'_pid'
      v = b'382408'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x70aa54675640>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x15\x04#28000Access denied for user \'_07fdbf6ba2f0be13\'@\'localhost\' (using password: YES)")
      packet_header = b'U\x00\x00\x02'
      btrl = 85
      btrh = 0
      packet_number = 2
      bytes_to_read = 85
      recv_data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      packet = <pymysql.protocol.MysqlPacket object at 0x70aa542430a0>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x70aa542430a0>
      errno = 1045
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errno = 1045
      errval = "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
Backup failed for Site local.westside. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['local.westside', 'westside.local'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7f527d36b420>
      exit_code = 0
      rollback_callback = None
      site = 'local.westside'
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7f527c289420>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7f527c289420>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7f527c2894c0>
      db_name = '_07fdbf6ba2f0be13'
      user = '_07fdbf6ba2f0be13'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7f527c289420>
      site = 'local.westside'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7f527c2894c0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 483, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7f527c25a030>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x7f527e953320>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_07fdbf6ba2f0be13'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x7f527c25fa60>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x7f527c25fb00>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7f527c25a030>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_07fdbf6ba2f0be13'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7f527c25a030>
  File "apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7f527c25a030>
  File "apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7f527c25a030>
  File "apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7f527c25a030>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x7f527c3d4aa0>
      user = '_07fdbf6ba2f0be13'
      password = ********
      host = '127.0.0.1'
      database = '_07fdbf6ba2f0be13'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x7f527c3e76a0>, <class 'int'>: <function escape_int at 0x7f527c3e7740>, <class 'float'>: <function escape_float at 0x7f527c3e77e0>, <class 'str'>: <function escape_str at 0x7f527c3e7a60>, <class 'bytes'>: <function escape_bytes at 0x7f527c3e79c0>, <class 'tuple'>: <function escape_sequence at 0x7f527c3e7560>, <class 'list'>: <function escape_sequence at 0x7f527c3e7560>, <class 'set'>: <function escape_sequence at 0x7f527c3e7560>, <class 'frozenset'>: <function escape_sequence at 0x7f527c3e7560>, <class 'dict'>: <function escape_dict at 0x7f527c3e74c0>, <class 'NoneType'>: <function escape_None at 0x7f527c3e7b00>, <class 'datetime.date'>: <function escape_date at 0x7f527c3e7d80>, <class 'datetime.datetime'>: <function escape_datetime at 0x7f527c3e7ce0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x7f527c3e7ba0>, <class 'datetime.time'>: <function escape_time at 0x7f527c3e7c40>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x7f527c3d4aa0>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x7f527c3d4aa0>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_07fdbf6ba2f0be13\x00\x14\xfb\x0b\xe9qIP5\xf6t6\x9cJ\x12\x06=\xe3d\xb9^\xcd_07fdbf6ba2f0be13\x00mysql_native_password\x007\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x06472047'
      authresp = b'\xfb\x0b\xe9qIP5\xf6t6\x9cJ\x12\x06=\xe3d\xb9^\xcd'
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x06472047'
      k = b'_pid'
      v = b'472047'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x7f527c3d4aa0>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x15\x04#28000Access denied for user \'_07fdbf6ba2f0be13\'@\'localhost\' (using password: YES)")
      packet_header = b'U\x00\x00\x02'
      btrl = 85
      btrh = 0
      packet_number = 2
      bytes_to_read = 85
      recv_data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      packet = <pymysql.protocol.MysqlPacket object at 0x7f527c289480>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x7f527c289480>
      errno = 1045
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errno = 1045
      errval = "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
Backup failed for Site westside.local. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['local.westside', 'westside.local'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7f527d36b420>
      exit_code = 1
      rollback_callback = None
      site = 'westside.local'
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7f527c25a020>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7f527c25a020>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7f527c568a40>
      db_name = '_07fdbf6ba2f0be13'
      user = '_07fdbf6ba2f0be13'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7f527c25a020>
      site = 'local.westside'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7f527c568a40>
  File "apps/frappe/frappe/database/mariadb/database.py", line 483, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7f527e953320>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x7f527c730e30>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_07fdbf6ba2f0be13'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x7f527c25fa60>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x7f527c25fb00>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7f527e953320>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_07fdbf6ba2f0be13'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7f527e953320>
  File "apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7f527e953320>
  File "apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7f527e953320>
  File "apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7f527e953320>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x7f527ebfd640>
      user = '_07fdbf6ba2f0be13'
      password = ********
      host = '127.0.0.1'
      database = '_07fdbf6ba2f0be13'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x7f527c3e76a0>, <class 'int'>: <function escape_int at 0x7f527c3e7740>, <class 'float'>: <function escape_float at 0x7f527c3e77e0>, <class 'str'>: <function escape_str at 0x7f527c3e7a60>, <class 'bytes'>: <function escape_bytes at 0x7f527c3e79c0>, <class 'tuple'>: <function escape_sequence at 0x7f527c3e7560>, <class 'list'>: <function escape_sequence at 0x7f527c3e7560>, <class 'set'>: <function escape_sequence at 0x7f527c3e7560>, <class 'frozenset'>: <function escape_sequence at 0x7f527c3e7560>, <class 'dict'>: <function escape_dict at 0x7f527c3e74c0>, <class 'NoneType'>: <function escape_None at 0x7f527c3e7b00>, <class 'datetime.date'>: <function escape_date at 0x7f527c3e7d80>, <class 'datetime.datetime'>: <function escape_datetime at 0x7f527c3e7ce0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x7f527c3e7ba0>, <class 'datetime.time'>: <function escape_time at 0x7f527c3e7c40>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x7f527ebfd640>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x7f527ebfd640>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b"\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_07fdbf6ba2f0be13\x00\x14\xdd*Q1\xc0>\xcf~\x0b+\xa6\xa6'\xd5\xc1\xf8<\x91Z8_07fdbf6ba2f0be13\x00mysql_native_password\x007\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x06472047"
      authresp = b"\xdd*Q1\xc0>\xcf~\x0b+\xa6\xa6'\xd5\xc1\xf8<\x91Z8"
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x06472047'
      k = b'_pid'
      v = b'472047'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x7f527ebfd640>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x15\x04#28000Access denied for user \'_07fdbf6ba2f0be13\'@\'localhost\' (using password: YES)")
      packet_header = b'U\x00\x00\x02'
      btrl = 85
      btrh = 0
      packet_number = 2
      bytes_to_read = 85
      recv_data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      packet = <pymysql.protocol.MysqlPacket object at 0x7f527c0b3190>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x7f527c0b3190>
      errno = 1045
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errno = 1045
      errval = "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
Backup failed for Site local.westside. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['local.westside', 'westside.local'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x78806e76b420>
      exit_code = 0
      rollback_callback = None
      site = 'local.westside'
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x78806d689420>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x78806d689420>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x78806d6894c0>
      db_name = '_07fdbf6ba2f0be13'
      user = '_07fdbf6ba2f0be13'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x78806d689420>
      site = 'local.westside'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x78806d6894c0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 483, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x78806d65a030>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x78806fd53320>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_07fdbf6ba2f0be13'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x78806d65fa60>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x78806d65fb00>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x78806d65a030>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_07fdbf6ba2f0be13'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x78806d65a030>
  File "apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x78806d65a030>
  File "apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x78806d65a030>
  File "apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x78806d65a030>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x78806d7d4aa0>
      user = '_07fdbf6ba2f0be13'
      password = ********
      host = '127.0.0.1'
      database = '_07fdbf6ba2f0be13'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x78806d7e76a0>, <class 'int'>: <function escape_int at 0x78806d7e7740>, <class 'float'>: <function escape_float at 0x78806d7e77e0>, <class 'str'>: <function escape_str at 0x78806d7e7a60>, <class 'bytes'>: <function escape_bytes at 0x78806d7e79c0>, <class 'tuple'>: <function escape_sequence at 0x78806d7e7560>, <class 'list'>: <function escape_sequence at 0x78806d7e7560>, <class 'set'>: <function escape_sequence at 0x78806d7e7560>, <class 'frozenset'>: <function escape_sequence at 0x78806d7e7560>, <class 'dict'>: <function escape_dict at 0x78806d7e74c0>, <class 'NoneType'>: <function escape_None at 0x78806d7e7b00>, <class 'datetime.date'>: <function escape_date at 0x78806d7e7d80>, <class 'datetime.datetime'>: <function escape_datetime at 0x78806d7e7ce0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x78806d7e7ba0>, <class 'datetime.time'>: <function escape_time at 0x78806d7e7c40>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x78806d7d4aa0>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x78806d7d4aa0>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_07fdbf6ba2f0be13\x00\x1461\xd0\xe0>\xac\xf9IO\xf2\x8c\x12TH\x7f\x9c\xc0\x1ci\x9b_07fdbf6ba2f0be13\x00mysql_native_password\x007\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x06725893'
      authresp = b'61\xd0\xe0>\xac\xf9IO\xf2\x8c\x12TH\x7f\x9c\xc0\x1ci\x9b'
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x06725893'
      k = b'_pid'
      v = b'725893'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x78806d7d4aa0>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x15\x04#28000Access denied for user \'_07fdbf6ba2f0be13\'@\'localhost\' (using password: YES)")
      packet_header = b'U\x00\x00\x02'
      btrl = 85
      btrh = 0
      packet_number = 2
      bytes_to_read = 85
      recv_data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      packet = <pymysql.protocol.MysqlPacket object at 0x78806d689480>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x78806d689480>
      errno = 1045
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errno = 1045
      errval = "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
Backup failed for Site westside.local. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['local.westside', 'westside.local'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x78806e76b420>
      exit_code = 1
      rollback_callback = None
      site = 'westside.local'
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x78806d65a020>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x78806d65a020>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x78806d968a40>
      db_name = '_07fdbf6ba2f0be13'
      user = '_07fdbf6ba2f0be13'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x78806d65a020>
      site = 'local.westside'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x78806d968a40>
  File "apps/frappe/frappe/database/mariadb/database.py", line 483, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x78806fd53320>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x78806db30e30>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_07fdbf6ba2f0be13'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x78806d65fa60>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x78806d65fb00>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x78806fd53320>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_07fdbf6ba2f0be13'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x78806fd53320>
  File "apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x78806fd53320>
  File "apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x78806fd53320>
  File "apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x78806fd53320>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x78806fffd640>
      user = '_07fdbf6ba2f0be13'
      password = ********
      host = '127.0.0.1'
      database = '_07fdbf6ba2f0be13'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x78806d7e76a0>, <class 'int'>: <function escape_int at 0x78806d7e7740>, <class 'float'>: <function escape_float at 0x78806d7e77e0>, <class 'str'>: <function escape_str at 0x78806d7e7a60>, <class 'bytes'>: <function escape_bytes at 0x78806d7e79c0>, <class 'tuple'>: <function escape_sequence at 0x78806d7e7560>, <class 'list'>: <function escape_sequence at 0x78806d7e7560>, <class 'set'>: <function escape_sequence at 0x78806d7e7560>, <class 'frozenset'>: <function escape_sequence at 0x78806d7e7560>, <class 'dict'>: <function escape_dict at 0x78806d7e74c0>, <class 'NoneType'>: <function escape_None at 0x78806d7e7b00>, <class 'datetime.date'>: <function escape_date at 0x78806d7e7d80>, <class 'datetime.datetime'>: <function escape_datetime at 0x78806d7e7ce0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x78806d7e7ba0>, <class 'datetime.time'>: <function escape_time at 0x78806d7e7c40>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x78806fffd640>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x78806fffd640>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_07fdbf6ba2f0be13\x00\x14a\xc9\x9e\xb1\xeb\xab\xb2\x8f6\xb7\x1d\xedlQ\t\x88))\xf3\xfc_07fdbf6ba2f0be13\x00mysql_native_password\x007\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x06725893'
      authresp = b'a\xc9\x9e\xb1\xeb\xab\xb2\x8f6\xb7\x1d\xedlQ\t\x88))\xf3\xfc'
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x06725893'
      k = b'_pid'
      v = b'725893'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x78806fffd640>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x15\x04#28000Access denied for user \'_07fdbf6ba2f0be13\'@\'localhost\' (using password: YES)")
      packet_header = b'U\x00\x00\x02'
      btrl = 85
      btrh = 0
      packet_number = 2
      bytes_to_read = 85
      recv_data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      packet = <pymysql.protocol.MysqlPacket object at 0x78806d4b3190>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x78806d4b3190>
      errno = 1045
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errno = 1045
      errval = "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
Backup failed for Site local.westside. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['local.westside', 'westside.local'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x78706a167420>
      exit_code = 0
      rollback_callback = None
      site = 'local.westside'
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7870690854b0>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7870690854b0>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x787069085550>
      db_name = '_07fdbf6ba2f0be13'
      user = '_07fdbf6ba2f0be13'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7870690854b0>
      site = 'local.westside'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x787069085550>
  File "apps/frappe/frappe/database/mariadb/database.py", line 483, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x787069056090>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x7870690573e0>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_07fdbf6ba2f0be13'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x78706905ba60>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x78706905bb00>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x787069056090>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_07fdbf6ba2f0be13'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x787069056090>
  File "apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x787069056090>
  File "apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x787069056090>
  File "apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x787069056090>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x7870691e8770>
      user = '_07fdbf6ba2f0be13'
      password = ********
      host = '127.0.0.1'
      database = '_07fdbf6ba2f0be13'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x7870691e36a0>, <class 'int'>: <function escape_int at 0x7870691e3740>, <class 'float'>: <function escape_float at 0x7870691e37e0>, <class 'str'>: <function escape_str at 0x7870691e3a60>, <class 'bytes'>: <function escape_bytes at 0x7870691e39c0>, <class 'tuple'>: <function escape_sequence at 0x7870691e3560>, <class 'list'>: <function escape_sequence at 0x7870691e3560>, <class 'set'>: <function escape_sequence at 0x7870691e3560>, <class 'frozenset'>: <function escape_sequence at 0x7870691e3560>, <class 'dict'>: <function escape_dict at 0x7870691e34c0>, <class 'NoneType'>: <function escape_None at 0x7870691e3b00>, <class 'datetime.date'>: <function escape_date at 0x7870691e3d80>, <class 'datetime.datetime'>: <function escape_datetime at 0x7870691e3ce0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x7870691e3ba0>, <class 'datetime.time'>: <function escape_time at 0x7870691e3c40>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x7870691e8770>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x7870691e8770>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_07fdbf6ba2f0be13\x00\x14AN\xe0p\xd4\x86\x01\x07\xb7\xb3\xb7:\xaf<\x0cq0\xc0\xd8\xca_07fdbf6ba2f0be13\x00mysql_native_password\x008\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x071018690'
      authresp = b'AN\xe0p\xd4\x86\x01\x07\xb7\xb3\xb7:\xaf<\x0cq0\xc0\xd8\xca'
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x071018690'
      k = b'_pid'
      v = b'1018690'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x7870691e8770>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x15\x04#28000Access denied for user \'_07fdbf6ba2f0be13\'@\'localhost\' (using password: YES)")
      packet_header = b'U\x00\x00\x02'
      btrl = 85
      btrh = 0
      packet_number = 2
      bytes_to_read = 85
      recv_data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      packet = <pymysql.protocol.MysqlPacket object at 0x787069085630>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x787069085630>
      errno = 1045
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errno = 1045
      errval = "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
Backup failed for Site westside.local. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['local.westside', 'westside.local'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x78706a167420>
      exit_code = 1
      rollback_callback = None
      site = 'westside.local'
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x787069056080>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x787069056080>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x78706b753320>
      db_name = '_07fdbf6ba2f0be13'
      user = '_07fdbf6ba2f0be13'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x787069056080>
      site = 'local.westside'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x78706b753320>
  File "apps/frappe/frappe/database/mariadb/database.py", line 483, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7870690573e0>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x78706a06c380>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_07fdbf6ba2f0be13'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x78706905ba60>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x78706905bb00>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7870690573e0>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_07fdbf6ba2f0be13'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7870690573e0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7870690573e0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7870690573e0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7870690573e0>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x787069209c40>
      user = '_07fdbf6ba2f0be13'
      password = ********
      host = '127.0.0.1'
      database = '_07fdbf6ba2f0be13'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x7870691e36a0>, <class 'int'>: <function escape_int at 0x7870691e3740>, <class 'float'>: <function escape_float at 0x7870691e37e0>, <class 'str'>: <function escape_str at 0x7870691e3a60>, <class 'bytes'>: <function escape_bytes at 0x7870691e39c0>, <class 'tuple'>: <function escape_sequence at 0x7870691e3560>, <class 'list'>: <function escape_sequence at 0x7870691e3560>, <class 'set'>: <function escape_sequence at 0x7870691e3560>, <class 'frozenset'>: <function escape_sequence at 0x7870691e3560>, <class 'dict'>: <function escape_dict at 0x7870691e34c0>, <class 'NoneType'>: <function escape_None at 0x7870691e3b00>, <class 'datetime.date'>: <function escape_date at 0x7870691e3d80>, <class 'datetime.datetime'>: <function escape_datetime at 0x7870691e3ce0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x7870691e3ba0>, <class 'datetime.time'>: <function escape_time at 0x7870691e3c40>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x787069209c40>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x787069209c40>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_07fdbf6ba2f0be13\x00\x14W\xd0\x12>l}\x96^\xaat.0\xa8\x8e\xfb\xad\x86~\xc7"_07fdbf6ba2f0be13\x00mysql_native_password\x008\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x071018690'
      authresp = b'W\xd0\x12>l}\x96^\xaat.0\xa8\x8e\xfb\xad\x86~\xc7"'
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x071018690'
      k = b'_pid'
      v = b'1018690'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x787069209c40>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x15\x04#28000Access denied for user \'_07fdbf6ba2f0be13\'@\'localhost\' (using password: YES)")
      packet_header = b'U\x00\x00\x02'
      btrl = 85
      btrh = 0
      packet_number = 2
      bytes_to_read = 85
      recv_data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      packet = <pymysql.protocol.MysqlPacket object at 0x7870690854b0>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x7870690854b0>
      errno = 1045
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errno = 1045
      errval = "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
Backup failed for Site local.westside. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['local.westside', 'westside.local'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7d886d46b420>
      exit_code = 0
      rollback_callback = None
      site = 'local.westside'
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7d886c389120>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7d886c389120>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7d886d4b1250>
      db_name = '_07fdbf6ba2f0be13'
      user = '_07fdbf6ba2f0be13'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7d886c389120>
      site = 'local.westside'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7d886d4b1250>
  File "apps/frappe/frappe/database/mariadb/database.py", line 483, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7d886c35a060>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x7d886c389220>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_07fdbf6ba2f0be13'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x7d886c35fa60>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x7d886c35fb00>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7d886c35a060>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_07fdbf6ba2f0be13'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7d886c35a060>
  File "apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7d886c35a060>
  File "apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7d886c35a060>
  File "apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7d886c35a060>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x7d886c359df0>
      user = '_07fdbf6ba2f0be13'
      password = ********
      host = '127.0.0.1'
      database = '_07fdbf6ba2f0be13'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x7d886c4e76a0>, <class 'int'>: <function escape_int at 0x7d886c4e7740>, <class 'float'>: <function escape_float at 0x7d886c4e77e0>, <class 'str'>: <function escape_str at 0x7d886c4e7a60>, <class 'bytes'>: <function escape_bytes at 0x7d886c4e79c0>, <class 'tuple'>: <function escape_sequence at 0x7d886c4e7560>, <class 'list'>: <function escape_sequence at 0x7d886c4e7560>, <class 'set'>: <function escape_sequence at 0x7d886c4e7560>, <class 'frozenset'>: <function escape_sequence at 0x7d886c4e7560>, <class 'dict'>: <function escape_dict at 0x7d886c4e74c0>, <class 'NoneType'>: <function escape_None at 0x7d886c4e7b00>, <class 'datetime.date'>: <function escape_date at 0x7d886c4e7d80>, <class 'datetime.datetime'>: <function escape_datetime at 0x7d886c4e7ce0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x7d886c4e7ba0>, <class 'datetime.time'>: <function escape_time at 0x7d886c4e7c40>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x7d886c359df0>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x7d886c359df0>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_07fdbf6ba2f0be13\x00\x14\x0b@\xad\xdawm>\xf2>\xc2U\xa8\x8f\xe2e==Q\xa4\xe0_07fdbf6ba2f0be13\x00mysql_native_password\x007\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x06119526'
      authresp = b'\x0b@\xad\xdawm>\xf2>\xc2U\xa8\x8f\xe2e==Q\xa4\xe0'
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x06119526'
      k = b'_pid'
      v = b'119526'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x7d886c359df0>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x15\x04#28000Access denied for user \'_07fdbf6ba2f0be13\'@\'localhost\' (using password: YES)")
      packet_header = b'U\x00\x00\x02'
      btrl = 85
      btrh = 0
      packet_number = 2
      bytes_to_read = 85
      recv_data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      packet = <pymysql.protocol.MysqlPacket object at 0x7d886c3891e0>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x7d886c3891e0>
      errno = 1045
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errno = 1045
      errval = "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
Backup failed for Site westside.local. Database or site_config.json may be corrupted
Traceback with variables (most recent call last):
  File "apps/frappe/frappe/commands/site.py", line 843, in backup
    odb = scheduled_backup(
      context = {'sites': ['local.westside', 'westside.local'], 'force': False, 'verbose': True, 'profile': False}
      with_files = False
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_backup_conf = False
      verbose = True
      compress = False
      include = ''
      exclude = ''
      old_backup_metadata = False
      scheduled_backup = <function scheduled_backup at 0x7d886d46b420>
      exit_code = 1
      rollback_callback = None
      site = 'westside.local'
  File "apps/frappe/frappe/utils/backups.py", line 568, in scheduled_backup
    return new_backup(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7d886c35a050>
  File "apps/frappe/frappe/utils/backups.py", line 605, in new_backup
    odb = BackupGenerator(
      older_than = 6
      ignore_files = True
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      backup_path_conf = None
      ignore_conf = False
      include_doctypes = ''
      exclude_doctypes = ''
      compress = False
      force = True
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7d886c35a050>
  File "apps/frappe/frappe/utils/backups.py", line 89, in __init__
    self.setup_backup_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7d886c9c98e0>
      db_name = '_07fdbf6ba2f0be13'
      user = '_07fdbf6ba2f0be13'
      password = ********
      backup_path = None
      backup_path_db = None
      backup_path_files = None
      backup_path_private_files = None
      db_socket = None
      db_host = '127.0.0.1'
      db_port = '3306'
      db_type = 'mariadb'
      backup_path_conf = None
      ignore_conf = False
      compress_files = False
      include_doctypes = ''
      exclude_doctypes = ''
      verbose = True
      old_backup_metadata = False
      rollback_callback = <frappe.utils.CallbackManager object at 0x7d886c35a050>
      site = 'local.westside'
  File "apps/frappe/frappe/utils/backups.py", line 121, in setup_backup_tables
    existing_tables = frappe.db.get_tables()
      self = <frappe.utils.backups.BackupGenerator object at 0x7d886c9c98e0>
  File "apps/frappe/frappe/database/mariadb/database.py", line 483, in get_tables
    .run(pluck=True)
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7d886d4b1250>
      cached = True
      to_query = True
      tables = None
      information_schema = <pypika.queries.Schema object at 0x7d886c2b96d0>
  File "apps/frappe/frappe/query_builder/utils.py", line 87, in execute_query
    result = frappe.db.sql(query, params, *args, **kwargs)  # nosemgrep
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      args = ()
      kwargs = {'pluck': True}
      child_queries = []
      params = {'param1': '_07fdbf6ba2f0be13'}
      execute_child_queries = <function patch_query_execute.<locals>.execute_child_queries at 0x7d886c35fa60>
      prepare_query = <function patch_query_execute.<locals>.prepare_query at 0x7d886c35fb00>
  File "apps/frappe/frappe/database/database.py", line 207, in sql
    self.connect()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7d886d4b1250>
      query = 'SELECT `table_name` FROM `information_schema`.`tables` WHERE `table_schema`=%(param1)s'
      values = {'param1': '_07fdbf6ba2f0be13'}
      as_dict = 0
      as_list = 0
      debug = False
      ignore_ddl = 0
      auto_commit = 0
      update = None
      explain = False
      run = True
      pluck = True
      as_iterator = False
  File "apps/frappe/frappe/database/database.py", line 112, in connect
    self._conn: "MariadbConnection" | "PostgresConnection" = self.get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7d886d4b1250>
  File "apps/frappe/frappe/database/mariadb/database.py", line 107, in get_connection
    conn = self._get_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7d886d4b1250>
  File "apps/frappe/frappe/database/mariadb/database.py", line 113, in _get_connection
    return self.create_connection()
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7d886d4b1250>
  File "apps/frappe/frappe/database/mariadb/database.py", line 116, in create_connection
    return pymysql.connect(**self.get_connection_settings())
      self = <frappe.database.mariadb.database.MariaDBDatabase object at 0x7d886d4b1250>
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 361, in __init__
    self.connect()
      self = <pymysql.connections.Connection object at 0x7d886c3891c0>
      user = '_07fdbf6ba2f0be13'
      password = ********
      host = '127.0.0.1'
      database = '_07fdbf6ba2f0be13'
      unix_socket = None
      port = 3306
      charset = 'utf8mb4'
      collation = 'utf8mb4_unicode_ci'
      sql_mode = None
      read_default_file = None
      conv = {<class 'bool'>: <function escape_bool at 0x7d886c4e76a0>, <class 'int'>: <function escape_int at 0x7d886c4e7740>, <class 'float'>: <function escape_float at 0x7d886c4e77e0>, <class 'str'>: <function escape_str at 0x7d886c4e7a60>, <class 'bytes'>: <function escape_bytes at 0x7d886c4e79c0>, <class 'tuple'>: <function escape_sequence at 0x7d886c4e7560>, <class 'list'>: <function escape_sequence at 0x7d886c4e7560>, <class 'set'>: <function escape_sequence at 0x7d886c4e7560>, <class 'frozenset'>: <function escape_sequence at 0x7d886c4e7560>, <class 'dict'>: <function escape_dict at 0x7d886c4e74c0>, <class 'NoneType'>: <function escape_None at 0x7d886c4e7b00>, <class 'datetime.date'>: <function escape_date at 0x7d886c4e7d80>, <class 'datetime.datetime'>: <function escape_datetime at 0x7d886c4e7ce0>, <class 'datetime.timedelta'>: <function escape_timedelta at 0x7d886c4e7ba0>, <class 'datetime.time'>: <function escape_time at 0x7d886c4e7c40>, <class 'time.struct_time'>: <function escape_struc...
      use_unicode = True
      client_flag = 3842573
      cursorclass = <class 'pymysql.cursors.Cursor'>
      init_command = None
      connect_timeout = 10
      read_default_group = None
      autocommit = False
      local_infile = False
      max_allowed_packet = 16777216
      defer_connect = False
      auth_plugin_map = None
      read_timeout = None
      write_timeout = None
      bind_address = None
      binary_prefix = False
      program_name = None
      server_public_key = ********
      ssl = None
      ssl_ca = None
      ssl_cert = None
      ssl_disabled = None
      ssl_key = ********
      ssl_key_password = ********
      ssl_verify_cert = None
      ssl_verify_identity = None
      compress = None
      named_pipe = None
      passwd = ********
      db = None
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 669, in connect
    self._request_authentication()
      self = <pymysql.connections.Connection object at 0x7d886c3891c0>
      sock = <socket.socket [closed] fd=-1, family=2, type=1, proto=6>
      kwargs = {}
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 957, in _request_authentication
    auth_packet = self._read_packet()
      self = <pymysql.connections.Connection object at 0x7d886c3891c0>
      charset_id = 45
      data_init = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'
      data = b'\r\xa2:\x00\xff\xff\xff\x00-\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00_07fdbf6ba2f0be13\x00\x14\x04\xaa\xd0\x86\xaa\x14#\xf4[\xa3\xfe\xf6\x1c\xb9\xe2X\xa02V\\_07fdbf6ba2f0be13\x00mysql_native_password\x007\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x06119526'
      authresp = b'\x04\xaa\xd0\x86\xaa\x14#\xf4[\xa3\xfe\xf6\x1c\xb9\xe2X\xa02V\\'
      plugin_name = b'mysql_native_password'
      connect_attrs = b'\x0c_client_name\x07pymysql\x0f_client_version\x051.1.1\x04_pid\x06119526'
      k = b'_pid'
      v = b'119526'
  File "env/lib/python3.12/site-packages/pymysql/connections.py", line 775, in _read_packet
    packet.raise_for_error()
      self = <pymysql.connections.Connection object at 0x7d886c3891c0>
      packet_type = <class 'pymysql.protocol.MysqlPacket'>
      buff = bytearray(b"\xff\x15\x04#28000Access denied for user \'_07fdbf6ba2f0be13\'@\'localhost\' (using password: YES)")
      packet_header = b'U\x00\x00\x02'
      btrl = 85
      btrh = 0
      packet_number = 2
      bytes_to_read = 85
      recv_data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      packet = <pymysql.protocol.MysqlPacket object at 0x7d886c389120>
  File "env/lib/python3.12/site-packages/pymysql/protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
      self = <pymysql.protocol.MysqlPacket object at 0x7d886c389120>
      errno = 1045
  File "env/lib/python3.12/site-packages/pymysql/err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
      data = b"\xff\x15\x04#28000Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errno = 1045
      errval = "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)"
      errorclass = <class 'pymysql.err.OperationalError'>
pymysql.err.OperationalError: (1045, "Access denied for user '_07fdbf6ba2f0be13'@'localhost' (using password: YES)")
