{"version": 3, "sources": ["../../../../../../../../tmp/tmp-4109-uaLsB72kqPr8/wiki/wiki/public/scss/wiki.bundle.css"], "sourcesContent": [":root {\n  --neutral-white: #ffffff;\n  --neutral-black: #000000;\n  --neutral: var(--neutral-white);\n  --invert-neutral: var(--neutral-black);\n  --gray-50: #f8f8f8;\n  --gray-100: #f3f3f3;\n  --gray-200: #ededed;\n  --gray-300: #e2e2e2;\n  --gray-400: #c7c7c7;\n  --gray-500: #999999;\n  --gray-600: #7c7c7c;\n  --gray-700: #525252;\n  --gray-800: #383838;\n  --gray-900: #171717;\n  --blue-50: #f7fbfd;\n  --blue-100: #edf6fd;\n  --blue-200: #e3f1fd;\n  --blue-300: #c9e7fc;\n  --blue-400: #70b6f0;\n  --blue-500: #0289f7;\n  --blue-600: #007be0;\n  --blue-700: #0070cc;\n  --blue-800: #005ca3;\n  --blue-900: #004880;\n  --green-50: #f3fcf5;\n  --green-100: #e4f5e9;\n  --green-200: #daf0e1;\n  --green-300: #cae5d4;\n  --green-400: #b6dec5;\n  --green-500: #59ba8b;\n  --green-600: #30a66d;\n  --green-700: #278f5e;\n  --green-800: #16794c;\n  --green-900: #173b2c;\n  --red-50: #fff7f7;\n  --red-100: #fff0f0;\n  --red-200: #fcd7d7;\n  --red-300: #f9c6c6;\n  --red-400: #eb9091;\n  --red-500: #e03636;\n  --red-600: #cc2929;\n  --red-700: #b52a2a;\n  --red-800: #941f1f;\n  --red-900: #6b1515;\n  --orange-50: #fff9f5;\n  --orange-100: #fff1e7;\n  --orange-200: #fce6d5;\n  --orange-300: #f7d6bd;\n  --orange-400: #f0b58b;\n  --orange-500: #e86c13;\n  --orange-600: #d45a08;\n  --orange-700: #bd3e0c;\n  --orange-800: #9e3513;\n  --orange-900: #6b2711;\n  --amber-50: #fdfaed;\n  --amber-100: #fcf3cf;\n  --amber-200: #f7e28d;\n  --amber-300: #f5d261;\n  --amber-400: #f2be3a;\n  --amber-500: #e79913;\n  --amber-600: #db7706;\n  --amber-700: #b35309;\n  --amber-800: #91400d;\n  --amber-900: #763813;\n  --yellow-50: #fffcef;\n  --yellow-100: #fff7d3;\n  --yellow-200: #f7e9a8;\n  --yellow-300: #f5e171;\n  --yellow-400: #f2d14b;\n  --yellow-500: #edba13;\n  --yellow-600: #d1930d;\n  --yellow-700: #ab6e05;\n  --yellow-800: #8c5600;\n  --yellow-900: #733f12;\n  --cyan-50: #f5fbfc;\n  --cyan-100: #e0f8ff;\n  --cyan-200: #b3ecfc;\n  --cyan-300: #94e6ff;\n  --cyan-400: #6bd3f2;\n  --cyan-500: #34bae3;\n  --cyan-600: #32a4c7;\n  --cyan-700: #267a94;\n  --cyan-800: #125c73;\n  --cyan-900: #164759;\n  --teal-50: #f0fdfa;\n  --teal-100: #e6f7f4;\n  --teal-200: #bae8e1;\n  --teal-300: #97ded4;\n  --teal-400: #73d1c4;\n  --teal-500: #36baad;\n  --teal-600: #0b9e92;\n  --teal-700: #0f736b;\n  --teal-800: #115c57;\n  --teal-900: #114541;\n  --violet-50: #fbfaff;\n  --violet-100: #f5f2ff;\n  --violet-200: #e5e1fa;\n  --violet-300: #dad2f7;\n  --violet-400: #bdb1f0;\n  --violet-500: #6846e3;\n  --violet-600: #5f46c7;\n  --violet-700: #4f3da1;\n  --violet-800: #392980;\n  --violet-900: #251959;\n  --pink-50: #fff7fc;\n  --pink-100: #feeef8;\n  --pink-200: #f8e2f0;\n  --pink-300: #f2d4e6;\n  --pink-400: #e9c4da;\n  --pink-500: #e34aa6;\n  --pink-600: #cf3a96;\n  --pink-700: #9c2671;\n  --pink-800: #801458;\n  --pink-900: #570f3e;\n  --purple-50: #fdfaff;\n  --purple-100: #f9f0ff;\n  --purple-200: #f1e5fa;\n  --purple-300: #e9d6f5;\n  --purple-400: #d6c1e6;\n  --purple-500: #9c45e3;\n  --purple-600: #8642c2;\n  --purple-700: #6e399d;\n  --purple-800: #5c2f83;\n  --purple-900: #401863;\n  --white-overlay-50: rgba(255, 255, 255, 0.09);\n  --white-overlay-100: rgba(255, 255, 255, 0.18);\n  --white-overlay-200: rgba(255, 255, 255, 0.27);\n  --white-overlay-300: rgba(255, 255, 255, 0.36);\n  --white-overlay-400: rgba(255, 255, 255, 0.45);\n  --white-overlay-500: rgba(255, 255, 255, 0.54);\n  --white-overlay-600: rgba(255, 255, 255, 0.63);\n  --white-overlay-700: rgba(255, 255, 255, 0.72);\n  --white-overlay-800: rgba(255, 255, 255, 0.81);\n  --white-overlay-900: rgba(255, 255, 255, 0.9);\n  --black-overlay-50: rgba(0, 0, 0, 0.09);\n  --black-overlay-100: rgba(0, 0, 0, 0.18);\n  --black-overlay-200: rgba(0, 0, 0, 0.27);\n  --black-overlay-300: rgba(0, 0, 0, 0.36);\n  --black-overlay-400: rgba(0, 0, 0, 0.45);\n  --black-overlay-500: rgba(0, 0, 0, 0.54);\n  --black-overlay-600: rgba(0, 0, 0, 0.63);\n  --black-overlay-700: rgba(0, 0, 0, 0.72);\n  --black-overlay-800: rgba(0, 0, 0, 0.81);\n  --black-overlay-900: rgba(0, 0, 0, 0.9);\n  --linear-black: linear-gradient(\n  \tto bottom,\n  \trgba(46, 46, 46, 0.18) 0%,\n  \trgba(36, 36, 36, 0.14) 100%\n  );\n  --linear-blue: linear-gradient(\n  \tto bottom,\n  \trgba(17, 142, 245, 0.067) 0%,\n  \trgba(7, 127, 247, 0.029) 100%\n  );\n  --angular-white: conic-gradient(rgba(255, 255, 255, 1) 72.38%, rgba(255, 255, 255, 1) 99.87%);\n  --angular-black: conic-gradient(rgba(56, 56, 56, 0.22) 72.38%, rgba(56, 56, 56, 0.22) 99.87%);\n  --angular-green: conic-gradient(\n  \trgba(23, 117, 75, 0.092) 72.38%,\n  \trgba(23, 117, 75, 0.092) 99.87%\n  );\n  --angular-red: conic-gradient(\n  \trgba(205, 41, 41, 0.804) 72.38%,\n  \trgba(205, 41, 41, 0.804) 99.87%\n  );\n  --angular-blue: conic-gradient(rgba(0, 110, 219, 0) 72.38%, rgba(0, 110, 219, 0) 99.87%);\n}\n\n:root {\n  --font-stack: \"InterVariable\", \"Inter\", \"-apple-system\", \"BlinkMacSystemFont\", \"Segoe UI\",\n  \t\"Roboto\", \"Oxygen\", \"Ubuntu\", \"Cantarell\", \"Fira Sans\", \"Droid Sans\", \"Helvetica Neue\",\n  \tsans-serif;\n  --text-tiny: 11px;\n  --text-2xs: 12px;\n  --text-xs: 12px;\n  --text-sm: 13px;\n  --text-md: 13px;\n  --text-base: 14px;\n  --text-lg: 16px;\n  --text-xl: 18px;\n  --text-2xl: 20px;\n  --text-3xl: 24px;\n  --text-4xl: 26px;\n  --text-5xl: 28px;\n  --text-6xl: 32px;\n  --text-7xl: 40px;\n  --text-8xl: 44px;\n  --text-9xl: 48px;\n  --text-10xl: 52px;\n  --text-11xl: 56px;\n  --text-12xl: 64px;\n  --weight-regular: 420;\n  --weight-medium: 500;\n  --weight-semibold: 600;\n  --weight-bold: 700;\n  --weight-black: 800;\n  --text-line-height-3xl: 115%;\n  --text-line-height-4xl: 160%;\n  --text-line-height-7xl: 140%;\n  --text-line-height-12xl: 130%;\n  --text-line-height-14xl: 120%;\n  --para-line-height-2-xs: 160%;\n  --para-line-height-sm: 150%;\n  --para-line-height-2xl: 148%;\n  --para-line-height-3xl: 140%;\n  --heading-color: var(--gray-900);\n  --text-neutral: var(--gray-900);\n  --text-color: var(--gray-800);\n  --text-muted: var(--gray-700);\n  --text-light: var(--gray-600);\n  --text-dark: var(--fg-color);\n}\n\n:root {\n  --shadow-xs: rgba(0, 0, 0, 0.05) 0px 0.5px 0px 0px, rgba(0, 0, 0, 0.08) 0px 0px 0px 1px,\n  \trgba(0, 0, 0, 0.05) 0px 2px 4px 0px;\n  --shadow-sm: 0px 1px 2px rgba(0, 0, 0, 0.1);\n  --shadow-base: 0px 0px 1px rgba(0, 0, 0, 0.45), 0px 1px 2px rgba(0, 0, 0, 0.1);\n  --shadow-md: 0px 0px 1px rgba(0, 0, 0, 0.12), 0px 0.5px 2px rgba(0, 0, 0, 0.15),\n  \t0px 2px 3px rgba(0, 0, 0, 0.16);\n  --shadow-lg: 0px 0px 1px rgba(0, 0, 0, 0.35), 0px 6px 8px -4px rgba(0, 0, 0, 0.1);\n  --shadow-xl: 0px 0px 1px rgba(0, 0, 0, 0.19), 0px 1px 2px rgba(0, 0, 0, 0.07),\n  \t0px 6px 15px -5px rgba(0, 0, 0, 0.11);\n  --shadow-2xl: 0px 0px 1px rgba(0, 0, 0, 0.2), 0px 1px 3px rgba(0, 0, 0, 0.05),\n  \t0px 10px 24px -3px rgba(0, 0, 0, 0.1);\n  --focus-default: 0px 0px 0px 2px #c9c9c9;\n  --focus-blue: 0px 0px 0px 2px #65b9fc;\n  --focus-green: 0px 0px 0px 2px #5bb98c;\n  --focus-yellow: 0px 0px 0px 2px #fff0ad;\n  --focus-red: 0px 0px 0px 2px #eb9091;\n  --custom-status: 0px 0px 0px 1.5px #ffffff;\n  --custom-shadow-sm: 0px 1px 4px rgba(0, 0, 0, 0.1);\n  --drop-shadow: 0px 0.5px 0px rgba(0, 0, 0, 0.05), 0px 0px 0px rgba(0, 0, 0, 0),\n  \t0px 2px 4px rgba(0, 0, 0, 0.05);\n}\n\n:root {\n  --input-padding: 6px 8px;\n  --dropdown-padding: 4px 8px;\n  --grid-padding: 10px 8px;\n  --disabled-input-padding: 3px 8px;\n  --number-card-padding: 8px 8px 8px 12px;\n}\n\n:root {\n  --border-radius-tiny: 4px;\n  --border-radius-sm: 8px;\n  --border-radius: 8px;\n  --border-radius-md: 10px;\n  --border-radius-lg: 12px;\n  --border-radius-xl: 16px;\n  --border-radius-2xl: 20px;\n  --border-radius-full: 999px;\n}\n\n.font-size-xs {\n  font-size: 0.75rem;\n}\n\n.font-size-sm {\n  font-size: 0.875rem;\n}\n\n.font-size-base {\n  font-size: 1rem;\n}\n\n.font-size-lg {\n  font-size: 1.125rem;\n}\n\n.font-size-xl {\n  font-size: 1.25rem;\n}\n\n.font-size-2xl {\n  font-size: 1.5rem;\n}\n\n.font-size-3xl {\n  font-size: 1.875rem;\n}\n\n.font-size-4xl {\n  font-size: 2.25rem;\n}\n\n.font-size-5xl {\n  font-size: 3rem;\n}\n\n.font-size-6xl {\n  font-size: 4rem;\n}\n\n:root,\n[data-theme=light] {\n  --brand-color: var(--primary);\n  --padding-xs: 5px;\n  --padding-sm: 6px;\n  --padding-md: 15px;\n  --padding-lg: 20px;\n  --padding-xl: 30px;\n  --padding-2xl: 40px;\n  --margin-xs: 5px;\n  --margin-sm: 10px;\n  --margin-md: 15px;\n  --margin-lg: 20px;\n  --margin-xl: 30px;\n  --margin-2xl: 40px;\n  --modal-shadow: var(--shadow-md);\n  --card-shadow: var(--shadow-sm);\n  --btn-shadow: var(--shadow-xs);\n  --navbar-height: 48px;\n  --icon-fill: transparent;\n  --icon-fill-bg: var(--fg-color);\n  --icon-stroke: var(--gray-800);\n  --bg-blue: var(--blue-100);\n  --bg-light-blue: var(--blue-50);\n  --bg-dark-blue: var(--blue-300);\n  --bg-green: var(--green-100);\n  --bg-yellow: var(--yellow-100);\n  --bg-orange: var(--orange-100);\n  --bg-red: var(--red-100);\n  --bg-gray: var(--gray-100);\n  --bg-grey: var(--gray-100);\n  --bg-light-gray: var(--gray-100);\n  --bg-dark-gray: var(--gray-400);\n  --bg-darkgrey: var(--gray-400);\n  --bg-purple: var(--purple-100);\n  --bg-pink: var(--pink-50);\n  --bg-cyan: var(--cyan-50);\n  --text-on-blue: var(--blue-700);\n  --text-on-light-blue: var(--blue-600);\n  --text-on-dark-blue: var(--blue-800);\n  --text-on-green: var(--green-800);\n  --text-on-yellow: var(--yellow-700);\n  --text-on-orange: var(--orange-700);\n  --text-on-red: var(--red-700);\n  --text-on-gray: var(--gray-700);\n  --text-on-grey: var(--gray-700);\n  --text-on-darkgrey: var(--gray-800);\n  --text-on-dark-gray: var(--gray-800);\n  --text-on-light-gray: var(--gray-800);\n  --text-on-purple: var(--purple-700);\n  --text-on-pink: var(--pink-700);\n  --text-on-cyan: var(--cyan-700);\n  --alert-text-danger: var(--red-600);\n  --alert-text-warning: var(--yellow-700);\n  --alert-text-info: var(--blue-700);\n  --alert-text-success: var(--green-700);\n  --alert-bg-danger: var(--red-50);\n  --alert-bg-warning: var(--yellow-50);\n  --alert-bg-info: var(--blue-50);\n  --alert-bg-success: var(--green-100);\n  --bg-color: white;\n  --fg-color: white;\n  --subtle-accent: var(--gray-50);\n  --subtle-fg: var(--gray-100);\n  --navbar-bg: white;\n  --fg-hover-color: var(--gray-100);\n  --card-bg: var(--fg-color);\n  --disabled-text-color: var(--gray-600);\n  --disabled-control-bg: var(--gray-50);\n  --control-bg: var(--gray-100);\n  --control-bg-on-gray: var(--gray-200);\n  --awesomebar-focus-bg: var(--fg-color);\n  --modal-bg: white;\n  --toast-bg: var(--modal-bg);\n  --popover-bg: white;\n  --awesomplete-hover-bg: var(--control-bg);\n  --btn-primary: var(--gray-900);\n  --btn-default-bg: var(--gray-100);\n  --btn-default-hover-bg: var(--gray-300);\n  --border-primary: var(--gray-900);\n  --sidebar-select-color: var(--gray-100);\n  --scrollbar-thumb-color: var(--gray-400);\n  --scrollbar-track-color: var(--gray-200);\n  --shadow-inset: inset 0px -1px 0px var(--gray-300);\n  --border-color: var(--gray-200);\n  --dark-border-color: var(--gray-300);\n  --table-border-color: var(--gray-200);\n  --highlight-color: var(--gray-50);\n  --yellow-highlight-color: var(--yellow-50);\n  --btn-group-border-color: var(--gray-300);\n  --placeholder-color: var(--gray-50);\n  --highlight-shadow: 1px 1px 10px var(--blue-50), 0px 0px 4px var(--blue-600);\n  --code-block-bg: var(--gray-900);\n  --code-block-text: var(--gray-400);\n  --primary-color: var(--gray-900);\n  --btn-height: 28px;\n  --input-height: 28px;\n  --input-disabled-bg: var(--gray-200);\n  --checkbox-right-margin: var(--margin-xs);\n  --checkbox-size: 14px;\n  --checkbox-color: var(--neutral-black);\n  --checkbox-focus-shadow: 0 0 0 2px var(--gray-300);\n  --checkbox-gradient: linear-gradient(180deg, var(--primary) -124.51%, var(--primary) 100%);\n  --checkbox-disabled-gradient: linear-gradient(\n  \t180deg,\n  \tvar(--disabled-control-bg) -124.51%,\n  \tvar(--disabled-control-bg) 100%\n  );\n  --switch-bg: var(--gray-300);\n  --diff-added: var(--green-100);\n  --diff-removed: var(--red-100);\n  --right-arrow-svg: url(\"data: image/svg+xml;utf8, <svg width='6' height='8' viewBox='0 0 6 8' fill='none' xmlns='http://www.w3.org/2000/svg'><path d='M1.25 7.5L4.75 4L1.25 0.5' stroke='%231F272E' stroke-linecap='round' stroke-linejoin='round'/></svg>\");\n  --left-arrow-svg: url(\"data: image/svg+xml;utf8, <svg width='6' height='8' viewBox='0 0 6 8' fill='none' xmlns='http://www.w3.org/2000/svg'><path d='M7.5 9.5L4 6l3.5-3.5' stroke='%231F272E' stroke-linecap='round' stroke-linejoin='round'></path></svg>\");\n}\n\n:root {\n  --light: #f3f3f3;\n  --font-size-xs: 0.75rem;\n  --font-size-sm: 0.875rem;\n  --font-size-base: 1rem;\n  --font-size-lg: 1.125rem;\n  --font-size-xl: 1.25rem;\n  --font-size-2xl: 1.5rem;\n  --font-size-3xl: 1.875rem;\n  --font-size-4xl: 2.5rem;\n  --font-size-5xl: 3rem;\n  --font-size-6xl: 4rem;\n  --card-border-radius: 0.75rem;\n}\n\nbody {\n  --gray-700: #242a30;\n  --gray-800: #1c2126;\n  --gray-900: #161a1f;\n  --text-color: #494949;\n  --background-color: white;\n  --sidebar-bg-color: #f8f8f8;\n  --sidebar-hover-color: #ebebe9;\n  --admin-banner-bg: #f8f8f8;\n  --admin-banner-text: #242a30;\n  --admin-banner-btn-bg: #242a30;\n  --admin-banner-btn-active-bg: #383838;\n  --admin-banner-btn-text: #fff;\n  --admin-banner-btn-hover-bg: #1c2126;\n  --btn-primary-color: #fff;\n  --btn-primary-bg-color: #171717;\n  --btn-primary-hover-bg-color: #383838;\n  --btn-primary-active-bg-color: #525252;\n  --btn-secondary-color: #383838;\n  --btn-secondary-bg-color: #f3f3f3;\n  --btn-secondary-hover-bg-color: #ededed;\n  --btn-secondary-active-bg-color: #e2e2e2;\n  --active-item-color: #f1f1f0;\n  --active-item-text-color: var(--primary);\n  --searchbar-color: #f4f5f6;\n  --border-color: var(--gray-300);\n  --bqoute-border-color: var(--gray-400);\n  --bqoute-bg-color: var(--gray-50);\n  --code-bg-color: var(--light);\n  --code-text-color: var(--purple-600);\n  --sidebar-text-color: var(--gray-600);\n  --sidebar-hover-color: #f3f3f3;\n  --sidebar-active-item-color: #fff;\n  --htmldiff-ins-color: #dcfce7;\n  --htmldiff-del-color: #fee2e2;\n  --diff-ins-bg-color: #dcfce7;\n  --diff-ins-text-color: var(--text-color);\n  --diff-del-bg-color: #fee2e2;\n  --diff-del-text-color: var(--text-color);\n  --editor-button-text-color: var(--gray-700);\n  --editor-hover-button-color: var(--gray-100);\n  --navbar-dropdown-bg-color: white;\n  --navbar-dropdown-hover-color: #c0c0c0;\n  --toc-hover-text-color: var(--gray-900);\n  --icon-stroke: var(--text-color);\n  --editor-line-no-bg-color: var(--gray-100);\n  --editor-background-color: #f3f3f3;\n  --button-background-color: #383838;\n  --editor-toolbar-btn-color: var(--gray-800);\n  --editor-toolbar-btn-bg-color: var(--editor-background-color);\n  --editor-text-selection-color: #d0d0d0;\n  --content-text-color: #494949;\n  --table-border-color: #cbcbcb;\n  --table-header-color: #f1f1f0;\n  --scroll-thumb-color: var(--scrollbar-thumb-color);\n  --scroll-track-color: var(--scrollbar-track-color);\n  --search-modal-bg-color: white;\n  --search-modal-color: #494949;\n  --search-modal-hover-color: var(--sidebar-hover-color);\n  --badge-color: #525252;\n  --badge-bg-color: #e8e8e8;\n  --view-bg-color: #ececec;\n  --view-btn-bg: #f3f3f3;\n  --view-btn-text: #383838;\n  --view-btn-bg-active: #fff;\n  --view-btn-text-active: #111827;\n  font-family: \"InterVariable\", \"Inter\", \"-apple-system\", \"BlinkMacSystemFont\", \"Segoe UI\", \"Roboto\", \"Oxygen\", \"Ubuntu\", \"Cantarell\", \"Fira Sans\", \"Droid Sans\", \"Helvetica Neue\", sans-serif;\n  background-color: var(--background-color);\n  color: var(--text-color);\n  height: auto;\n}\n\nbody.dark {\n  --primary: var(--gray-100);\n  --background-color: #030712;\n  --text-color: var(--gray-50);\n  --sidebar-bg-color: var(--background-color); /* #111111; */\n  --sidebar-hover-color: #242a30;\n  --admin-banner-bg: #242a30;\n  --admin-banner-text: #fff;\n  --admin-banner-btn-bg: #f8f8f8;\n  --admin-banner-btn-active-bg: #e2e2e2;\n  --admin-banner-btn-text: #242a30;\n  --admin-banner-btn-hover-bg: #e2e2e2;\n  --btn-primary-color: #000;\n  --btn-primary-bg-color: #fff;\n  --btn-primary-hover-bg-color: #d4d4d4;\n  --btn-primary-active-bg-color: #afafaf;\n  --btn-secondary-color: #d4d4d4;\n  --btn-secondary-bg-color: #333c44;\n  --btn-secondary-hover-bg-color: #313a41;\n  --btn-secondary-active-bg-color: #2b3339;\n  --active-item-color: var(--gray-700);\n  --searchbar-color: #242a30;\n  --border-color: var(--gray-700);\n  --bqoute-border-color: #47474d;\n  --bqoute-bg-color: var(--gray-900);\n  --code-bg-color: var(--purple-900);\n  --code-text-color: #ad63ef;\n  --input-bg-color: #242a30;\n  --sidebar-text-color: var(--gray-400);\n  --sidebar-hover-color: #242a30;\n  --sidebar-active-item-color: var(--gray-700);\n  --bg-orange: var(--orange-600);\n  --text-on-orange: var(--orange-50);\n  --bg-green: var(--green-700);\n  --text-on-green: var(--green-50);\n  --htmldiff-ins-color: #006400;\n  --htmldiff-del-color: #8b0000;\n  --diff-ins-bg-color: #006400;\n  --diff-ins-text-color: var(--text-color);\n  --diff-del-bg-color: #8b0000;\n  --diff-del-text-color: var(--text-color);\n  --editor-button-text-color: var(--gray-400);\n  --editor-hover-button-color: var(--gray-700);\n  --subtle-fg: var(--btn-secondary-bg-color);\n  --navbar-dropdown-bg-color: #32393f;\n  --navbar-dropdown-hover-color: #262c30;\n  --toc-hover-text-color: #fff;\n  --editor-line-no-bg-color: var(--gray-800);\n  --control-bg-on-gray: var(--gray-700);\n  --editor-background-color: #333c44;\n  --button-background-color: #383838;\n  --editor-toolbar-btn-color: var(--gray-100);\n  --editor-toolbar-btn-bg-color: var(--editor-background-color);\n  --editor-text-selection-color: #606060;\n  --content-text-color: #d1d5dc;\n  --table-border-color: #434343;\n  --table-header-color: #181f24;\n  --scroll-thumb-color: #949494;\n  --scroll-track-color: #3f3f3f;\n  --sidebar-font-color: #d1d5dc;\n  --search-modal-bg-color: rgb(35, 35, 35);\n  --search-modal-color: rgb(212, 212, 212);\n  --search-modal-hover-color: #343434;\n  --badge-color: #cdcdcd;\n  --badge-bg-color: #484848;\n  --view-bg-color: #272727;\n  --view-btn-bg: #333c44;\n  --view-btn-text: #d4d4d4;\n  --view-btn-bg-active: #3d3d3d;\n  --view-btn-text-active: #d4d4d4;\n}\nbody.dark .draft-wiki-page {\n  background: var(--gray-700);\n  color: var(--gray-50);\n}\nbody.dark .navbar .navbar-toggler-icon {\n  background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.5%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e\");\n}\nbody.dark .alert-message {\n  color: var(--gray-800) !important;\n}\n\n* {\n  scrollbar-width: thin;\n  scrollbar-color: var(--scroll-thumb-color) var(--scroll-track-color);\n}\n\n.admin-banner {\n  background-color: var(--admin-banner-bg) !important;\n  color: var(--admin-banner-text) !important;\n  font-size: 14px;\n  margin-top: 22px !important;\n  margin-bottom: 0;\n  padding: 0.5rem 0.7rem;\n}\n.admin-banner strong {\n  color: inherit;\n}\n.admin-banner .btn-warning {\n  background-color: var(--admin-banner-btn-bg);\n  color: var(--admin-banner-btn-text) !important;\n}\n.admin-banner .btn-warning:hover {\n  background-color: var(--admin-banner-btn-hover-bg);\n}\n.admin-banner .btn-warning:active {\n  background-color: var(--admin-banner-btn-active-bg) !important;\n}\n\n.wiki-page-meta {\n  font-size: 0.875rem;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin: auto;\n  width: 100%;\n  max-width: 650px;\n}\n\n.drafts {\n  border: 1px solid var(--searchbar-color) !important;\n  border-radius: 0.5rem;\n}\n.drafts .table th,\n.drafts .table td {\n  border-top: 1px solid var(--table-border-color);\n}\n\n.dropdown-menu {\n  background-color: var(--editor-background-color);\n  border-color: var(--editor-background-color);\n}\n.dropdown-menu .dropdown-item {\n  color: var(--text-color);\n}\n.dropdown-menu .dropdown-item:active, .dropdown-menu .dropdown-item:hover {\n  background-color: var(--sidebar-hover-color);\n  color: var(--text-color);\n}\n\n.nav-item .dropdown-menu.show {\n  top: 40px !important;\n}\n\n.main-column {\n  background-color: var(--background-color);\n  padding: 0px 50px;\n  width: 100%;\n}\n@media (max-width: 991.98px) {\n  .main-column {\n    padding: 20px;\n    width: 100vw;\n  }\n}\n\n.wiki-editor .wiki-title-container {\n  margin-bottom: 1rem;\n}\n.wiki-editor .wiki-title-input {\n  color: var(--text-color);\n  background-color: var(--editor-background-color);\n}\n\n.feather-link {\n  visibility: hidden;\n}\n\n.user-contributions {\n  margin: 0 0 0 auto;\n  color: var(--sidebar-text-color);\n  font-size: 0.8rem;\n}\n\n.form-control:focus {\n  color: var(--text-color);\n  border: 1px solid var(--gray-500);\n}\n\n.contributions-header {\n  margin: 2rem 0 1.5rem 0;\n  font-size: 1.25rem;\n  font-weight: 700;\n  line-height: 16px;\n}\n\n.count {\n  font-size: var(--text-xs);\n  background-color: var(--gray-700);\n  border-radius: var(--border-radius-sm);\n  color: var(--gray-50);\n  padding: 0 var(--padding-xs);\n  float: right;\n  margin-top: 2px;\n}\n\nimg::after {\n  content: \"\";\n}\n\nimg[alt]::after {\n  width: unset;\n  height: unset;\n}\n\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n  color: var(--text-color) !important;\n  font-weight: 600 !important;\n}\n\nstrong {\n  font-weight: 800 !important;\n}\n\nh1 {\n  margin-top: 3rem;\n  margin-bottom: 0.75rem;\n  font-size: 30px;\n  font-weight: 600;\n  line-height: 28px;\n  letter-spacing: 0.005em;\n}\n@media (min-width: 576px) {\n  h1 {\n    margin-top: 1.5rem !important;\n    margin-bottom: 0 !important;\n  }\n}\n\nh2 {\n  font-size: 22px;\n  margin-top: 2rem;\n  margin-bottom: 0.5rem;\n}\n@media (min-width: 576px) {\n  h2 {\n    margin-top: 4rem;\n    margin-bottom: 0.75rem;\n  }\n}\n\nh3 {\n  font-size: 20px;\n  margin-top: 2rem;\n  margin-bottom: 0.5rem;\n}\n@media (min-width: 576px) {\n  h3 {\n    margin-top: 2.5rem;\n  }\n}\n\nh4 {\n  font-size: 18px;\n  margin-top: 2rem;\n  margin-bottom: 0.5rem;\n}\n@media (min-width: 576px) {\n  h4 {\n    margin-top: 2.5rem;\n  }\n}\nh4 a {\n  color: #525252;\n}\n\nh5 {\n  font-size: 16px;\n}\n\nh6 {\n  font-size: 16px;\n}\n\np {\n  font-size: 1rem;\n}\n\n.text-sm {\n  font-size: 13px !important;\n  line-height: 1.15;\n  letter-spacing: 0.02em;\n}\n\n.text-xs {\n  font-size: 12px;\n  line-height: 1.15;\n  letter-spacing: 0.02em;\n  font-weight: 400;\n}\n\n.wiki-space-route-block {\n  padding: 0.4rem 0.5rem;\n  border-radius: 0.375rem;\n  background-color: var(--bqoute-border-color);\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0;\n}\n\n.wiki-editor,\n.wiki-content {\n  font-size: 1rem;\n  line-height: 1.7;\n  max-width: calc(100vw - 6rem);\n}\n.wiki-editor a:not(.dropdown-item, .btn),\n.wiki-content a:not(.dropdown-item, .btn) {\n  color: var(--text-color);\n  text-decoration: underline;\n}\n.wiki-editor ul,\n.wiki-editor ol,\n.wiki-content ul,\n.wiki-content ol {\n  padding-left: 2rem;\n}\n.wiki-editor ul,\n.wiki-content ul {\n  list-style-type: disc;\n}\n.wiki-editor ol,\n.wiki-content ol {\n  list-style: decimal;\n}\n.wiki-editor li,\n.wiki-content li {\n  padding-top: 1px;\n  padding-bottom: 1px;\n}\n.wiki-editor li::marker,\n.wiki-content li::marker {\n  color: var(--editor-button-text-color);\n}\n.wiki-editor li p,\n.wiki-content li p {\n  margin-bottom: 0;\n}\n.wiki-editor li > ul,\n.wiki-editor li > ol,\n.wiki-content li > ul,\n.wiki-content li > ol {\n  padding-left: 1.5rem;\n}\n.wiki-editor ul > li:first-child,\n.wiki-content ul > li:first-child {\n  margin-top: 3px !important;\n}\n.wiki-editor ul > * + *,\n.wiki-editor ol > * + *,\n.wiki-content ul > * + *,\n.wiki-content ol > * + * {\n  margin-top: 2px !important;\n}\n.wiki-editor blockquote,\n.wiki-content blockquote {\n  padding: 0.75rem 1rem 0.75rem 1.25rem;\n  font-size: 0.875rem;\n  font-weight: 500;\n  border: 1px solid var(--bqoute-border-color);\n  border-left: 5px solid var(--bqoute-border-color);\n  border-radius: 0.5rem;\n  margin: 1.5rem 0 !important;\n  background-color: var(--bqoute-bg-color);\n}\n.wiki-editor blockquote p:last-child,\n.wiki-content blockquote p:last-child {\n  margin-bottom: 0 !important;\n}\n.wiki-editor b,\n.wiki-editor strong,\n.wiki-content b,\n.wiki-content strong {\n  color: var(--content-text-color);\n  font-weight: 600;\n}\n.wiki-editor h1,\n.wiki-editor h2,\n.wiki-editor h3,\n.wiki-editor h4,\n.wiki-editor h5,\n.wiki-editor h6,\n.wiki-content h1,\n.wiki-content h2,\n.wiki-content h3,\n.wiki-content h4,\n.wiki-content h5,\n.wiki-content h6 {\n  color: #171717;\n}\n.wiki-editor h2,\n.wiki-editor h3,\n.wiki-editor h4,\n.wiki-editor h5,\n.wiki-editor h6,\n.wiki-content h2,\n.wiki-content h3,\n.wiki-content h4,\n.wiki-content h5,\n.wiki-content h6 {\n  font-weight: 600;\n}\n.wiki-editor h1,\n.wiki-content h1 {\n  font-size: 2rem;\n  line-height: 1.5;\n  font-weight: 600;\n}\n.wiki-editor h1 + p,\n.wiki-content h1 + p {\n  margin-top: 1rem !important;\n  line-height: 1.4;\n}\n.wiki-editor > p,\n.wiki-content > p {\n  margin-top: 1rem !important;\n}\n.wiki-editor h2,\n.wiki-content h2 {\n  font-size: 1.5rem;\n  line-height: 1.56;\n  margin: 3rem 0 1rem !important;\n}\n@media (min-width: 768px) {\n  .wiki-editor h2,\n  .wiki-content h2 {\n    font-size: 1.6rem;\n  }\n}\n.wiki-editor h3,\n.wiki-content h3 {\n  font-size: 1.25rem;\n  line-height: 1.56;\n  margin: 2rem 0 1rem !important;\n}\n@media (min-width: 768px) {\n  .wiki-editor h3,\n  .wiki-content h3 {\n    font-size: 1.41rem;\n  }\n}\n.wiki-editor h4,\n.wiki-content h4 {\n  font-size: 1.125rem;\n  line-height: 1.56;\n  margin-top: 1.25rem !important;\n}\n.wiki-editor h5:not(.modal-title),\n.wiki-content h5:not(.modal-title) {\n  font-size: 1rem;\n  line-height: 1.5;\n  font-weight: 600;\n  margin-top: 1rem !important;\n}\n.wiki-editor h6,\n.wiki-content h6 {\n  font-size: 0.75rem;\n  line-height: 1.35;\n  font-weight: 600;\n  text-transform: uppercase;\n  margin-top: 1rem !important;\n}\n.wiki-editor tr > td,\n.wiki-editor tr > th,\n.wiki-content tr > td,\n.wiki-content tr > th {\n  font-size: 0.875rem;\n  padding: 0.5rem;\n}\n.wiki-editor th:empty,\n.wiki-content th:empty {\n  display: none;\n}\n.wiki-editor .screenshot,\n.wiki-content .screenshot {\n  border: 1px solid #c7c7c7;\n  border-radius: 0.375rem;\n  margin-top: 0.5rem !important;\n}\n.wiki-editor .screenshot + em,\n.wiki-content .screenshot + em {\n  text-align: center;\n  display: block;\n  margin-top: 0.5rem !important;\n  margin-bottom: 2rem !important;\n}\n.wiki-editor p > code:not(.hljs),\n.wiki-content p > code:not(.hljs) {\n  padding: 0 0.25rem;\n  background-color: var(--code-bg-color);\n  color: var(--code-text-color);\n  border-radius: 0.125rem;\n}\n.wiki-editor pre > code:not(.hljs),\n.wiki-content pre > code:not(.hljs) {\n  color: #fff;\n}\n.wiki-editor table,\n.wiki-content table {\n  border-color: #ededed;\n  border-collapse: collapse;\n  table-layout: fixed;\n  width: 100%;\n  margin: 0;\n  overflow: hidden;\n}\n.wiki-editor table td,\n.wiki-editor table th,\n.wiki-content table td,\n.wiki-content table th {\n  min-width: 1em;\n  padding: 3px 5px;\n  vertical-align: top;\n  box-sizing: border-box;\n  position: relative;\n}\n.wiki-editor table td > *,\n.wiki-editor table th > *,\n.wiki-content table td > *,\n.wiki-content table th > * {\n  margin-bottom: 0;\n}\n.wiki-editor table th,\n.wiki-content table th {\n  font-weight: bold;\n  text-align: left;\n  background-color: var(--table-header-color);\n}\n.wiki-editor table .selectedCell:after,\n.wiki-content table .selectedCell:after {\n  z-index: 2;\n  position: absolute;\n  content: \"\";\n  left: 0;\n  right: 0;\n  top: 0;\n  bottom: 0;\n  background: rgba(200, 200, 255, 0.4);\n  pointer-events: none;\n}\n.wiki-editor table .column-resize-handle,\n.wiki-content table .column-resize-handle {\n  position: absolute;\n  right: -2px;\n  top: 0;\n  bottom: -2px;\n  width: 4px;\n  background-color: #adf;\n  pointer-events: none;\n}\n.wiki-editor table p,\n.wiki-content table p {\n  margin: 0;\n}\n.wiki-editor .table-bordered,\n.wiki-editor .table-bordered th,\n.wiki-editor .table-bordered td,\n.wiki-content .table-bordered,\n.wiki-content .table-bordered th,\n.wiki-content .table-bordered td {\n  border: 1px solid;\n  border-color: var(--table-border-color);\n  color: inherit;\n}\n.wiki-editor .table-bordered thead th,\n.wiki-editor .table-bordered thead td,\n.wiki-content .table-bordered thead th,\n.wiki-content .table-bordered thead td {\n  border-bottom-width: 1px;\n}\n.wiki-editor pre,\n.wiki-content pre {\n  overflow: hidden;\n  position: relative;\n}\n.wiki-editor pre:hover .copy-btn,\n.wiki-content pre:hover .copy-btn {\n  visibility: visible;\n}\n.wiki-editor pre .copy-btn,\n.wiki-content pre .copy-btn {\n  visibility: hidden;\n  right: 0.5rem;\n  padding: 0.75rem;\n  position: absolute;\n  background-color: inherit;\n}\n.wiki-editor pre .copy-btn:hover,\n.wiki-content pre .copy-btn:hover {\n  color: var(--gray-500);\n  background-color: inherit;\n  border-color: inherit;\n}\n.wiki-editor pre .copy-btn:focus,\n.wiki-content pre .copy-btn:focus {\n  box-shadow: none;\n}\n.wiki-editor hr,\n.wiki-content hr {\n  border-top: 1px solid #ededed;\n}\n\n.wiki-editor {\n  margin-left: -2rem;\n  width: 800px;\n}\n.wiki-editor .ace_print-margin {\n  visibility: hidden !important;\n}\n@media (max-width: 991.98px) {\n  .wiki-editor {\n    margin: 0;\n    width: 100%;\n    max-width: none;\n  }\n}\n.wiki-editor pre {\n  padding: 0.75rem 1rem;\n  background-color: #011627;\n  border-radius: 0.5rem;\n}\n.wiki-editor h1 {\n  font-size: 2rem;\n  line-height: 1.5;\n  font-weight: 600;\n}\n.wiki-editor h1 + p {\n  margin-top: 1rem !important;\n  line-height: 1.4;\n}\n.wiki-editor > p {\n  margin-top: 1rem !important;\n}\n.wiki-editor h2 {\n  font-size: 1.5rem;\n  line-height: 1.56;\n  margin: 3rem 0 1rem !important;\n}\n@media (min-width: 768px) {\n  .wiki-editor h2 {\n    font-size: 1.6rem;\n  }\n}\n.wiki-editor h3 {\n  font-size: 1.25rem;\n  line-height: 1.56;\n  margin: 2rem 0 1rem !important;\n}\n@media (min-width: 768px) {\n  .wiki-editor h3 {\n    font-size: 1.41rem;\n  }\n}\n.wiki-editor h4 {\n  font-size: 1.125rem;\n  line-height: 1.56;\n  margin-top: 1.25rem !important;\n}\n.wiki-editor h5:not(.modal-title) {\n  font-size: 1rem;\n  line-height: 1.5;\n  font-weight: 600;\n  margin-top: 1rem !important;\n}\n.wiki-editor h6 {\n  font-size: 0.75rem;\n  line-height: 1.35;\n  font-weight: 600;\n  text-transform: uppercase;\n  margin-top: 1rem !important;\n}\n.wiki-editor p {\n  font-size: 15px;\n  font-weight: 420;\n  line-height: 155%;\n  letter-spacing: 0.02em;\n  text-align: left;\n}\n.wiki-editor button:not(.btn-primary) {\n  all: unset;\n  cursor: pointer;\n  background: var(--editor-background-color);\n  border-radius: 0.5rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: -moz-fit-content;\n  width: fit-content;\n}\n.wiki-editor .btn-primary {\n  color: var(--btn-primary-color) !important;\n  background-color: var(--btn-primary-bg-color) !important;\n}\n.wiki-editor .btn-primary:hover {\n  background-color: var(--btn-primary-hover-bg-color) !important;\n}\n.wiki-editor .btn-primary:active {\n  color: var(--btn-primary-color) !important;\n  background-color: var(--btn-primary-active-bg-color) !important;\n}\n.wiki-editor .btn-secondary {\n  color: var(--btn-secondary-color) !important;\n  background-color: var(--btn-secondary-bg-color) !important;\n}\n.wiki-editor .btn-secondary:hover {\n  background-color: var(--btn-secondary-hover-bg-color) !important;\n}\n.wiki-editor .btn-secondary:active {\n  background-color: var(--btn-secondary-active-bg-color) !important;\n}\n.wiki-editor a.btn-primary-light {\n  background-color: var(--btn-secondary-color) !important;\n}\n.wiki-editor button:hover {\n  background-color: var(--editor-hover-button-color);\n}\n.wiki-editor .is-active {\n  background-color: var(--editor-hover-button-color) !important;\n}\n.wiki-editor .dropdown {\n  display: inline-block;\n}\n.wiki-editor .dropdown #dropdownMenuButton::after {\n  display: none;\n}\n.wiki-editor .dropdown-menu.show {\n  transform: translate3d(1px, 28px, 0px) !important;\n}\n.wiki-editor .ace_editor {\n  height: 70vh;\n  padding: 0 1rem;\n  padding-right: 120px;\n  border-radius: 0.5rem;\n  background-color: var(--editor-background-color);\n}\n@media (max-width: 991.98px) {\n  .wiki-editor .ace_editor {\n    padding-right: 1rem;\n    width: 92vw;\n  }\n}\n.wiki-editor .ace_editor .is-empty::before {\n  content: attr(data-placeholder);\n  float: left;\n  color: #ced4da;\n  pointer-events: none;\n  height: 0;\n}\n.wiki-editor .ace_editor:focus-visible {\n  outline: none;\n}\n.wiki-editor .ace_editor .ace_gutter {\n  z-index: auto;\n  background: var(--editor-background-color);\n  color: var(--text-light);\n}\n.wiki-editor img.ProseMirror-selectednode {\n  border: 2px solid #7cbcf5;\n}\n.wiki-editor > :last-child {\n  overflow-x: auto;\n  margin-top: 0 !important;\n  border-radius: 0.5rem;\n}\n.wiki-editor > :first-child {\n  padding-bottom: 0.5rem;\n  display: flex;\n  flex-wrap: wrap;\n  flex-direction: row;\n  align-items: center;\n  justify-content: space-between;\n  width: 800px;\n}\n@media (max-width: 991.98px) {\n  .wiki-editor > :first-child {\n    width: 100%;\n    flex-wrap: nowrap;\n    align-items: flex-start;\n  }\n}\n.wiki-editor > :first-child .wiki-edit-controls {\n  flex-wrap: wrap;\n  display: inline-flex;\n  row-gap: 0.5rem;\n}\n.wiki-editor > :first-child .wiki-edit-control-btn {\n  margin-left: auto;\n  display: inline-flex;\n}\n.wiki-editor > :first-child .wiki-edit-control-btn > * {\n  margin: 0.5rem 0.25rem;\n}\n@media (max-width: 991.98px) {\n  .wiki-editor > :first-child .wiki-edit-control-btn {\n    margin-left: 0;\n    align-items: flex-start;\n  }\n}\n\n.wiki-content {\n  min-height: 55vh;\n}\n@media (max-width: 991.98px) {\n  .wiki-content {\n    max-width: calc(100vw - 3rem);\n  }\n}\n.wiki-content div > p:first-child {\n  margin-top: 1.5rem !important;\n  line-height: 1.4;\n}\n.wiki-content code {\n  padding: 0.75rem 1rem;\n}\n.wiki-content ul[data-type=taskList] p {\n  margin: 0 !important;\n}\n\n.btn:hover {\n  color: #000;\n  background-color: #f9fafb;\n  border-color: #f9fafb;\n}\n\n.btn.btn-primary:hover {\n  color: #fff;\n  background-color: var(--button-background-color);\n  border-color: #383838;\n}\n\nul[data-type=taskList] {\n  list-style: none;\n  padding: 0;\n}\nul[data-type=taskList] p {\n  margin: 0;\n}\nul[data-type=taskList] li {\n  display: flex;\n}\nul[data-type=taskList] li > label {\n  flex: 0 0 auto;\n  margin-right: 0.25rem;\n  margin-bottom: 0;\n  -webkit-user-select: none;\n     -moz-user-select: none;\n          user-select: none;\n}\nul[data-type=taskList] li > div {\n  flex: 1 1 auto;\n}\nul[data-type=taskList] li ul li,\nul[data-type=taskList] li ol li {\n  display: list-item;\n}\nul[data-type=taskList] li ul[data-type=taskList] > li {\n  display: flex;\n}\nul[data-type=taskList] li input {\n  color: #000;\n  margin: 0.1rem;\n  margin-top: 0.4rem;\n  border: 1px solid var(--gray-500);\n  accent-color: black;\n}\nul[data-type=taskList] li input:checked {\n  background-color: var(--primary);\n  background-image: url(\"data:image/svg+xml, <svg viewBox='0 0 8 7' fill='none' xmlns='http://www.w3.org/2000/svg'><path d='M1 4.00001L2.66667 5.80001L7 1.20001' stroke='white' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/></svg>\"), var(--checkbox-gradient);\n  background-size: 57%, 100%;\n  box-shadow: none;\n  border: none;\n}\n\n.file-upload-area {\n  background: var(--btn-secondary-bg-color) !important;\n}\n\n.file-upload-area div button {\n  color: var(--btn-secondary-color) !important;\n}\n\n.ace_editor .ace_marker-layer .ace_selection {\n  background: var(--editor-text-selection-color) !important;\n}\n\n.toolbar {\n  padding-bottom: 8px;\n  display: flex;\n  gap: 6px;\n}\n\n.toolbar button {\n  width: 26px !important;\n  height: 26px !important;\n  background: var(--editor-toolbar-btn-bg-color) !important;\n  border: 1px solid #ccc;\n  border-radius: 4px !important;\n  color: var(--editor-toolbar-btn-color) !important;\n  cursor: pointer;\n  padding: 2px !important;\n}\n.toolbar button svg {\n  width: 18px;\n  height: 18px;\n}\n\n.toolbar i {\n  font-size: 14px;\n}\n\n.no-revision {\n  text-align: center;\n  margin: 10px !important;\n  font-size: 24px;\n}\n\n.forward-back a p {\n  text-overflow: ellipsis;\n  overflow: hidden;\n  white-space: nowrap;\n}\n\n.btn.btn-secondary {\n  color: var(--btn-secondary-color) !important;\n  background-color: var(--btn-secondary-bg-color) !important;\n}\n\n.btn.btn-secondary:hover {\n  background-color: var(--btn-secondary-hover-bg-color) !important;\n}\n\n.btn.btn-secondary:active {\n  background-color: var(--btn-secondary-active-bg-color) !important;\n}\n\n.feedback-btn {\n  cursor: pointer;\n}\n.feedback-btn:hover {\n  text-decoration: underline;\n}\n\n.form-control {\n  background-color: white;\n  border: 1px solid var(--background-color);\n}\n\n@keyframes pulse {\n  0% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.5;\n  }\n  100% {\n    opacity: 1;\n  }\n}\n.pulse {\n  animation: pulse 1.5s infinite;\n}\n\n.markdown {\n  font-size: 14px;\n  line-height: 2 !important;\n  color: var(--content-text-color);\n  width: 100%;\n  max-width: 650px;\n}\n.markdown a:not(.dropdown-item, .btn) {\n  color: var(--text-color);\n  text-decoration: underline;\n  text-underline-offset: 6px;\n}\n.markdown p {\n  font-size: 14px;\n  margin-top: 1.25em;\n  margin-bottom: 1.25em;\n}\n.markdown [class~=lead] {\n  font-size: 1.25em;\n  line-height: 1.6;\n  margin-top: 1.2em;\n  margin-bottom: 1.2em;\n}\n.markdown blockquote {\n  padding: 0.75rem 1rem 0.75rem 1.25rem;\n  font-size: 0.875rem;\n  font-weight: 500;\n  border: 1px solid var(--bqoute-border-color);\n  border-left: 5px solid var(--bqoute-border-color);\n  border-radius: 0.5rem;\n  margin: 1.5rem 0 !important;\n  background-color: var(--bqoute-bg-color);\n}\n.markdown blockquote p:last-child {\n  margin-top: 0 !important;\n  margin-bottom: 0 !important;\n}\n.markdown h1 {\n  font-size: 22px;\n  margin-top: 0;\n  margin-bottom: 0.8888889em;\n  line-height: 1.1111111;\n}\n.markdown h2 {\n  font-size: 20px;\n  margin-top: 1.5em;\n  margin-bottom: 0.5em;\n  line-height: 1.3333333;\n}\n.markdown h3 {\n  font-size: 18px;\n  margin-top: 1.2em;\n  margin-bottom: 0.4em;\n  line-height: 1.6;\n}\n.markdown h4 {\n  margin-top: 16px;\n  margin-bottom: 0.5em;\n  line-height: 1.5;\n}\n.markdown h5 {\n  margin-top: 16px;\n  margin-bottom: 0.5em;\n  line-height: 1.5;\n}\n.markdown h6 {\n  margin-top: 16px;\n  margin-bottom: 0.5em;\n  line-height: 1.5;\n}\n.markdown img {\n  margin-top: 2em;\n  margin-bottom: 2em;\n}\n.markdown picture {\n  margin-top: 2em;\n  margin-bottom: 2em;\n}\n.markdown picture > img {\n  margin-top: 0;\n  margin-bottom: 0;\n}\n.markdown video {\n  margin-top: 2em;\n  margin-bottom: 2em;\n}\n.markdown kbd {\n  font-size: 0.875em;\n  border-radius: 0.3125rem;\n  padding-top: 0.1875em;\n  padding-inline-end: 0.375em;\n  padding-bottom: 0.1875em;\n  padding-inline-start: 0.375em;\n}\n.markdown code {\n  font-size: 0.875em;\n}\n.markdown h2 code {\n  font-size: 0.875em;\n}\n.markdown h3 code {\n  font-size: 0.9em;\n}\n.markdown pre {\n  font-size: 0.875em;\n  line-height: 1.7142857;\n  margin-top: 1.7142857em;\n  margin-bottom: 1.7142857em;\n  border-radius: 0.375rem;\n}\n.markdown ol {\n  margin-top: 1.25em;\n  margin-bottom: 1.25em;\n  padding-inline-start: 1.625em;\n}\n.markdown ul {\n  margin-top: 1.25em;\n  margin-bottom: 1.25em;\n  padding-inline-start: 1.625em;\n}\n.markdown li {\n  font-size: 14px;\n  margin-top: 0.25em;\n  margin-bottom: 0.25em;\n}\n.markdown li::marker {\n  color: var(--editor-button-text-color);\n}\n.markdown ol > li {\n  padding-inline-start: 0.375em;\n}\n.markdown ul > li {\n  padding-inline-start: 0.375em;\n}\n.markdown > ul > li p {\n  margin-top: 0.75em;\n  margin-bottom: 0.75em;\n}\n.markdown > ul > li > p:first-child {\n  margin-top: 1.25em;\n}\n.markdown > ul > li > p:last-child {\n  margin-bottom: 1.25em;\n}\n.markdown > ol > li > p:first-child {\n  margin-top: 1.25em;\n}\n.markdown > ol > li > p:last-child {\n  margin-bottom: 1.25em;\n}\n.markdown ul ul,\n.markdown ul ol,\n.markdown ol ul,\n.markdown ol ol {\n  margin-top: 0.75em;\n  margin-bottom: 0.75em;\n}\n.markdown dl {\n  margin-top: 1.25em;\n  margin-bottom: 1.25em;\n}\n.markdown dt {\n  margin-top: 1.25em;\n}\n.markdown dd {\n  margin-top: 0.5em;\n  padding-inline-start: 1.625em;\n}\n.markdown hr {\n  margin-top: 1em;\n  margin-bottom: 1em;\n  background-color: var(--border-color);\n}\n.markdown hr + * {\n  margin-top: 0;\n}\n.markdown h2 + * {\n  margin-top: 0;\n}\n.markdown h3 + * {\n  margin-top: 0;\n}\n.markdown h4 + * {\n  margin-top: 0;\n}\n.markdown table {\n  font-size: 0.875em;\n  line-height: 1.7142857;\n}\n.markdown thead th {\n  padding-inline-end: 0.5714286em;\n  padding-bottom: 0.5714286em;\n  padding-inline-start: 0.5714286em;\n}\n.markdown thead th:last-child {\n  padding-inline-end: 0;\n}\n.markdown tbody td,\n.markdown tfoot td {\n  padding-top: 0.5714286em;\n  padding-inline-end: 0.5714286em;\n  padding-bottom: 0.5714286em;\n  padding-inline-start: 0.5714286em;\n}\n.markdown code:not(.hljs) {\n  padding: 0 0.25rem;\n  background: rgba(0, 0, 0, 0) !important;\n  border-radius: 0.125rem;\n  color: var(--code-text-color) !important;\n  font-weight: 550;\n}\n.markdown code:not(.hljs):before,\n.markdown code:not(.hljs):after {\n  content: \"`\";\n  display: inline;\n}\n.markdown tbody td:last-child,\n.markdown tfoot td:last-child {\n  padding-inline-end: 0;\n}\n.markdown figure {\n  margin-top: 2em;\n  margin-bottom: 2em;\n}\n.markdown figure > * {\n  margin-top: 0;\n  margin-bottom: 0;\n}\n.markdown figcaption {\n  font-size: 0.875em;\n  line-height: 1.4285714;\n  margin-top: 0.8571429em;\n}\n\n.wiki-content .from-markdown > :first-child {\n  margin-top: 0;\n}\n\n.wiki-page-content {\n  margin: 0.5rem auto;\n  width: 100%;\n}\n@media (max-width: 991.98px) {\n  .wiki-page-content {\n    width: auto;\n    margin: unset;\n    overflow-x: hidden;\n  }\n}\n\n.wiki-page-content .from-markdown h1::before,\n.wiki-page-content .from-markdown h2::before,\n.wiki-page-content .from-markdown h3::before,\n.wiki-page-content .from-markdown h4::before,\n.wiki-page-content .from-markdown h5::before,\n.wiki-page-content .from-markdown h6::before {\n  height: 0;\n  margin-top: 0;\n}\n\n.hljs {\n  overflow: auto;\n}\n\nh2:hover .feather-link,\nh3:hover .feather-link,\nh4:hover .feather-link,\nh5:hover .feather-link,\nh6:hover .feather-link {\n  visibility: visible;\n}\n\n.wiki-editor,\n.from-markdown {\n  margin: auto;\n}\n\n.markdown-preview {\n  overflow: auto;\n  padding: 0 !important;\n}\n\n.navbar-brand {\n  padding: 0;\n  color: var(--text-color) !important;\n}\n.navbar-brand img {\n  height: 20px;\n  max-width: -moz-fit-content;\n  max-width: fit-content;\n}\n@media (max-width: 991.98px) {\n  .navbar-brand {\n    border-bottom: unset;\n  }\n}\n\n.navbar-brand-container {\n  width: 17rem;\n  display: flex;\n  align-items: center;\n  padding: 10px 18px;\n  background-color: var(--sidebar-bg-color);\n  position: sticky;\n  top: 0;\n  z-index: 5;\n  height: 60px;\n  position: relative;\n}\n@media (max-width: 768px) {\n  .navbar-brand-container {\n    width: 12rem;\n  }\n}\n@media (max-width: 991.98px) {\n  .navbar-brand-container {\n    max-width: 14rem;\n    background-color: var(--background-color);\n  }\n}\n\n.navbar-nav {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: flex-end;\n  background-color: var(--background-color);\n  padding: 0 14px;\n  border-left: 1px solid var(--border-color);\n}\n@media (max-width: 767.98px) {\n  .navbar-nav {\n    max-width: 100vw;\n    height: auto;\n    align-items: flex-start;\n  }\n}\n.navbar-nav .search-item {\n  margin-right: auto;\n  height: auto !important;\n}\n.navbar-nav .dropdown-menu {\n  position: sticky;\n  border: 1px solid var(--border-color);\n}\n.navbar-nav .dropdown-menu .dropdown-item {\n  color: var(--text-color);\n}\n.navbar-nav .dropdown-menu .dropdown-item:focus-visible {\n  outline: none;\n}\n.navbar-nav .dropdown-menu .dropdown-item:hover {\n  background-color: var(--sidebar-hover-color);\n}\n.navbar-nav .dropdown-menu .dropdown-item:hover .h6 {\n  color: var(--background-color) !important;\n}\n\n.nav-item {\n  margin-left: 1rem;\n  display: flex;\n  align-items: center;\n}\n.nav-item #search-container {\n  padding-right: 0px;\n  padding-left: 0px;\n}\n.nav-item #search-container .dropdown {\n  height: 32px;\n  width: 240px;\n  background-color: var(--searchbar-color);\n}\n.nav-item #search-container .dropdown:hover {\n  border-color: var(--primary);\n}\n.nav-item #search-container .dropdown kbd {\n  position: absolute;\n  top: 7px;\n  right: 5px;\n  padding: 0.1rem 0.4rem;\n  color: var(--sidebar-text-color);\n  background-color: transparent;\n}\n.nav-item #search-container .dropdown span {\n  margin-left: 2rem;\n  margin-right: 3rem;\n}\n.nav-item select {\n  height: 100%;\n}\n\n.wiki-navbar {\n  padding: 0px !important;\n  border-bottom: 1px solid var(--border-color);\n  background-color: var(--background-color);\n}\n@media (max-width: 991.98px) {\n  .wiki-navbar {\n    width: auto;\n    padding-left: 2rem;\n  }\n}\n.wiki-navbar .wiki-navbar-container {\n  padding-right: 1rem;\n  align-items: center;\n  background-color: var(--background-color);\n}\n@media (max-width: 991.98px) {\n  .wiki-navbar .wiki-navbar-container {\n    box-shadow: unset;\n    margin-left: 0;\n  }\n  .wiki-navbar .wiki-navbar-container .navbar-nav {\n    padding-left: 10px !important;\n    max-width: 100vw;\n  }\n}\n.wiki-navbar .doc-container .navbar-collapse {\n  padding-top: 2rem;\n  background-color: var(--background-color);\n  margin-left: 2rem;\n  padding-bottom: 1rem;\n}\n@media (max-width: 991.98px) {\n  .wiki-navbar .doc-container .navbar-collapse {\n    padding-top: 0;\n    margin: 0;\n  }\n}\n.wiki-navbar .container {\n  height: 36px;\n}\n.wiki-navbar .sun-moon-container {\n  cursor: pointer;\n  margin-left: 24px;\n  display: flex;\n  align-items: center;\n}\n.wiki-navbar .sun-moon-container svg {\n  width: 16px !important;\n}\n@media (max-width: 991.98px) {\n  .wiki-navbar .sun-moon-container {\n    margin-left: 0px;\n  }\n}\n.wiki-navbar .mobile-search-icon {\n  margin: 0 1rem 0 auto;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n}\n\n.navbar .navbar-expand-lg {\n  width: 100%;\n  position: fixed;\n  top: 0;\n  /*ensure navbar stays affixes to the top*/\n  left: 0;\n  right: 0;\n}\n.navbar .navbar-link {\n  color: var(--text-color);\n  font-size: 0.875rem;\n  font-weight: 500;\n  padding: 0.5rem 0;\n  display: block;\n}\n.navbar .navbar-link:hover {\n  color: var(--primary);\n  text-decoration: none;\n}\n.navbar .navbar-toggler {\n  border-color: transparent;\n  padding: 8px;\n}\n.navbar .navbar-toggler:focus {\n  outline: unset;\n}\n.navbar .logged-in {\n  display: flex;\n  align-items: center;\n}\n.navbar .logged-in .nav-avatar {\n  padding: 0;\n}\n\n@media (max-width: 991.98px) {\n  .navbar {\n    position: inherit;\n  }\n  .nav-item {\n    margin-left: 0.5rem;\n  }\n  .nav-item #search-container {\n    margin: 1rem 0;\n    width: 140%;\n  }\n}\n.sm-sidebar {\n  width: 100%;\n}\n\n.sm-sidebar .web-sidebar {\n  margin-top: 4px;\n  padding-bottom: 2rem;\n}\n@media (max-width: 768px) {\n  .sm-sidebar .web-sidebar {\n    padding-bottom: 0;\n  }\n}\n\n.web-sidebar {\n  position: relative;\n}\n\n.doc-sidebar {\n  margin-bottom: 0;\n  height: 100vh;\n  padding-top: 60px;\n  display: flex;\n  flex-direction: column;\n}\n.doc-sidebar .web-sidebar {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  padding: 0px 8px;\n  overflow-x: hidden;\n  overflow-y: auto;\n  height: 100%;\n  width: 17rem;\n}\n.doc-sidebar .web-sidebar .sidebar-items {\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n  width: 100%;\n  padding-top: 8px;\n}\n\n.sidebar-column {\n  margin-top: -60px;\n  border-right: 1px solid var(--border-color);\n}\n\n.sidebar-group-list {\n  display: flex;\n  flex-direction: column;\n  list-style-type: none;\n  padding: 0px;\n}\n\n.sidebar-group-container {\n  height: 32px;\n  padding: 5px 10px;\n  border-radius: 8px;\n  gap: 8px;\n}\n.sidebar-group-container .icon {\n  width: 8px;\n  height: 8px;\n  margin: 0;\n  color: #999999;\n}\n.sidebar-group-container .icon.rotate {\n  transform: rotate(90deg);\n}\n\n.sidebar-group-title {\n  font-size: 13px;\n  font-weight: 420 !important;\n  line-height: 16px;\n  letter-spacing: 0.015em;\n  text-align: left;\n}\n\n.sidebar-group-item-list {\n  display: flex;\n  flex-direction: column;\n  list-style-type: none;\n  margin-left: 10px;\n  margin-bottom: 4px !important;\n  gap: 2px;\n}\n\n.sidebar-group-item {\n  display: flex;\n  align-items: center;\n  height: 26px;\n  border-radius: 8px;\n}\n\n.sidebar-item-active a {\n  background-color: transparent !important;\n  color: var(--text-color) !important;\n}\n\n.sidebar-group-item-title {\n  color: var(--text-light);\n  text-overflow: ellipsis;\n  overflow: hidden;\n  white-space: nowrap;\n  font-weight: 420 !important;\n}\n\n.sidebar-group {\n  margin: 0;\n  font-style: normal;\n  font-weight: 500;\n  font-size: 1rem;\n  line-height: 1.5;\n  /* identical to box height, or 28px */\n  letter-spacing: -0.011em;\n}\n.sidebar-group ul {\n  padding-left: 14px;\n}\n.sidebar-group .list-unstyled:empty::after {\n  font-size: 12px;\n  font-weight: 400;\n  font-style: italic;\n  color: var(--sidebar-text-color);\n  content: \"This Wiki Group will be deleted automatically\";\n}\n.sidebar-group .collapsible {\n  padding: 6px 8px;\n  display: flex;\n  align-items: center;\n  width: 100%;\n}\n.sidebar-group div .h6 {\n  font-size: 0.875rem;\n  margin-bottom: 0;\n  line-height: 1.5rem;\n  color: var(--text-color);\n  font-weight: 700;\n}\n.sidebar-group .drop-icon,\n.sidebar-group .add-sidebar-page {\n  cursor: pointer;\n  display: inline-flex;\n  margin: 0 5px 0 auto;\n  transition: transform 0.2s ease-in-out;\n  transform: rotate(0deg);\n  color: var(--sidebar-text-color);\n}\n.sidebar-group .drop-icon.rotate,\n.sidebar-group .add-sidebar-page.rotate {\n  transform: rotate(-90deg);\n}\n\n.sidebar-group .collapsible:hover,\n.sidebar-item:hover {\n  cursor: pointer;\n}\n.sidebar-group .collapsible:hover:not(.active),\n.sidebar-item:hover:not(.active) {\n  background-color: var(--sidebar-hover-color);\n  border-radius: 0.625rem;\n}\n\n.sidebar-item {\n  display: flex;\n  align-items: center;\n  min-height: 1.75rem;\n}\n.sidebar-item.active {\n  background-color: var(--sidebar-active-item-color);\n  box-shadow: 0 0 rgba(0, 0, 0, 0), 0 0 rgba(0, 0, 0, 0), 0px 1px 2px rgba(0, 0, 0, 0.1);\n}\n.sidebar-item div {\n  display: flex;\n  align-items: center;\n}\n.sidebar-item a {\n  margin: 0;\n  width: 100%;\n  padding: 5px 12px;\n}\n.sidebar-item a:hover {\n  color: unset;\n}\n.sidebar-item :first-child {\n  width: 100%;\n}\n\n.doc-sidebar {\n  background-color: var(--sidebar-bg-color);\n  color: var(--sidebar-font-color);\n}\n\n.my-contributions,\n.new-wiki-page,\n.sidebar-edit-mode-btn,\n.sidebar-view-mode-btn,\n.add-sidebar-group {\n  cursor: pointer;\n  margin: auto;\n  font-weight: 500;\n}\n.my-contributions svg,\n.new-wiki-page svg,\n.sidebar-edit-mode-btn svg,\n.sidebar-view-mode-btn svg,\n.add-sidebar-group svg {\n  margin-bottom: 0.1rem;\n}\n.my-contributions span,\n.new-wiki-page span,\n.sidebar-edit-mode-btn span,\n.sidebar-view-mode-btn span,\n.add-sidebar-group span {\n  font-size: 0.75rem;\n}\n\n.sidebar-options {\n  bottom: 0;\n  position: sticky;\n  padding: 0.5rem;\n  background-color: var(--sidebar-bg-color);\n}\n\n.remove-sidebar-item {\n  cursor: pointer;\n  margin: 0 1rem 0 auto;\n}\n\n.collapsible .remove-sidebar-item {\n  margin-right: calc(15px - 0.5rem);\n  margin-bottom: 3px;\n}\n\n.trash-icon {\n  visibility: hidden;\n}\n\n.sidebar-item:hover .trash-icon,\n.sidebar-group .collapsible:hover .trash-icon {\n  visibility: visible;\n}\n\n@media (min-width: 992px) {\n  .doc-sidebar {\n    top: 0;\n    padding-bottom: 0;\n  }\n}\n@media (max-width: 991.98px) {\n  .web-sidebar {\n    padding-top: 0;\n  }\n  .web-sidebar > a {\n    display: none;\n  }\n}\n.page-toc {\n  background-color: var(--background-color);\n  font-size: 0.75rem;\n  position: sticky;\n  top: 90px;\n  overflow-x: hidden;\n  overflow-y: auto;\n  scrollbar-width: none;\n  height: 90vh;\n  min-width: 220px;\n  max-width: 220px;\n  margin-right: 16px;\n  padding-bottom: 10rem;\n  margin-left: auto;\n}\n.page-toc::-webkit-scrollbar {\n  display: none;\n}\n.page-toc .page-toc-title {\n  text-transform: uppercase;\n  font-size: 11px;\n  font-weight: 600;\n  line-height: 13px;\n  letter-spacing: 0.09em;\n  text-align: left;\n  margin-bottom: 0.75rem;\n}\n.page-toc h5 {\n  font-size: 0.75rem;\n  padding-left: 1rem;\n  letter-spacing: 0.4px;\n  line-height: 28px;\n  font-size: 13px;\n  font-weight: 600;\n  margin-bottom: 0;\n}\n.page-toc div {\n  width: 100%;\n  padding: 0;\n  top: 0;\n  border-left: 1px solid var(--border-color);\n}\n.page-toc div ul {\n  padding-bottom: 0;\n  margin-bottom: 0;\n}\n.page-toc div ul li a {\n  text-overflow: ellipsis;\n  overflow: hidden;\n  white-space: nowrap;\n}\n.page-toc .active {\n  color: var(--text-color);\n  box-shadow: 1px 0 0 var(--primary) inset;\n  transition: color 0.2s, box-shadow 0.2s linear, transform 0.2s linear;\n}\n.page-toc a {\n  font-size: 13px;\n  padding: 0.25rem;\n  color: var(--sidebar-text-color);\n  transform: translateX(-1px);\n}\n.page-toc a:hover {\n  color: var(--toc-hover-text-color);\n}\n\n.wiki-footer {\n  border-top: 1px solid var(--border-color);\n  margin-top: 1rem;\n  width: 100%;\n  max-width: 650px;\n  margin: auto;\n}\n.wiki-footer .btn {\n  margin-top: 1rem;\n  color: var(--text-color);\n  border: 1px solid var(--border-color);\n  border-radius: 8px;\n  padding: 11px 16px 13px !important;\n  width: 48%;\n  height: 100%;\n  transition: border-color 0.25s;\n  box-shadow: unset;\n  margin-bottom: 3.5rem;\n}\n.wiki-footer .btn p {\n  line-height: 20px;\n  margin: 0;\n}\n.wiki-footer .btn p:first-child {\n  font-size: 12px;\n  font-weight: 500;\n  color: var(--sidebar-text-color);\n}\n.wiki-footer .btn p:last-child {\n  font-size: 15px;\n  font-weight: 500;\n  color: var(--primary);\n  transition: color 0.25s;\n}\n.wiki-footer .btn:hover {\n  background-color: transparent;\n  border: 1px solid var(--primary);\n}\n.wiki-footer .btn.left {\n  margin-right: auto;\n  text-align: left;\n}\n.wiki-footer .btn.right {\n  margin-left: auto;\n  text-align: right;\n}\n\n@media (max-width: 991.98px) {\n  .wiki-footer .btn.left,\n  .wiki-footer .btn.right {\n    width: 100%;\n    margin-bottom: 10px;\n  }\n}\n.modal .modal-content {\n  background-color: var(--background-color);\n}\n.modal .modal-header {\n  border-bottom: unset;\n}\n.modal .modal-header .close {\n  font-weight: 400;\n}\n.modal .modal-body {\n  padding-top: 0;\n}\n.modal .modal-body label {\n  color: var(--text-color);\n}\n.modal .modal-body input {\n  width: 100%;\n  background: #ededed;\n  border-radius: 0.375rem;\n  border: none;\n  outline: none;\n  padding: 0.25rem 0.5rem;\n  font-size: 13px;\n  line-height: 1.25rem;\n}\n.modal .modal-body input[type=checkbox] {\n  color: #000;\n  padding: 0%;\n  border: 1px solid var(--gray-500);\n  border-radius: 4px;\n  accent-color: black;\n}\n.modal .modal-body input[type=checkbox]:checked {\n  background-color: var(--primary);\n  background-image: url(\"data:image/svg+xml, <svg viewBox='0 0 8 7' fill='none' xmlns='http://www.w3.org/2000/svg'><path d='M1 4.00001L2.66667 5.80001L7 1.20001' stroke='white' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/></svg>\"), var(--checkbox-gradient);\n  background-size: 57%, 100%;\n  box-shadow: none;\n  border: none;\n  background-repeat: no-repeat;\n  background-position: center;\n}\n.modal .modal-body input,\n.modal .modal-body textarea {\n  background-color: var(--searchbar-color);\n  color: var(--text-color);\n}\n.modal .modal-footer {\n  border-top: unset;\n  justify-content: end;\n}\n.modal .modal-footer .btn {\n  width: 100%;\n}\n\n.feedback-modal {\n  width: 25rem;\n}\n.feedback-modal .form-control:focus {\n  border: 1px solid var(--background-color);\n}\n.feedback-modal .rating-options-buttons {\n  display: grid;\n  border-radius: 6px;\n  overflow: hidden;\n  border: 1.5px solid #000;\n}\n.feedback-modal .rating-options-buttons > .ratings-number {\n  border-right: 1px solid #000;\n}\n.feedback-modal .rating-options-buttons > .ratings-number:last-child {\n  border-right: none;\n}\n.feedback-modal .ratings-number {\n  font-size: 15px;\n  padding: 8px 0px;\n  border: none;\n  color: #000;\n  background-color: #fff;\n}\n.feedback-modal .ratings-number.rating-active {\n  background-color: #000;\n  color: #fff;\n}\n.feedback-modal .submit-feedback-btn.disabled {\n  pointer-events: none;\n}\n\n.search-dialog .modal-content {\n  background-color: var(--search-modal-bg-color) !important;\n}\n\n.search-modal {\n  padding: 10px;\n  border-radius: 1rem;\n  margin-top: 100px;\n}\n.search-modal .modal-header {\n  padding: 0px;\n  padding-left: 4px;\n}\n.search-modal .search-icon {\n  width: 16px;\n}\n.search-modal input {\n  color: var(--search-modal-color);\n  margin-left: 4px;\n  background: transparent;\n  border: transparent;\n}\n.search-modal input:focus {\n  background: transparent;\n  border: transparent;\n}\n.search-modal .modal-body {\n  padding: 0px;\n}\n.search-modal .dropdown-border {\n  height: 1px;\n  background-color: var(--border-color);\n  margin: 4px 0px;\n}\n.search-modal .dropdown-border:last-child {\n  display: none;\n}\n.search-modal .search-dropdown-menu {\n  max-height: 500px;\n  overflow-x: hidden !important;\n  overflow-y: auto !important;\n}\n.search-modal .search-dropdown-menu:not(:empty) {\n  margin-top: 18px;\n}\n.search-modal .search-dropdown-menu .result-title {\n  font-size: 14px;\n  font-weight: 550;\n}\n.search-modal .search-dropdown-menu .result-text {\n  font-size: 12px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n.search-modal .search-dropdown-menu .match {\n  font-weight: 750;\n}\n.search-modal .dropdown-item {\n  padding: 8px !important;\n  color: var(--search-modal-color);\n}\n.search-modal .dropdown-item:hover {\n  background-color: var(--search-modal-hover-color);\n}\n\n#navbar-dropdown {\n  flex-grow: 1;\n  display: flex;\n  justify-content: end;\n  height: 80%;\n  cursor: pointer;\n  align-items: center;\n}\n@media (max-width: 768px) {\n  #navbar-dropdown {\n    justify-content: unset;\n  }\n}\n\n#navbar-dropdown-content {\n  position: absolute;\n  left: 10px;\n  top: 100%;\n  background-color: var(--search-modal-bg-color);\n  color: var(--search-modal-color);\n  width: 96%;\n  border-radius: 8px;\n  box-shadow: 0 10px 24px -3px rgba(0, 0, 0, 0.1019607843);\n}\n#navbar-dropdown-content a {\n  color: inherit !important;\n  text-decoration: none !important;\n}\n#navbar-dropdown-content a:hover {\n  color: inherit !important;\n  text-decoration: none !important;\n  cursor: pointer !important;\n}\n#navbar-dropdown-content .app-switcher {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n  font-size: 14px;\n  padding: 10px;\n  height: 100%;\n  max-height: 350px;\n  overflow: auto;\n  font-weight: 500;\n}\n#navbar-dropdown-content .pending-reviews-count {\n  color: var(--badge-color);\n  background-color: var(--badge-bg-color);\n  padding: 2px 6px;\n  border-radius: 10px;\n  font-size: 12px;\n  margin-left: auto;\n  width: 21px;\n  height: 21px;\n  text-align: center;\n  font-weight: 750;\n}\n#navbar-dropdown-content .space-link {\n  padding: 4px;\n  padding-left: 8px;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  width: 100%;\n}\n#navbar-dropdown-content .space-link span {\n  display: flex;\n  align-items: center;\n  width: 100%;\n}\n#navbar-dropdown-content .space-link img {\n  width: 22px;\n}\n#navbar-dropdown-content .space-link:hover {\n  background-color: var(--search-modal-hover-color);\n  border-radius: 4px;\n}\n\n.dropdown-toggle.wiki-options::after {\n  display: none;\n}\n\n.wiki-options {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  width: 2rem;\n  height: 2rem;\n  margin-left: 0.5rem;\n}\n.wiki-options svg {\n  width: 2rem;\n}\n.wiki-options .dropdown-menu {\n  min-width: 7.5rem;\n}\n\n.wiki-options:hover {\n  background-color: var(--gray-200);\n  border-radius: 5px;\n}\n\n.dark .wiki-options:hover {\n  background-color: var(--gray-700);\n}"], "mappings": ";AAAA;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AAKA;AACA;AACA;AAIA;AAIA;AAAA;AAGF;AACE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;AAAA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAIF;AACE;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AAAA;AAGF;AAAA;AAEE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAEF;AACE;AACA;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AAAA;AAGF;AACE;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AACA;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AACA;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AAAA;AAEF;AAAA;AAEE;AAAA;AAGF;AACE;AACA;AAAA;AAEF;AACE;AAAA;AAEF;AAAA;AACE;AACA;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AACA;AACA;AAAA;AAEF;AACE;AACE;AACA;AAAA;AAAA;AAIJ;AACE;AAAA;AAEF;AACE;AACA;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AACA;AACA;AAAA;AAGF;AACE;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AACA;AAAA;AAGF;AAAA;AAAA;AAAA;AAAA;AAAA;AAME;AACA;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AACA;AAAA;AAEF;AACE;AACE;AACA;AAAA;AAAA;AAIJ;AACE;AACA;AACA;AAAA;AAEF;AACE;AACE;AACA;AAAA;AAAA;AAIJ;AACE;AACA;AACA;AAAA;AAEF;AACE;AACE;AAAA;AAAA;AAIJ;AACE;AACA;AACA;AAAA;AAEF;AACE;AACE;AAAA;AAAA;AAGJ;AACE;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AAAA;AAGF;AAAA;AAEE;AACA;AACA;AAAA;AAEF;AAAA;AAEE;AACA;AAAA;AAEF;AAAA;AAAA;AAAA;AAIE;AAAA;AAEF;AAAA;AAEE;AAAA;AAEF;AAAA;AAEE;AAAA;AAEF;AAAA;AAEE;AACA;AAAA;AAEF;AAAA;AAEE;AAAA;AAEF;AAAA;AAEE;AAAA;AAEF;AAAA;AAAA;AAAA;AAIE;AAAA;AAEF;AAAA;AAEE;AAAA;AAEF;AAAA;AAAA;AAAA;AAIE;AAAA;AAEF;AAAA;AAEE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAEF;AAAA;AAEE;AAAA;AAEF;AAAA;AAAA;AAAA;AAIE;AACA;AAAA;AAEF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYE;AAAA;AAEF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUE;AAAA;AAEF;AAAA;AAEE;AACA;AACA;AAAA;AAEF;AAAA;AAEE;AACA;AAAA;AAEF;AAAA;AAEE;AAAA;AAEF;AAAA;AAEE;AACA;AACA;AAAA;AAEF;AACE;AAAA;AAEE;AAAA;AAAA;AAGJ;AAAA;AAEE;AACA;AACA;AAAA;AAEF;AACE;AAAA;AAEE;AAAA;AAAA;AAGJ;AAAA;AAEE;AACA;AACA;AAAA;AAEF;AAAA;AAEE;AACA;AACA;AACA;AAAA;AAEF;AAAA;AAEE;AACA;AACA;AACA;AACA;AAAA;AAEF;AAAA;AAAA;AAAA;AAIE;AACA;AAAA;AAEF;AAAA;AAEE;AAAA;AAEF;AAAA;AAEE;AACA;AACA;AAAA;AAEF;AAAA;AAEE;AACA;AACA;AACA;AAAA;AAEF;AAAA;AAEE;AACA;AACA;AACA;AAAA;AAEF;AAAA;AAEE;AAAA;AAEF;AAAA;AAEE;AACA;AACA;AACA;AACA;AACA;AAAA;AAEF;AAAA;AAAA;AAAA;AAIE;AACA;AACA;AACA;AACA;AAAA;AAEF;AAAA;AAAA;AAAA;AAIE;AAAA;AAEF;AAAA;AAEE;AACA;AACA;AAAA;AAEF;AAAA;AAEE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAEF;AAAA;AAEE;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAEF;AAAA;AAEE;AAAA;AAEF;AAAA;AAAA;AAAA;AAAA;AAAA;AAME;AACA;AACA;AAAA;AAEF;AAAA;AAAA;AAAA;AAIE;AAAA;AAEF;AAAA;AAEE;AACA;AAAA;AAEF;AAAA;AAEE;AAAA;AAEF;AAAA;AAEE;AACA;AACA;AACA;AACA;AAAA;AAEF;AAAA;AAEE;AACA;AACA;AAAA;AAEF;AAAA;AAEE;AAAA;AAEF;AAAA;AAEE;AAAA;AAGF;AACE;AACA;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AACE;AACA;AACA;AAAA;AAAA;AAGJ;AACE;AACA;AACA;AAAA;AAEF;AACE;AACA;AACA;AAAA;AAEF;AACE;AACA;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AACA;AACA;AAAA;AAEF;AACE;AACE;AAAA;AAAA;AAGJ;AACE;AACA;AACA;AAAA;AAEF;AACE;AACE;AAAA;AAAA;AAGJ;AACE;AACA;AACA;AAAA;AAEF;AACE;AACA;AACA;AACA;AAAA;AAEF;AACE;AACA;AACA;AACA;AACA;AAAA;AAEF;AACE;AACA;AACA;AACA;AACA;AAAA;AAEF;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAEF;AACE;AACA;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AACA;AAAA;AAEF;AACE;AACA;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AACA;AACA;AACA;AACA;AAAA;AAEF;AACE;AACE;AACA;AAAA;AAAA;AAGJ;AACE;AACA;AACA;AACA;AACA;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AACA;AACA;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AACA;AACA;AAAA;AAEF;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAEF;AACE;AACE;AACA;AACA;AAAA;AAAA;AAGJ;AACE;AACA;AACA;AAAA;AAEF;AACE;AACA;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AACE;AACA;AAAA;AAAA;AAIJ;AACE;AAAA;AAEF;AACE;AACE;AAAA;AAAA;AAGJ;AACE;AACA;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AAAA;AAGF;AACE;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AAAA;AAGF;AACE;AACA;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AACA;AACA;AACA;AACG;AACK;AAAA;AAEV;AACE;AAAA;AAEF;AAAA;AAEE;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AACA;AACA;AACA;AACA;AAAA;AAEF;AACE;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAEF;AACE;AACA;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AAAA;AAGF;AACE;AACA;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AAAA;AAEF;AACE;AAAA;AAGF;AACE;AACA;AAAA;AAGF;AAAA;AAEI;AAAA;AAAA;AAGA;AAAA;AAAA;AAGA;AAAA;AAAA;AAGJ;AACE;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AAAA;AAEF;AACE;AACA;AACA;AAAA;AAEF;AACE;AACA;AACA;AAAA;AAEF;AACE;AACA;AACA;AACA;AAAA;AAEF;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAEF;AACE;AACA;AAAA;AAEF;AACE;AACA;AACA;AACA;AAAA;AAEF;AACE;AACA;AACA;AACA;AAAA;AAEF;AACE;AACA;AACA;AACA;AAAA;AAEF;AACE;AACA;AACA;AAAA;AAEF;AACE;AACA;AACA;AAAA;AAEF;AACE;AACA;AACA;AAAA;AAEF;AACE;AACA;AAAA;AAEF;AACE;AACA;AAAA;AAEF;AACE;AACA;AAAA;AAEF;AACE;AACA;AAAA;AAEF;AACE;AACA;AACA;AACA;AACA;AACA;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AACA;AACA;AACA;AACA;AAAA;AAEF;AACE;AACA;AACA;AAAA;AAEF;AACE;AACA;AACA;AAAA;AAEF;AACE;AACA;AACA;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AACA;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AAAA;AAEF;AAAA;AAAA;AAAA;AAIE;AACA;AAAA;AAEF;AACE;AACA;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AACA;AAAA;AAEF;AACE;AACA;AACA;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AACA;AAAA;AAEF;AACE;AACA;AACA;AAAA;AAEF;AACE;AAAA;AAEF;AAAA;AAEE;AACA;AACA;AACA;AAAA;AAEF;AACE;AACA;AACA;AACA;AACA;AAAA;AAEF;AAAA;AAEE;AACA;AAAA;AAEF;AAAA;AAEE;AAAA;AAEF;AACE;AACA;AAAA;AAEF;AACE;AACA;AAAA;AAEF;AACE;AACA;AACA;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AACA;AAAA;AAEF;AACE;AACE;AACA;AACA;AAAA;AAAA;AAIJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAME;AACA;AAAA;AAGF;AACE;AAAA;AAGF;AAAA;AAAA;AAAA;AAAA;AAKE;AAAA;AAGF;AAAA;AAEE;AAAA;AAGF;AACE;AACA;AAAA;AAGF;AACE;AACA;AAAA;AAEF;AACE;AACA;AACA;AAAA;AAEF;AACE;AACE;AAAA;AAAA;AAIJ;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAEF;AACE;AACE;AAAA;AAAA;AAGJ;AACE;AACE;AACA;AAAA;AAAA;AAIJ;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAEF;AACE;AACE;AACA;AACA;AAAA;AAAA;AAGJ;AACE;AACA;AAAA;AAEF;AACE;AACA;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AAAA;AAGF;AACE;AACA;AACA;AAAA;AAEF;AACE;AACA;AAAA;AAEF;AACE;AACA;AACA;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AACA;AACA;AACA;AACA;AACA;AAAA;AAEF;AACE;AACA;AAAA;AAEF;AACE;AAAA;AAGF;AACE;AACA;AACA;AAAA;AAEF;AACE;AACE;AACA;AAAA;AAAA;AAGJ;AACE;AACA;AACA;AAAA;AAEF;AACE;AACE;AACA;AAAA;AAEF;AACE;AACA;AAAA;AAAA;AAGJ;AACE;AACA;AACA;AACA;AAAA;AAEF;AACE;AACE;AACA;AAAA;AAAA;AAGJ;AACE;AAAA;AAEF;AACE;AACA;AACA;AACA;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AACE;AAAA;AAAA;AAGJ;AACE;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AAEA;AACA;AAAA;AAEF;AACE;AACA;AACA;AACA;AACA;AAAA;AAEF;AACE;AACA;AAAA;AAEF;AACE;AACA;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AACA;AAAA;AAEF;AACE;AAAA;AAGF;AACE;AACE;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AACA;AAAA;AAAA;AAGJ;AACE;AAAA;AAGF;AACE;AACA;AAAA;AAEF;AACE;AACE;AAAA;AAAA;AAIJ;AACE;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AAAA;AAEF;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAEF;AACE;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AAAA;AAEF;AACE;AACA;AACA;AACA;AAAA;AAEF;AACE;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AAEA;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AACA;AACA;AACA;AACA;AAAA;AAEF;AACE;AACA;AACA;AACA;AAAA;AAEF;AACE;AACA;AACA;AACA;AACA;AAAA;AAEF;AAAA;AAEE;AACA;AACA;AACA;AACA;AACA;AAAA;AAEF;AAAA;AAEE;AAAA;AAGF;AAAA;AAEE;AAAA;AAEF;AAAA;AAEE;AACA;AAAA;AAGF;AACE;AACA;AACA;AAAA;AAEF;AACE;AACA;AAAA;AAAA;AAAA;AAAA;AAEF;AACE;AACA;AAAA;AAEF;AACE;AACA;AACA;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AAAA;AAGF;AACE;AACA;AAAA;AAGF;AAAA;AAAA;AAAA;AAAA;AAKE;AACA;AACA;AAAA;AAEF;AAAA;AAAA;AAAA;AAAA;AAKE;AAAA;AAEF;AAAA;AAAA;AAAA;AAAA;AAKE;AAAA;AAGF;AACE;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AAAA;AAGF;AACE;AACA;AAAA;AAGF;AACE;AAAA;AAGF;AAAA;AAEE;AAAA;AAGF;AACE;AACE;AACA;AAAA;AAAA;AAGJ;AACE;AACE;AAAA;AAEF;AACE;AAAA;AAAA;AAGJ;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAEF;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAEF;AACE;AACA;AACA;AACA;AAAA;AAEF;AACE;AACA;AAAA;AAEF;AACE;AACA;AACA;AAAA;AAEF;AACE;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAEF;AACE;AACA;AACA;AACA;AAAA;AAEF;AACE;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AAAA;AAEF;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAEF;AACE;AACA;AAAA;AAEF;AACE;AACA;AACA;AAAA;AAEF;AACE;AACA;AACA;AACA;AAAA;AAEF;AACE;AACA;AAAA;AAEF;AACE;AACA;AAAA;AAEF;AACE;AACA;AAAA;AAGF;AACE;AAAA;AAEE;AACA;AAAA;AAAA;AAGJ;AACE;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAEF;AACE;AACA;AACA;AACA;AACA;AAAA;AAEF;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAEF;AAAA;AAEE;AACA;AAAA;AAEF;AACE;AACA;AAAA;AAEF;AACE;AAAA;AAGF;AACE;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AACA;AACA;AACA;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AACA;AACA;AACA;AACA;AAAA;AAEF;AACE;AACA;AAAA;AAEF;AACE;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AACA;AACA;AAAA;AAEF;AACE;AACA;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AACA;AACA;AACA;AAAA;AAEF;AACE;AACA;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AACA;AACA;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AACA;AACA;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AACA;AAAA;AAEF;AACE;AACA;AACA;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AACA;AAAA;AAEF;AACE;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AACA;AAAA;AAEF;AACE;AACE;AAAA;AAAA;AAIJ;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAEF;AACE;AACA;AAAA;AAEF;AACE;AACA;AACA;AAAA;AAEF;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAEF;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAEF;AACE;AACA;AACA;AACA;AACA;AACA;AAAA;AAEF;AACE;AACA;AACA;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AACA;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AACA;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AAAA;AAGF;AACE;AACA;AAAA;AAGF;AACE;AAAA;", "names": []}