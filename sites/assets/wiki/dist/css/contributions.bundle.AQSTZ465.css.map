{"version": 3, "sources": ["../../../../../../../../tmp/tmp-4109-uaLsB72kqPr8/wiki/wiki/public/scss/contributions.bundle.css"], "sourcesContent": [".frappe-card {\n  padding: 0;\n  background-color: var(--background-color);\n}\n\n.navbar-brand-container {\n  background-color: var(--background-color);\n  position: relative;\n  border: 0;\n}\n\n.navbar .doc-container .navbar-collapse {\n  margin-left: 0;\n}\n\n.list-jobs {\n  font-size: 14px;\n}\n\n.table {\n  margin-bottom: 0px;\n  margin-top: 0px;\n  color: var(--text-color);\n}\n.table td {\n  padding: 14px 20px;\n}\n.table a {\n  color: var(--btn-text-color) !important;\n}\n\nthead td {\n  border-top: 0 !important;\n}\n\n.table th,\n.table td {\n  margin-top: -0.5px;\n}\n\n.worker-name {\n  display: flex;\n  align-items: center;\n  white-space: nowrap;\n}\n\n.job-name {\n  font-size: 13px;\n  font-family: \"Courier New\", Courier, monospace;\n}\n\n.background-job-row:hover {\n  background-color: #f9fafa;\n}\n\n.no-background-jobs {\n  min-height: 320px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-direction: column;\n}\n\n.no-background-jobs > img {\n  margin-bottom: var(15px);\n  max-height: 100px;\n}\n\n.footer {\n  align-items: flex-end;\n  margin-top: 15px;\n  font-size: 14px;\n}\n\n.page-content-wrapper {\n  padding: 0 !important;\n  margin: 0 0 1rem 0 !important;\n}\n\n.all-contributions .table td.message-col {\n  width: auto;\n}\n.all-contributions .table td.status-col {\n  width: 15%;\n}\n.all-contributions .table td.raised-by-col {\n  width: auto;\n}\n.all-contributions .table td.date-col {\n  width: 15%;\n}\n.all-contributions .table .patch-row {\n  cursor: pointer;\n  transition: background-color 0.2s ease;\n}\n.all-contributions .table .patch-row:hover {\n  background-color: var(--btn-secondary-hover-bg-color);\n}\n.all-contributions .message-cell {\n  font-weight: 550;\n}\n.all-contributions .message-cell,\n.all-contributions .raised-by-cell,\n.all-contributions .space-cell {\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  max-width: 0;\n}\n.all-contributions .action-buttons .btn {\n  margin-right: 4px;\n  border-radius: 8px;\n  padding: 3px 8px;\n  font-size: 13px;\n  border: none;\n}\n.all-contributions .action-buttons .btn.btn-default {\n  background-color: #e9e9e9;\n  color: #000;\n}\n.all-contributions .action-buttons .btn.btn-default:hover {\n  background-color: #dcdcdc;\n}\n.all-contributions .action-buttons .btn.btn-success {\n  background-color: #000;\n  color: #fff;\n}\n.all-contributions .action-buttons .btn.btn-success:hover {\n  background-color: #333;\n}\n.all-contributions .action-buttons .btn.btn-danger {\n  background-color: #dc3545;\n  color: #fff;\n}\n.all-contributions .action-buttons .btn.btn-danger:hover {\n  background-color: #bb2d3b;\n}\n.all-contributions .action-buttons .btn:last-child {\n  margin-right: 0;\n}\n.all-contributions .get_patches {\n  margin-top: 15px;\n}\n\n.diff-content {\n  text-wrap: auto;\n  color: var(--text-color);\n}\n.diff-content ins {\n  background-color: var(--diff-ins-bg-color);\n  color: var(--diff-ins-text-color);\n  text-decoration: none;\n}\n.diff-content del {\n  background-color: var(--diff-del-bg-color);\n  color: var(--diff-del-text-color);\n  text-decoration: none;\n}\n\n.review-patch-modal {\n  min-height: calc(100vh - 100px);\n}\n.review-patch-modal .modal-header {\n  border-bottom: 1px solid #e9e9e9 !important;\n  padding: 1rem 1rem !important;\n  display: flex;\n  flex-direction: row;\n}\n@media (max-width: 768px) {\n  .review-patch-modal .modal-header {\n    flex-direction: column;\n  }\n}\n.review-patch-modal .modal-header .close {\n  position: absolute;\n  top: 5px;\n  right: 8px;\n  padding: 0;\n  margin: 0;\n}\n.review-patch-modal .view-toggle-buttons-wrapper {\n  display: flex;\n  align-items: end;\n}\n.review-patch-modal .view-toggle-buttons {\n  display: flex;\n  gap: 6px;\n  background-color: var(--view-bg-color);\n  padding: 4px;\n  width: -moz-max-content;\n  width: max-content;\n  border-radius: 10px;\n  margin-top: 22px;\n  margin-right: 14px;\n}\n.review-patch-modal .view-toggle-buttons .btn {\n  padding: 3px 6px;\n  border-radius: 8px;\n  font-size: 12px;\n  font-weight: 500;\n  color: var(--view-btn-text);\n  background-color: var(--view-bg-color);\n  transition: all 0.2s ease;\n}\n.review-patch-modal .view-toggle-buttons .btn.active {\n  background-color: var(--view-btn-bg-active);\n  color: var(--view-btn-text-active);\n}\n.review-patch-modal .view-toggle-buttons .btn:active {\n  background-color: var(--view-btn-bg-active) !important;\n  color: var(--view-btn-text-active) !important;\n  outline: none !important;\n}\n.review-patch-modal .modal-title {\n  max-width: 100% !important;\n}\n.review-patch-modal .modal-body {\n  padding-top: 18px !important;\n}\n.review-patch-modal .modal-body .diff-content,\n.review-patch-modal .modal-body .preview-content {\n  display: none;\n  max-width: 100%;\n}\n.review-patch-modal .modal-body .diff-content.active,\n.review-patch-modal .modal-body .preview-content.active {\n  display: block;\n}\n.review-patch-modal .review-modal-footer {\n  display: flex;\n  flex-wrap: nowrap;\n  gap: 8px;\n  border-top: 1px solid #e9e9e9;\n}\n.review-patch-modal .review-modal-footer .btn {\n  width: auto !important;\n}\n.review-patch-modal .review-modal-footer .approve-patch {\n  background-color: #000;\n  color: #fff;\n}\n.review-patch-modal .review-modal-footer .reject-patch {\n  background-color: #fff;\n  color: #000;\n  background-color: #f8f8f8;\n}\n\n.filters-wrapper {\n  display: flex;\n  align-items: center;\n  gap: 24px;\n}\n.filters-wrapper .space-filter {\n  display: flex;\n  flex-direction: column;\n  width: -moz-fit-content;\n  width: fit-content;\n}\n.filters-wrapper .space-filter label {\n  font-size: 14px;\n  font-weight: 550;\n  margin-bottom: 4px;\n}\n.filters-wrapper .space-filter select {\n  position: relative;\n  border: 1.5px solid #c7c7c7;\n  border-radius: 8px;\n  padding: 8px;\n  padding-top: 2px;\n  padding-bottom: 2px;\n  font-size: 14px;\n  -moz-appearance: none;\n  -webkit-appearance: none;\n  appearance: none;\n}\n.filters-wrapper .space-filter select svg {\n  position: absolute;\n  right: 12px;\n  top: 50%;\n  transform: translateY(-50%);\n}\n\n.frappe-card {\n  margin-top: 12px;\n}\n\n.contributions-view {\n  width: 100%;\n  padding: 32px;\n  padding-top: 18px;\n}\n.contributions-view .btn-warning {\n  background-color: var(--admin-banner-btn-bg);\n  color: var(--admin-banner-btn-text) !important;\n}\n.contributions-view .btn-warning:hover {\n  background-color: var(--admin-banner-btn-hover-bg);\n}\n.contributions-view .btn-warning:active {\n  background-color: var(--admin-banner-btn-active-bg) !important;\n}\n.contributions-view .back-to-content {\n  font-size: 12px;\n  cursor: pointer;\n  color: var(--text-color);\n}\n.contributions-view .page-title {\n  font-weight: 550;\n}\n.contributions-view td {\n  border-top: 1px solid var(--table-border-color);\n}\n\n.pagination-container {\n  margin-top: 12px;\n}"], "mappings": ";AAAA;AACE;AACA;AAAA;AAGF;AACE;AACA;AACA;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AACA;AACA;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AAAA;AAGF;AACE;AAAA;AAGF;AAAA;AAEE;AAAA;AAGF;AACE;AACA;AACA;AAAA;AAGF;AACE;AACA;AAAA;AAAA;AAAA;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AACA;AACA;AACA;AACA;AAAA;AAGF;AACE;AACA;AAAA;AAGF;AACE;AACA;AACA;AAAA;AAGF;AACE;AACA;AAAA;AAGF;AACE;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AACA;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AAAA;AAEF;AAAA;AAAA;AAGE;AACA;AACA;AACA;AAAA;AAEF;AACE;AACA;AACA;AACA;AACA;AAAA;AAEF;AACE;AACA;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AACA;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AACA;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AAAA;AAGF;AACE;AACA;AAAA;AAEF;AACE;AACA;AACA;AAAA;AAEF;AACE;AACA;AACA;AAAA;AAGF;AACE;AAAA;AAEF;AACE;AACA;AACA;AACA;AAAA;AAEF;AACE;AACE;AAAA;AAAA;AAGJ;AACE;AACA;AACA;AACA;AACA;AAAA;AAEF;AACE;AACA;AAAA;AAEF;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAEF;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAEF;AACE;AACA;AAAA;AAEF;AACE;AACA;AACA;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AAAA;AAEF;AAAA;AAEE;AACA;AAAA;AAEF;AAAA;AAEE;AAAA;AAEF;AACE;AACA;AACA;AACA;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AACA;AAAA;AAEF;AACE;AACA;AACA;AAAA;AAGF;AACE;AACA;AACA;AAAA;AAEF;AACE;AACA;AACA;AACA;AAAA;AAEF;AACE;AACA;AACA;AAAA;AAEF;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAEF;AACE;AACA;AACA;AACA;AAAA;AAGF;AACE;AAAA;AAGF;AACE;AACA;AACA;AAAA;AAEF;AACE;AACA;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AACA;AACA;AAAA;AAEF;AACE;AAAA;AAEF;AACE;AAAA;AAGF;AACE;AAAA;", "names": []}