_HiStOrY_V2_
Truncate\040table\040`tabForwarder`;
select\040*\040from\040tabForwarder;
frappe.db.count("HS\040Code")
;
select\040*\040from\040tabSI\040Clauses;
delete\040*\040from\040`tabBill`;
delete\040*\040from\040`tabBill`
;
delete\040*\040from\040tabBill;
DELETE\040FROM\040`tabBill`;
SET\040SQL_SAFE_UPDATES\040=\0400;
DELETE\040FROM\040`tabBill`;
SET\040SQL_SAFE_UPDATES\040=\0401;
delete\040*\040from\040`tabBill`;
DELETE\040FROM\040`tabBill`;
SET\040SQL_SAFE_UPDATES\040=\0400;
DELETE\040FROM\040`tabBill`;
UPDATE\040tabBill\040Item\040Details
SET\040rate\040=\0400
WHERE\040rate\040IS\040NULL\040OR\040rate\040=\040'';
UPDATE\040tabBill\040Item\040Details\040SET\040rate\040=\0400\040WHERE\040rate\040IS\040NULL\040OR\040rate\040=\040'';
UPDATE\040`tabBill\040Item\040Details`
SET\040rate\040=\0400
WHERE\040rate\040IS\040NULL\040OR\040rate\040=\040'';
UPDATE\040`tabBill\040Item\040Details`\040SET\040rate\040=\0400\040WHERE\040rate\040IS\040NULL\040OR\040rate\040=\040'';
SET\040SQL_SAFE_UPDATES\040=\0400;
UPDATE\040`tabBill\040Item\040Details`\040SET\040rate\040=\0400\040WHERE\040rate\040IS\040NULL\040OR\040rate\040=\040'';
select\040status\040from\040`tabInvoice\040DB`\040where\040status="Paid";
select\040status\040from\040`tabInvoice\040DB`\040where\040status='Pending\040Payment';
select\040count\040from\040`tabInvoice\040DB`;
select\040count(*)\040from\040`tabInvoice\040DB`;
frappe.db.sql("""
\040\040\040\040SELECT\040pi.partner_name,\040COUNT(*)\040AS\040count
\040\040\040\040FROM\040`tabPartner\040Information`\040pi
\040\040\040\040INNER\040JOIN\040`tabBill\040of\040Lading`\040bol\040ON\040pi.parent\040=\040bol.name
\040\040\040\040WHERE\040pi.partner_role\040=\040'Consignee'
\040\040\040\040GROUP\040BY\040pi.partner_name
\040\040\040\040ORDER\040BY\040count\040DESC
""",\040as_dict=True)
;
frappe.db.sql("""\012\040\040\040\040SELECT\040pi.partner_name,\040COUNT(*)\040AS\040count\012\040\040\040\040FROM\040`tabPartner\040Information`\040pi\012\040\040\040\040INNER\040JOIN\040`tabBill\040of\040Lading`\040bol\040ON\040pi.parent\040=\040bol.name\012\040\040\040\040WHERE\040pi.partner_role\040=\040'Consignee'\012\040\040\040\040GROUP\040BY\040pi.partner_name\012\040\040\040\040ORDER\040BY\040count\040DESC\012""",\040as_dict=True);
DELETE\040FROM\040`tabInstalled\040Application`\040WHERE\040name\040=\040'<that-uuid>';
DELETE\040FROM\040`tabInstalled\040Application`\040WHERE\040name\040=\040'5ge29jf603';
SELECT\040*\040FROM\040`tabInstalled\040Application`\040WHERE\040app_name='wiki';
SELECT\040*\040FROM\040`tabModule\040Def`\040WHERE\040app_name='wiki';
SELECT\040*\040FROM\040`tabApp\040Installed`\040WHERE\040app_name='wiki';
DELETE\040FROM\040`tabDocType`\040WHERE\040module\040LIKE\040'%wiki%';
DELETE\040FROM\040`tabCustom\040Field`\040WHERE\040dt\040LIKE\040'%wiki%';
DELETE\040FROM\040`tabDocField`\040WHERE\040parent\040IN\040(SELECT\040name\040FROM\040`tabDocType`\040WHERE\040module\040LIKE\040'%wiki%');
DELETE\040FROM\040`tabProperty\040Setter`\040WHERE\040doc_type\040LIKE\040'%wiki%';
DELETE\040FROM\040`tabHas\040Role`\040WHERE\040parent\040IN\040(SELECT\040name\040FROM\040`tabDocType`\040WHERE\040module\040LIKE\040'%wiki%');
DELETE\040FROM\040`tabUser\040Permission`\040WHERE\040for_value\040IN\040(SELECT\040name\040FROM\040`tabDocType`\040WHERE\040module\040LIKE\040'%wiki%');
SET\040SQL_SAFE_UPDATES\040=\0400;
DELETE\040FROM\040`tabDocType`\040WHERE\040module\040LIKE\040'%wiki%';
DELETE\040FROM\040`tabCustom\040Field`\040WHERE\040dt\040LIKE\040'%wiki%';
DELETE\040FROM\040`tabDocField`\040WHERE\040parent\040IN\040(SELECT\040name\040FROM\040`tabDocType`\040WHERE\040module\040LIKE\040'%wiki%');
DELETE\040FROM\040`tabProperty\040Setter`\040WHERE\040doc_type\040LIKE\040'%wiki%';
DELETE\040FROM\040`tabHas\040Role`\040WHERE\040parent\040IN\040(SELECT\040name\040FROM\040`tabDocType`\040WHERE\040module\040LIKE\040'%wiki%');
DELETE\040FROM\040`tabUser\040Permission`\040WHERE\040for_value\040IN\040(SELECT\040name\040FROM\040`tabDocType`\040WHERE\040module\040LIKE\040'%wiki%');
DELETE\040FROM\040`tabModule\040Def`\040WHERE\040name\040LIKE\040'%wiki%';
SET\040SQL_SAFE_UPDATES\040=\0401;
DELETE\040FROM\040`tabDocType`\040WHERE\040name='Wiki\040Page';
--\040Disable\040safe\040update\040mode
SET\040SQL_SAFE_UPDATES\040=\0400;
--\040Delete\040DocFields\040of\040wiki\040DocTypes
DELETE\040df
FROM\040`tabDocField`\040df
JOIN\040`tabDocType`\040dt\040ON\040df.parent\040=\040dt.name
WHERE\040dt.module\040LIKE\040'%wiki%';
DELETE\040df\040FROM\040`tabDocField`\040df\040JOIN\040`tabDocType`\040dt\040ON\040df.parent\040=\040dt.name\040WHERE\040dt.module\040LIKE\040'%wiki%';
--\040Delete\040Property\040Setters
DELETE\040FROM\040`tabProperty\040Setter`\040
WHERE\040doc_type\040IN\040(SELECT\040name\040FROM\040`tabDocType`\040WHERE\040module\040LIKE\040'%wiki%');
DELETE\040FROM\040`tabProperty\040Setter`\040\040WHERE\040doc_type\040IN\040(SELECT\040name\040FROM\040`tabDocType`\040WHERE\040module\040LIKE\040'%wiki%');
--\040Delete\040Has\040Role\040entries
DELETE\040hr
FROM\040`tabHas\040Role`\040hr
JOIN\040`tabDocType`\040dt\040ON\040hr.parent\040=\040dt.name
WHERE\040dt.module\040LIKE\040'%wiki%';
DELETE\040hr\040FROM\040`tabHas\040Role`\040hr\040JOIN\040`tabDocType`\040dt\040ON\040hr.parent\040=\040dt.name\040WHERE\040dt.module\040LIKE\040'%wiki%';
--\040Delete\040User\040Permissions
DELETE\040up
FROM\040`tabUser\040Permission`\040up
JOIN\040`tabDocType`\040dt\040ON\040up.for_value\040=\040dt.name
WHERE\040dt.module\040LIKE\040'%wiki%';
DELETE\040up\040FROM\040`tabUser\040Permission`\040up\040JOIN\040`tabDocType`\040dt\040ON\040up.for_value\040=\040dt.name\040WHERE\040dt.module\040LIKE\040'%wiki%';
--\040Delete\040Custom\040Fields
DELETE\040FROM\040`tabCustom\040Field`\040
WHERE\040dt\040IN\040(SELECT\040name\040FROM\040`tabDocType`\040WHERE\040module\040LIKE\040'%wiki%');
DELETE\040FROM\040`tabCustom\040Field`\040\040WHERE\040dt\040IN\040(SELECT\040name\040FROM\040`tabDocType`\040WHERE\040module\040LIKE\040'%wiki%');
--\040Delete\040DocTypes
DELETE\040FROM\040`tabDocType`\040
WHERE\040module\040LIKE\040'%wiki%';
DELETE\040FROM\040`tabDocType`\040\040WHERE\040module\040LIKE\040'%wiki%';
--\040Delete\040Module\040Definitions
DELETE\040FROM\040`tabModule\040Def`\040
WHERE\040app_name\040=\040'wiki';
DELETE\040FROM\040`tabModule\040Def`\040\040WHERE\040app_name\040=\040'wiki';
--\040Delete\040Installed\040Application\040reference\040(if\040still\040exists)
DELETE\040FROM\040`tabInstalled\040Application`\040
WHERE\040app_name\040=\040'wiki';
DELETE\040FROM\040`tabInstalled\040Application`\040\040WHERE\040app_name\040=\040'wiki';
--\040Re-enable\040safe\040update\040mode
SET\040SQL_SAFE_UPDATES\040=\0401;
UPDATE\040`tabBill\040Item\040Details`\040
SET\040rate\040=\0400\040
WHERE\040rate\040=\040''\040OR\040rate\040IS\040NULL;
UPDATE\040`tabBill\040Item\040Details`\040\040SET\040rate\040=\0400\040\040WHERE\040rate\040=\040''\040OR\040rate\040IS\040NULL;
UPDATE\040`tabBill\040Item\040Details`\040
SET\040amount\040=\0400\040
WHERE\040amount\040=\040''\040OR\040amount\040IS\040NULL;
UPDATE\040`tabBill\040Item\040Details`\040\040SET\040amount\040=\0400\040\040WHERE\040amount\040=\040''\040OR\040amount\040IS\040NULL;
USE\040`_9daf192e2a9c7a7e`;
--\040Check\040max\040digits\040before\040and\040after\040decimal
SELECT\040MAX(LENGTH(FLOOR(rate)))\040AS\040max_digits_before,
\040\040\040\040\040\040\040MAX(LENGTH(SUBSTRING_INDEX(rate,'.',-1)))\040AS\040max_digits_after
FROM\040`tabBill\040Item\040Details`;
SELECT\040MAX(LENGTH(FLOOR(rate)))\040AS\040max_digits_before,\040\040\040\040\040\040\040\040MAX(LENGTH(SUBSTRING_INDEX(rate,'.',-1)))\040AS\040max_digits_after\040FROM\040`tabBill\040Item\040Details`;
SELECT\040MAX(LENGTH(FLOOR(amount)))\040AS\040max_digits_before,
\040\040\040\040\040\040\040MAX(LENGTH(SUBSTRING_INDEX(amount,'.',-1)))\040AS\040max_digits_after
FROM\040`tabBill\040Item\040Details`;
SELECT\040MAX(LENGTH(FLOOR(amount)))\040AS\040max_digits_before,\040\040\040\040\040\040\040\040MAX(LENGTH(SUBSTRING_INDEX(amount,'.',-1)))\040AS\040max_digits_after\040FROM\040`tabBill\040Item\040Details`;
--\040Check\040for\040non-numeric\040values
SELECT\040rate\040FROM\040`tabBill\040Item\040Details`\040WHERE\040NOT\040rate\040REGEXP\040'^-?[0-9]+(\134.[0-9]+)?$';
SELECT\040amount\040FROM\040`tabBill\040Item\040Details`\040WHERE\040NOT\040amount\040REGEXP\040'^-?[0-9]+(\134.[0-9]+)?$';
SET\040SQL_SAFE_UPDATES\040=\0400;
UPDATE\040`tabBill\040Item\040Details`\040
SET\040rate\040=\0400\040
WHERE\040rate\040=\040''\040OR\040rate\040IS\040NULL;
UPDATE\040`tabBill\040Item\040Details`\040\040SET\040rate\040=\0400\040\040WHERE\040rate\040=\040''\040OR\040rate\040IS\040NULL;
UPDATE\040`tabBill\040Item\040Details`\040
SET\040amount\040=\0400\040
WHERE\040amount\040=\040''\040OR\040amount\040IS\040NULL;
UPDATE\040`tabBill\040Item\040Details`\040\040SET\040amount\040=\0400\040\040WHERE\040amount\040=\040''\040OR\040amount\040IS\040NULL;
SET\040SQL_SAFE_UPDATES\040=\0401;
--\040Disable\040safe\040update\040mode
SET\040SQL_SAFE_UPDATES\040=\0400;
--\0401.\040Delete\040DocFields\040of\040wiki\040DocTypes
DELETE\040df
FROM\040`tabDocField`\040df
JOIN\040`tabDocType`\040dt\040ON\040df.parent\040=\040dt.name
WHERE\040dt.module\040LIKE\040'%wiki%';
DELETE\040df\040FROM\040`tabDocField`\040df\040JOIN\040`tabDocType`\040dt\040ON\040df.parent\040=\040dt.name\040WHERE\040dt.module\040LIKE\040'%wiki%';
--\0402.\040Delete\040Property\040Setters
DELETE\040FROM\040`tabProperty\040Setter`
WHERE\040doc_type\040IN\040(SELECT\040name\040FROM\040`tabDocType`\040WHERE\040module\040LIKE\040'%wiki%');
DELETE\040FROM\040`tabProperty\040Setter`\040WHERE\040doc_type\040IN\040(SELECT\040name\040FROM\040`tabDocType`\040WHERE\040module\040LIKE\040'%wiki%');
--\0403.\040Delete\040Has\040Role\040entries
DELETE\040hr
FROM\040`tabHas\040Role`\040hr
JOIN\040`tabDocType`\040dt\040ON\040hr.parent\040=\040dt.name
WHERE\040dt.module\040LIKE\040'%wiki%';
DELETE\040hr\040FROM\040`tabHas\040Role`\040hr\040JOIN\040`tabDocType`\040dt\040ON\040hr.parent\040=\040dt.name\040WHERE\040dt.module\040LIKE\040'%wiki%';
--\0404.\040Delete\040User\040Permissions
DELETE\040up
FROM\040`tabUser\040Permission`\040up
JOIN\040`tabDocType`\040dt\040ON\040up.for_value\040=\040dt.name
WHERE\040dt.module\040LIKE\040'%wiki%';
DELETE\040up\040FROM\040`tabUser\040Permission`\040up\040JOIN\040`tabDocType`\040dt\040ON\040up.for_value\040=\040dt.name\040WHERE\040dt.module\040LIKE\040'%wiki%';
--\0405.\040Delete\040Custom\040Fields
DELETE\040FROM\040`tabCustom\040Field`
WHERE\040dt\040IN\040(SELECT\040name\040FROM\040`tabDocType`\040WHERE\040module\040LIKE\040'%wiki%');
DELETE\040FROM\040`tabCustom\040Field`\040WHERE\040dt\040IN\040(SELECT\040name\040FROM\040`tabDocType`\040WHERE\040module\040LIKE\040'%wiki%');
--\0406.\040Delete\040DocTypes
DELETE\040FROM\040`tabDocType`
WHERE\040module\040LIKE\040'%wiki%';
DELETE\040FROM\040`tabDocType`\040WHERE\040module\040LIKE\040'%wiki%';
--\0407.\040Delete\040Module\040Definitions
DELETE\040FROM\040`tabModule\040Def`
WHERE\040app_name\040=\040'wiki';
DELETE\040FROM\040`tabModule\040Def`\040WHERE\040app_name\040=\040'wiki';
--\0408.\040Delete\040Installed\040Application\040reference
DELETE\040FROM\040`tabInstalled\040Application`
WHERE\040app_name\040=\040'wiki';
DELETE\040FROM\040`tabInstalled\040Application`\040WHERE\040app_name\040=\040'wiki';
--\040Re-enable\040safe\040update\040mode
SET\040SQL_SAFE_UPDATES\040=\0401;
--\040Check\040DocTypes
SELECT\040name,\040module\040FROM\040`tabDocType`\040WHERE\040module\040LIKE\040'%wiki%';
--\040Check\040DocFields
SELECT\040name,\040parent\040FROM\040`tabDocField`\040WHERE\040parent\040IN\040(SELECT\040name\040FROM\040`tabDocType`\040WHERE\040module\040LIKE\040'%wiki%');
--\040Check\040Custom\040Fields
SELECT\040name,\040dt\040FROM\040`tabCustom\040Field`\040WHERE\040dt\040IN\040(SELECT\040name\040FROM\040`tabDocType`\040WHERE\040module\040LIKE\040'%wiki%');
--\040Check\040Property\040Setters
SELECT\040name,\040doc_type\040FROM\040`tabProperty\040Setter`\040WHERE\040doc_type\040IN\040(SELECT\040name\040FROM\040`tabDocType`\040WHERE\040module\040LIKE\040'%wiki%');
--\040Check\040Has\040Role\040entries
SELECT\040parent,\040role\040FROM\040`tabHas\040Role`\040WHERE\040parent\040IN\040(SELECT\040name\040FROM\040`tabDocType`\040WHERE\040module\040LIKE\040'%wiki%');
--\040Check\040User\040Permissions
SELECT\040parent,\040for_value\040FROM\040`tabUser\040Permission`\040WHERE\040for_value\040IN\040(SELECT\040name\040FROM\040`tabDocType`\040WHERE\040module\040LIKE\040'%wiki%');
--\040Check\040Module\040Definitions
SELECT\040name\040FROM\040`tabModule\040Def`\040WHERE\040app_name\040=\040'wiki';
--\040Check\040Installed\040Applications
SELECT\040app_name\040FROM\040`tabInstalled\040Application`\040WHERE\040app_name\040=\040'wiki';
UPDATE\040`tabBill\040Item\040Details`\040
SET\040rate\040=\0400\040
WHERE\040rate\040=\040''\040OR\040rate\040IS\040NULL;
UPDATE\040`tabBill\040Item\040Details`\040\040SET\040rate\040=\0400\040\040WHERE\040rate\040=\040''\040OR\040rate\040IS\040NULL;
UPDATE\040`tabBill\040Item\040Details`\040
SET\040amount\040=\0400\040
WHERE\040amount\040=\040''\040OR\040amount\040IS\040NULL;
UPDATE\040`tabBill\040Item\040Details`\040\040SET\040amount\040=\0400\040\040WHERE\040amount\040=\040''\040OR\040amount\040IS\040NULL;
SET\040SQL_SAFE_UPDATES\040=\0400;
UPDATE\040`tabBill\040Item\040Details`\040
SET\040rate\040=\0400\040
WHERE\040rate\040=\040''\040OR\040rate\040IS\040NULL;
UPDATE\040`tabBill\040Item\040Details`\040\040SET\040rate\040=\0400\040\040WHERE\040rate\040=\040''\040OR\040rate\040IS\040NULL;
UPDATE\040`tabBill\040Item\040Details`\040
SET\040amount\040=\0400\040
WHERE\040amount\040=\040''\040OR\040amount\040IS\040NULL;
UPDATE\040`tabBill\040Item\040Details`\040\040SET\040amount\040=\0400\040\040WHERE\040amount\040=\040''\040OR\040amount\040IS\040NULL;
SET\040SQL_SAFE_UPDATES\040=\0401;
SELECT\040COUNT(*)\040AS\040evgm_count
FROM\040`tabEVGM`
WHERE\040booking_number\040LIKE\040'%2365%'\040OR\040name\040LIKE\040'%2365%';
SELECT\040COUNT(*)\040AS\040evgm_count\040FROM\040`tabEVGM`\040WHERE\040booking_number\040LIKE\040'%2365%'\040OR\040name\040LIKE\040'%2365%';
SELECT\040COUNT(*)\040AS\040equipment_count
FROM\040`tabEquipments`
WHERE\040equipment_name\040LIKE\040'%2365%'
\040\040\040OR\040name\040LIKE\040'%2365%'
\040\040\040OR\040carrier_booking_number\040LIKE\040'%2365%';
SELECT\040COUNT(*)\040AS\040equipment_count\040FROM\040`tabEquipments`\040WHERE\040equipment_name\040LIKE\040'%2365%'\040\040\040\040OR\040name\040LIKE\040'%2365%'\040\040\040\040OR\040carrier_booking_number\040LIKE\040'%2365%';
SELECT\040
\040\040\040\040(SELECT\040COUNT(*)\040
\040\040\040\040\040FROM\040`tabEVGM`\040
\040\040\040\040\040WHERE\040booking_number\040LIKE\040'%2365%'\040OR\040name\040LIKE\040'%2365%')\040
\040\040\040\040+\040
\040\040\040\040(SELECT\040COUNT(*)\040
\040\040\040\040\040FROM\040`tabEquipments`\040
\040\040\040\040\040WHERE\040equipment_name\040LIKE\040'%2365%'\040
\040\040\040\040\040\040\040\040OR\040name\040LIKE\040'%2365%'\040
\040\040\040\040\040\040\040\040OR\040carrier_booking_number\040LIKE\040'%2365%')\040
\040\040\040\040AS\040total_matches;
SELECT\040\040\040\040\040\040(SELECT\040COUNT(*)\040\040\040\040\040\040\040FROM\040`tabEVGM`\040\040\040\040\040\040\040WHERE\040booking_number\040LIKE\040'%2365%'\040OR\040name\040LIKE\040'%2365%')\040\040\040\040\040\040+\040\040\040\040\040\040(SELECT\040COUNT(*)\040\040\040\040\040\040\040FROM\040`tabEquipments`\040\040\040\040\040\040\040WHERE\040equipment_name\040LIKE\040'%2365%'\040\040\040\040\040\040\040\040\040\040OR\040name\040LIKE\040'%2365%'\040\040\040\040\040\040\040\040\040\040OR\040carrier_booking_number\040LIKE\040'%2365%')\040\040\040\040\040\040AS\040total_matches;
