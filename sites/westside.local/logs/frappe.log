2025-08-25 09:17:56,505 ERROR frappe New Exception collected in error log
Site: westside.local/
Form Dict: {'doc': '{"name":"Invoice DB","creation":"2025-05-20 12:47:36.536548","modified":"2025-08-22 16:09:15.193931","modified_by":"Administrator","owner":"Administrator","docstatus":0,"idx":0,"issingle":0,"is_virtual":0,"is_tree":0,"istable":0,"editable_grid":0,"track_changes":0,"module":"Westside","autoname":"Invoice-.##","naming_rule":"Expression (old style)","sort_field":"modified","sort_order":"DESC","read_only":0,"in_create":0,"allow_copy":0,"allow_rename":1,"allow_import":0,"hide_toolbar":0,"track_seen":0,"max_attachments":0,"engine":"InnoDB","is_submittable":0,"show_name_in_global_search":0,"custom":0,"beta":0,"has_web_view":0,"allow_guest_to_view":0,"email_append_to":0,"show_title_field_in_link":0,"migration_hash":"7cb45715efea1fcd4f343fe23977fdd7","translated_doctype":0,"is_calendar_and_gantt":0,"quick_entry":0,"track_views":0,"queue_in_background":0,"allow_events_in_timeline":0,"allow_auto_repeat":0,"make_attachments_public":0,"force_re_route_to_default_view":0,"show_preview_popup":0,"index_web_pages_for_search":1,"grid_page_length":0,"doctype":"DocType","fields":[{"docstatus":0,"doctype":"DocField","name":"new-docfield-kduetflwxa","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Data","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":1,"fieldname":"invoice_date","label":"Invoice Date","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-kjhrnnhvlh","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Text","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":2,"fieldname":"address","label":"Address","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-icbheepxdo","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Link","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":3,"fieldname":"hs_code","label":"HS code","options":"HS Code","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-sjhooydrff","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Data","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":4,"fieldname":"orgin_port","label":"Orgin Port","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-soheopdnvh","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Data","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":5,"fieldname":"destination_port","label":"Destination Port","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-pozoattgic","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Data","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":6,"fieldname":"bol","label":"BOL","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-fdohmychpf","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Date","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":7,"fieldname":"shipping_date","label":"Shipping Date","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-zdiudxrjuh","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Date","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":8,"fieldname":"due_date","label":"Due Date","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-esbfpmhlva","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Data","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":9,"fieldname":"quickbooks_customer_id","label":"QuickBooks Customer ID","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-ibbubxunqu","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Link","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":10,"fieldname":"customer_id","label":"Customer Id","options":"Customer DB","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-bcuohelhjq","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Data","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":11,"fieldname":"quickbooks_doc_number","label":"Quickbooks Doc Number","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-nssrwunuix","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Data","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":12,"fieldname":"carrier_booking_number","label":"Carrier Booking Number","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-xtetwpdrxd","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Link","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":13,"fieldname":"docket_id","label":"Docket Id","options":"Docket DB","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-fnorxprfld","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Small Text","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":14,"fieldname":"bill_to","label":"Bill To","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-zsipchilfb","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Check","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":15,"fieldname":"is_active","label":"Is Active","show_preview_popup":0,"columns":0,"default":"0","length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-pjbmrczjjp","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Column Break","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":16,"fieldname":"column_break_wkug","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-ifufzprylz","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Link","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":17,"fieldname":"booking_id","label":"Booking Id","options":"Booking Request","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-nlfygkoevn","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Data","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":18,"fieldname":"quickbooks_customer_name","label":"Quickbooks Customer Name","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-dngfajnjup","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Data","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":19,"fieldname":"email_address","label":"Email","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-emkzsvthod","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Data","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":20,"fieldname":"contact_number","label":"Contact","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-bfnjivhmyt","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Currency","precision":"2","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":21,"fieldname":"sub_total","label":"Sub Total","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-mbxbnlamhe","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Currency","precision":"2","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":22,"fieldname":"tax","label":"Tax","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-mlvqdcljjn","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Currency","precision":"2","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":23,"fieldname":"total_amount","label":"Total Amount","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-xieovhoddw","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Data","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":24,"fieldname":"invoice_number","label":"Invoice Number","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-dnouwwowwo","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Data","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":25,"fieldname":"incotern","label":"Incotern","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-eiolasyjzm","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Link","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":1,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":1,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":1,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":1,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":26,"fieldname":"amended_from","label":"Amended From","options":"Invoice DB","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-zamdrotdbo","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Data","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":27,"fieldname":"quickbooks_invoice_id","label":"QuickBooks Invoice ID","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-lfaulvoctz","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Data","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":28,"fieldname":"quickbooks_invoice_link","label":"QuickBooks Invoice Link","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-sqxommruld","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Data","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":29,"fieldname":"status","label":"Status","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-kdmuymzfdk","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Data","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":30,"fieldname":"invoice_pdf_name","label":"Invoice PDF Name","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-chuzllkkgq","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Data","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":31,"fieldname":"invoice_pdf_link","label":"Invoice PDF Link","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-sydaruwrpz","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Data","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":32,"fieldname":"customer_name","label":"Customer Name","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-lrxzsfzmwj","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Data","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":33,"fieldname":"uom","label":"Unit of Measurement","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-elepbxazpk","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Small Text","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":34,"fieldname":"comments","label":"Comments","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-yzxhalukso","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Data","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":35,"fieldname":"quickbooks_payment_id","label":"Quickbooks Payment Id","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-nmmhdqgfve","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Data","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":36,"fieldname":"purchase_order_number","label":"Purchase Order Number","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-koltkhaxke","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Data","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":37,"__run_link_triggers":1,"fieldname":"test_data","label":"Test Data"},{"docstatus":0,"doctype":"DocField","name":"new-docfield-ytrbzzwxgj","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Section Break","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":38,"fieldname":"attachments_section","label":"Attachments","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-ecjjmemmxc","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Table","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":39,"fieldname":"attachments","label":"Attachments","options":"Invoice Attachments","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-egyxbxjljd","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Section Break","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":40,"fieldname":"section_break_jsis","label":"Product/Services","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-ufzocezroi","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Table","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":41,"fieldname":"items","label":"Items","options":"Invoice Items","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-daeobadtwy","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Section Break","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":42,"fieldname":"data_section","label":"Data","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-zdljnflluu","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"JSON","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":43,"fieldname":"payload","label":"Payload","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-iqihotjscu","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"JSON","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":44,"fieldname":"response_data","label":"Response Data","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-fnzkfoctmr","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Table","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":45,"fieldname":"update_history","label":"Update History","options":"Invoice Update History","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-dswcyufffx","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Table","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":46,"fieldname":"payments","label":"Payments","options":"Invoice Payments","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-rkgarkdlia","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Table","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":47,"fieldname":"status_history","label":"Status History","options":"Invoice Status History","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-yzftkgmnrk","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Data","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":48,"fieldname":"payment_status","label":"Payment Status","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-xwszsvwkxg","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Currency","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":49,"fieldname":"balance_amount","label":"Balance Amount","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-sywuxcxlkq","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Currency","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":50,"fieldname":"paid_amount","label":"Paid Amount","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-omsxnzqfun","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Check","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":51,"fieldname":"is_overdue","label":"Is Overdue","show_preview_popup":0,"columns":0,"default":"0","length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-qgphpbafsm","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Int","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":52,"fieldname":"days_overdue","label":"Days Overdue","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-xvfcqwambf","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Datetime","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":53,"fieldname":"last_viewed_date","label":"Last Viewed Date","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-rduatashgc","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Datetime","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":54,"fieldname":"last_status_sync","label":"Last Status Sync","show_preview_popup":0,"columns":0,"length":0},{"docstatus":0,"doctype":"DocField","name":"new-docfield-jtcqmvizkw","__islocal":1,"__unsaved":1,"owner":"<EMAIL>","fieldtype":"Date","precision":"","non_negative":0,"hide_days":0,"hide_seconds":0,"reqd":0,"is_virtual":0,"search_index":0,"sort_options":0,"show_dashboard":0,"fetch_if_empty":0,"hidden":0,"show_on_timeline":0,"bold":0,"allow_in_quick_entry":0,"translatable":0,"print_hide":0,"print_hide_if_no_value":0,"report_hide":0,"collapsible":0,"hide_border":0,"in_list_view":0,"in_standard_filter":0,"in_preview":0,"in_filter":0,"in_global_search":0,"read_only":0,"allow_on_submit":0,"ignore_user_permissions":0,"allow_bulk_edit":0,"make_attachment_public":0,"permlevel":0,"ignore_xss_filter":0,"unique":0,"no_copy":0,"set_only_once":0,"remember_last_selected_value":0,"parent":"Invoice DB","parentfield":"fields","parenttype":"DocType","idx":55,"fieldname":"last_payment_date","label":"Last Payment Date","show_preview_popup":0,"columns":0,"length":0}],"actions":[],"states":[],"permissions":[{"name":"16ca3stbtn","creation":"2025-05-20 12:47:36.536548","modified":"2025-08-22 16:09:15.193931","modified_by":"<EMAIL>","owner":"Administrator","docstatus":0,"parent":"Invoice DB","parentfield":"permissions","parenttype":"DocType","idx":1,"permlevel":0,"role":"System Manager","read":1,"write":1,"create":1,"submit":0,"cancel":0,"delete":1,"amend":0,"report":1,"export":1,"import":0,"share":1,"print":1,"email":1,"if_owner":0,"select":1,"doctype":"DocPerm"},{"name":"16cd906s17","creation":"2025-05-20 12:47:36.536548","modified":"2025-08-22 16:09:15.193931","modified_by":"<EMAIL>","owner":"Administrator","docstatus":0,"parent":"Invoice DB","parentfield":"permissions","parenttype":"DocType","idx":2,"permlevel":0,"role":"Administrator","read":1,"write":1,"create":1,"submit":0,"cancel":0,"delete":1,"amend":0,"report":1,"export":1,"import":0,"share":1,"print":1,"email":1,"if_owner":0,"select":1,"doctype":"DocPerm"},{"name":"16cjvsqjov","creation":"2025-05-20 12:47:36.536548","modified":"2025-08-22 16:09:15.193931","modified_by":"<EMAIL>","owner":"Administrator","docstatus":0,"parent":"Invoice DB","parentfield":"permissions","parenttype":"DocType","idx":3,"permlevel":0,"role":"Guest","read":1,"write":1,"create":1,"submit":0,"cancel":0,"delete":1,"amend":0,"report":1,"export":1,"import":0,"share":1,"print":1,"email":1,"if_owner":0,"select":1,"doctype":"DocPerm"}],"links":[],"__unsaved":1}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-09-01 17:49:22,758 WARNING frappe Attachable 1233117 not found in QuickBooks - may have been already deleted or made inactive
Site: westside.local/
Form Dict: {'invoice_name': 'Invoice-37', 'update_data': '{"customer_id":"","quickbooks_customer_id":"1","invoice_data":{"docket_id":"","customer_name":"","quickbooks_customer_name":"Amy\'s Bird Sanctuary","email_address":"<EMAIL>","contact_number":"(650) 555-3311","address":"4581 Finch St., Bayshore, CA 94326","due_date":"2025-09-03","invoice_date":"2025-08-27","tax":0,"sub_total":0,"total_amount":0,"invoice_number":"","comments":"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.","bill_to":"4581 Finch St., Bayshore, CA 94326","shipping_date":"","bol":"","hs_code":"","orgin_port":"","destination_port":"","booking_id":"","incoterm":"","purchase_order_number":""},"items":[{"category_id":"","category_name":"","product_services":"Installation","product_description":"Installation of landscape design","quantity":1,"rate":50,"amount":50,"item_ref_id":"7","uom":"MT"},{"category_id":"","category_name":"","product_services":"Installation","product_description":"Installation of landscape design","quantity":1,"rate":50,"amount":50,"item_ref_id":"7","uom":"MT"}],"deleted_files":["/files/Ernakulam-Chelachuvadu-14895331-1320153.pdf","/files/Ernakulam-Chelachuvadu-14895331-1320153.pdf"]}', 'cmd': 'westside.westside.doctype.invoice_db.invoice_db.update_quickbooks_invoice'}
2025-09-10 11:19:12,472 ERROR frappe [Email Cron] Failed to send emails: [Errno 5] Input/output error
Site: westside.local
Form Dict: {}
2025-09-10 11:36:31,857 ERROR frappe [Email Cron] Failed to send emails: Task exceeded maximum timeout value (300 seconds)
Site: westside.local
Form Dict: {}
2025-09-11 17:23:43,538 ERROR frappe New Exception collected in error log
Site: westside.local/
Form Dict: {}
2025-09-17 16:13:52,793 ERROR frappe New Exception collected in error log
Site: westside.local/
Form Dict: {'page': '1', 'consignee': 'CUS-00145', 'carrier': 'HLCU', 'cmd': 'westside.www.Web_API.get_bill_of_lading.get_bill_of_lading'}
2025-09-17 16:16:36,856 ERROR frappe New Exception collected in error log
Site: westside.local/
Form Dict: {'consignee': 'FINSTER BLACK PRIVATE', 'cmd': 'westside.www.Web_API.get_bill_of_lading.get_bill_of_lading'}
2025-09-17 16:24:34,249 ERROR frappe New Exception collected in error log
Site: westside.local/
Form Dict: {'consignee': 'FINSTER BLACK PRIVATE', 'cmd': 'westside.www.Web_API.get_bill_of_lading.get_bill_of_lading'}
2025-09-17 16:24:39,071 ERROR frappe New Exception collected in error log
Site: westside.local/
Form Dict: {'page': '1', 'consignee': 'CUS-00145', 'cmd': 'westside.www.Web_API.get_bill_of_lading.get_bill_of_lading'}
2025-09-17 16:31:56,904 ERROR frappe New Exception collected in error log
Site: westside.local/
Form Dict: {'page': '1', 'consignee': 'CUS-00145', 'cmd': 'westside.www.Web_API.get_bill_of_lading.get_bill_of_lading'}
2025-09-17 16:32:00,452 ERROR frappe New Exception collected in error log
Site: westside.local/
Form Dict: {'page': '1', 'consignee': 'CUS-00145', 'cmd': 'westside.www.Web_API.get_bill_of_lading.get_bill_of_lading'}
2025-09-17 17:16:14,098 ERROR frappe New Exception collected in error log
Site: westside.local/
Form Dict: {'page': '1', 'consignee': 'CUS-00015', 'cmd': 'westside.www.Web_API.get_bill_of_lading.get_bill_of_lading'}
2025-09-17 17:16:19,450 ERROR frappe New Exception collected in error log
Site: westside.local/
Form Dict: {'page': '1', 'consignee': 'CUS-00145', 'cmd': 'westside.www.Web_API.get_bill_of_lading.get_bill_of_lading'}
2025-09-17 17:50:59,511 ERROR frappe New Exception collected in error log
Site: westside.local/
Form Dict: {'page': '1', 'consignee': 'CUS-00145', 'cmd': 'westside.www.Web_API.get_bill_of_lading.get_bill_of_lading'}
2025-09-17 17:51:12,509 ERROR frappe New Exception collected in error log
Site: westside.local/
Form Dict: {'page': '1', 'consignee': 'CUS-00145', 'cmd': 'westside.www.Web_API.get_bill_of_lading.get_bill_of_lading'}
2025-09-22 22:20:59,632 ERROR frappe New Exception collected in error log
Site: westside.local/
Form Dict: {'app_path': 'shipper/SHI-0002'}
2025-09-22 22:21:08,424 ERROR frappe New Exception collected in error log
Site: westside.local/
Form Dict: {'redirect-to': '/app/invoice-db'}
2025-09-22 22:21:15,822 ERROR frappe New Exception collected in error log
Site: westside.local/
Form Dict: {'app_path': 'customer-db'}
2025-09-22 22:23:54,293 ERROR frappe New Exception collected in error log
Site: westside.local/
Form Dict: {'app_path': 'customer-db'}
2025-09-22 22:23:57,128 ERROR frappe New Exception collected in error log
Site: westside.local/
Form Dict: {'app_path': 'customer-db'}
2025-09-22 22:23:58,157 ERROR frappe New Exception collected in error log
Site: westside.local/
Form Dict: {'app_path': 'customer-db'}
2025-09-22 22:41:42,119 ERROR frappe New Exception collected in error log
Site: westside.local/
Form Dict: {'app_path': 'customer-db'}
2025-09-22 22:41:55,261 ERROR frappe New Exception collected in error log
Site: westside.local/
Form Dict: {'app_path': 'customer-db'}
2025-09-22 22:41:56,456 ERROR frappe New Exception collected in error log
Site: westside.local/
Form Dict: {'app_path': 'customer-db'}
2025-09-22 23:00:28,611 ERROR frappe New Exception collected in error log
Site: westside.local/
Form Dict: {'redirect-to': '/app/customer-db'}
2025-09-23 12:07:01,835 ERROR frappe New Exception collected in error log
Site: westside.local/
Form Dict: {'redirect-to': '/app/customer-db'}
2025-09-23 12:47:53,693 ERROR frappe Could not take error snapshot: No module named 'wiki'
Site: westside.local/
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/app.py", line 100, in application
    init_request(request)
  File "/home/<USER>/westside-bench/apps/frappe/frappe/app.py", line 173, in init_request
    frappe.init(site=site, sites_path=_sites_path, force=True)
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 259, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 1653, in setup_module_map
    for module in get_module_list(app):
                  ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 1501, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 1466, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 1496, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 1437, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'wiki'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/error.py", line 86, in log_error_snapshot
    log_error(title=str(exception), defer_insert=True)
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/error.py", line 61, in log_error
    error_log = frappe.get_doc(
                ^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 1300, in get_doc
    return frappe.model.document.get_doc(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
                 ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/model/base_document.py", line 67, in get_controller
    return import_controller(doctype)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/model/base_document.py", line 89, in import_controller
    class_overrides = frappe.get_hooks("override_doctype_class")
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 1602, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/caching.py", line 47, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 1571, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 1437, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1310, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'wiki'
2025-09-23 12:48:01,152 ERROR frappe Could not take error snapshot: No module named 'wiki'
Site: westside.local/
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/app.py", line 100, in application
    init_request(request)
  File "/home/<USER>/westside-bench/apps/frappe/frappe/app.py", line 173, in init_request
    frappe.init(site=site, sites_path=_sites_path, force=True)
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 259, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 1653, in setup_module_map
    for module in get_module_list(app):
                  ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 1501, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 1466, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 1496, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 1437, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'wiki'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/error.py", line 86, in log_error_snapshot
    log_error(title=str(exception), defer_insert=True)
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/error.py", line 61, in log_error
    error_log = frappe.get_doc(
                ^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 1300, in get_doc
    return frappe.model.document.get_doc(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
                 ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/model/base_document.py", line 67, in get_controller
    return import_controller(doctype)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/model/base_document.py", line 89, in import_controller
    class_overrides = frappe.get_hooks("override_doctype_class")
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 1602, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/caching.py", line 47, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 1571, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 1437, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1310, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'wiki'
2025-09-23 12:48:03,471 ERROR frappe Could not take error snapshot: No module named 'wiki'
Site: westside.local/
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/app.py", line 100, in application
    init_request(request)
  File "/home/<USER>/westside-bench/apps/frappe/frappe/app.py", line 173, in init_request
    frappe.init(site=site, sites_path=_sites_path, force=True)
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 259, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 1653, in setup_module_map
    for module in get_module_list(app):
                  ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 1501, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 1466, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 1496, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 1437, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'wiki'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/error.py", line 86, in log_error_snapshot
    log_error(title=str(exception), defer_insert=True)
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/error.py", line 61, in log_error
    error_log = frappe.get_doc(
                ^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 1300, in get_doc
    return frappe.model.document.get_doc(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
                 ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/model/base_document.py", line 67, in get_controller
    return import_controller(doctype)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/model/base_document.py", line 89, in import_controller
    class_overrides = frappe.get_hooks("override_doctype_class")
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 1602, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/caching.py", line 47, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 1571, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 1437, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1310, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'wiki'
2025-09-23 12:48:16,308 ERROR frappe Could not take error snapshot: No module named 'wiki'
Site: westside.local/
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/app.py", line 100, in application
    init_request(request)
  File "/home/<USER>/westside-bench/apps/frappe/frappe/app.py", line 173, in init_request
    frappe.init(site=site, sites_path=_sites_path, force=True)
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 259, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 1653, in setup_module_map
    for module in get_module_list(app):
                  ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 1501, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 1466, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 1496, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 1437, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'wiki'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/error.py", line 86, in log_error_snapshot
    log_error(title=str(exception), defer_insert=True)
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/error.py", line 61, in log_error
    error_log = frappe.get_doc(
                ^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 1300, in get_doc
    return frappe.model.document.get_doc(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
                 ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/model/base_document.py", line 67, in get_controller
    return import_controller(doctype)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/model/base_document.py", line 89, in import_controller
    class_overrides = frappe.get_hooks("override_doctype_class")
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 1602, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/caching.py", line 47, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 1571, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 1437, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1310, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'wiki'
2025-09-23 12:49:06,032 ERROR frappe Could not take error snapshot: No module named 'wiki'
Site: westside.local/
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/app.py", line 100, in application
    init_request(request)
  File "/home/<USER>/westside-bench/apps/frappe/frappe/app.py", line 173, in init_request
    frappe.init(site=site, sites_path=_sites_path, force=True)
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 259, in init
    setup_module_map(include_all_apps=not (frappe.request or frappe.job or frappe.flags.in_migrate))
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 1653, in setup_module_map
    for module in get_module_list(app):
                  ^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 1501, in get_module_list
    return get_file_items(get_app_path(app_name, "modules.txt"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 1466, in get_app_path
    return get_pymodule_path(app_name, *joins)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 1496, in get_pymodule_path
    return abspath(join(dirname(get_module(scrub(modulename)).__file__ or ""), *joins))
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 1437, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'wiki'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/error.py", line 86, in log_error_snapshot
    log_error(title=str(exception), defer_insert=True)
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/error.py", line 61, in log_error
    error_log = frappe.get_doc(
                ^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 1300, in get_doc
    return frappe.model.document.get_doc(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
                 ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/model/base_document.py", line 67, in get_controller
    return import_controller(doctype)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/model/base_document.py", line 89, in import_controller
    class_overrides = frappe.get_hooks("override_doctype_class")
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 1602, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/utils/caching.py", line 47, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 1571, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/westside-bench/apps/frappe/frappe/__init__.py", line 1437, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1310, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'wiki'
2025-09-23 14:19:32,754 ERROR frappe New Exception collected in error log
Site: westside.local/
Form Dict: {'customer_id': 'CUS-00146', 'from_date': '2025-08-23', 'to_date': '2025-09-23', 'cmd': 'westside.www.Admin.reports.customer_report.get_customer_report'}
2025-09-23 14:19:39,340 ERROR frappe New Exception collected in error log
Site: westside.local/
Form Dict: {'customer_id': 'CUS-00146', 'from_date': '2025-08-23', 'to_date': '2025-09-23', 'cmd': 'westside.www.Admin.reports.customer_report.get_customer_report'}
2025-09-23 14:51:49,878 WARNING frappe Invoice 'WS-2025-071' not found for Docket 'DKT-0070'
Site: westside.local/
Form Dict: {'customer_id': 'CUS-00146', 'from_date': '2025-08-23', 'to_date': '2025-09-23', 'cmd': 'westside.www.Admin.reports.customer_report.get_customer_report'}
2025-09-23 14:51:49,915 WARNING frappe Invoice 'WS-2025-067' not found for Docket 'DKT-0069'
Site: westside.local/
Form Dict: {'customer_id': 'CUS-00146', 'from_date': '2025-08-23', 'to_date': '2025-09-23', 'cmd': 'westside.www.Admin.reports.customer_report.get_customer_report'}
2025-09-23 15:17:12,909 ERROR frappe New Exception collected in error log
Site: westside.local/
Form Dict: {'customer_id': 'CUS-00146', 'from_date': '2025-08-23', 'to_date': '2025-09-23', 'cmd': 'westside.www.Admin.reports.customer_report.export_customer_report'}
2025-09-24 17:49:57,034 ERROR frappe New Exception collected in error log
Site: westside.local/
Form Dict: {'vendor_id': 'VEN-00014', 'from_date': '01-08-2025', 'to_date': '24-09-2025', 'cmd': 'westside.www.Admin.reports.vendor_report.export_vendor_report'}
2025-09-26 12:53:03,813 ERROR frappe New Exception collected in error log
Site: westside.local/
Form Dict: {'customer_id': 'CUS-00146', 'from_date': '2025-08-26', 'to_date': '2025-09-26', 'cmd': 'westside.www.Admin.reports.customer_report.export_customer_report'}
2025-10-03 11:35:30,227 ERROR frappe New Exception collected in error log
Site: westside.local/
Form Dict: {'cmd': 'westside.www.Admin.customer.docket.attachments.attachments.send_docket'}
2025-10-03 11:54:36,180 ERROR frappe New Exception collected in error log
Site: westside.local/
Form Dict: {'cmd': 'westside.www.Admin.customer.docket.attachments.attachments.send_docket'}
2025-10-03 16:10:15,661 ERROR frappe New Exception collected in error log
Site: westside.local/
Form Dict: {'vendor_id': 'VEN-00014', 'from_date': '01-08-2025', 'to_date': '30-09-2025', 'cmd': 'westside.www.Admin.reports.vendor_report.export_vendor_report'}
