2025-03-26 16:02:00,842 INFO ipython === bench console session ===
2025-03-26 16:02:00,843 INFO ipython cr = frappe.get_doc('Carrier',{'parent':'BKG-0024'},['partyalias','partyname1'])
2025-03-26 16:02:00,843 INFO ipython cr = frappe.get_doc('Carrier',{'name':'partyalias'},['partyalias','partyname1'])
2025-03-26 16:02:00,843 INFO ipython cr = frappe.get_doc('Carrier',{'name':'CR-001'},['partyalias','partyname1'])
2025-03-26 16:02:00,844 INFO ipython cr
2025-03-26 16:02:00,844 INFO ipython cr.partalias
2025-03-26 16:02:00,844 INFO ipython cr = frappe.get_doc('Carrier',{'name':'CR-001'})
2025-03-26 16:02:00,844 INFO ipython cr.partalias
2025-03-26 16:02:00,845 INFO ipython cr.partyalias
2025-03-26 16:02:00,845 INFO ipython === session end ===
2025-03-26 16:07:01,232 INFO ipython === bench console session ===
2025-03-26 16:07:01,233 INFO ipython br = frappe.db.get_list('Booking Request',['booking_agent','shipper','place_of_carrier_receipt'])
2025-03-26 16:07:01,233 INFO ipython br
2025-03-26 16:07:01,233 INFO ipython for i in br:
    booking_agent = frappe.get_doc('Carrier',i.booking_agent)
    print(booking_agent.partyalias,booking_agent.partyname1)
    
2025-03-26 16:07:01,233 INFO ipython === session end ===
2025-03-26 17:03:12,591 INFO ipython === bench console session ===
2025-03-26 17:03:12,592 INFO ipython br = frappe.db.get_list('Booking Request',['booking_agent','shipper','place_of_carrier_receipt'])
2025-03-26 17:03:12,592 INFO ipython for i in br:
    booking_agent = frappe.get_doc('Carrier',i.booking_agent)
    print(booking_agent.partyalias,booking_agent.partyname1)
    
2025-03-26 17:03:12,593 INFO ipython for i in br:
    booking_agent = frappe.db.get_list('Carrier',{'name':i.booking_agent},[*])
2025-03-26 17:03:12,593 INFO ipython for i in br:
    booking_agent = frappe.db.get_list('Carrier',{'name':i.booking_agent},['*'])
    
2025-03-26 17:03:12,593 INFO ipython for i in br:
    booking_agent = frappe.db.get_list('Carrier',{'name':i.booking_agent},['*'])
    print(booking_agent)
    
2025-03-26 17:03:12,593 INFO ipython for i in br:
    booking_agent = frappe.db.get_list('Carrier',{'name':i.booking_agent},['partyname1','partalias'])
    print(booking_agent)
    
2025-03-26 17:03:12,594 INFO ipython for i in br:
    booking_agent = frappe.db.get_list('Carrier',{'name':i.booking_agent},['partyname1','partalias'])
    print(booking_agent[0])
    
2025-03-26 17:03:12,594 INFO ipython booking_agent = frappe.db.get_list('Carrier',{'name':'CR-001'},['partyalias','partyname1'])
2025-03-26 17:03:12,594 INFO ipython booking_agent
2025-03-26 17:03:12,594 INFO ipython booking_agent[0]
2025-03-26 17:03:12,594 INFO ipython booking_agent[0].partyalias
2025-03-26 17:03:12,595 INFO ipython === session end ===
2025-04-24 12:01:04,641 INFO ipython === bench console session ===
2025-04-24 12:01:04,648 INFO ipython frappe.db.count("HS Code")
2025-04-24 12:01:04,648 INFO ipython === session end ===
2025-04-24 12:21:28,496 INFO ipython === bench console session ===
2025-04-24 12:21:28,497 INFO ipython frappe.db.count("UNLOCODE Locations")
2025-04-24 12:21:28,497 INFO ipython === session end ===
2025-09-22 17:26:22,133 INFO ipython === bench console session ===
2025-09-22 17:26:22,134 INFO ipython frappe.db.sql("""
    SELECT pi.partner_name, COUNT(*) AS count
    FROM `tabPartner Information` pi
    INNER JOIN `tabBill of Lading` bol ON pi.parent = bol.name
    WHERE pi.partner_role = 'Consignee'
    GROUP BY pi.partner_name
    ORDER BY count DESC
""", as_dict=True)
2025-09-22 17:26:22,134 INFO ipython === session end ===
2025-09-22 22:26:11,997 INFO ipython === bench console session ===
2025-09-22 22:26:12,004 INFO ipython import frappe
frappe.db.delete("Module Def", {"app_name": "wiki"})
frappe.db.commit()
2025-09-22 22:26:12,004 INFO ipython === session end ===
2025-09-22 22:39:22,594 INFO ipython === bench console session ===
2025-09-22 22:39:22,602 INFO ipython import frappe
frappe.setup_module_map()
frappe.get_single("Installed Applications").save()
2025-09-22 22:39:22,602 INFO ipython === session end ===
2025-09-22 22:40:55,439 INFO ipython === bench console session ===
2025-09-22 22:40:55,440 INFO ipython import frappe

# Remove wiki from app hooks
if "wiki" in frappe.get_installed_apps():
    installed = frappe.get_installed_apps()
    installed.remove("wiki")
    frappe.db.set_value("DefaultValue", "installed_apps", None, str(installed))
    frappe.db.commit()
    
2025-09-22 22:40:55,440 INFO ipython === session end ===
2025-09-22 23:16:28,496 INFO ipython === bench console session ===
2025-09-22 23:16:28,497 INFO ipython frappe.get_installed_apps()
2025-09-22 23:16:28,497 INFO ipython # Get current installed apps
installed_apps = frappe.get_installed_apps()
print("Current apps:", installed_apps)

# Remove wiki if it exists
if 'wiki' in installed_apps:
    # Update the site config to remove wiki
    frappe.db.delete("Installed Application", {"app_name": "wiki"})
    frappe.db.commit()
    
    # Also remove from hooks table if it exists
    frappe.db.delete("Module Def", {"app_name": "wiki"})
    frappe.db.commit()
    
2025-09-22 23:16:28,497 INFO ipython === session end ===
2025-09-22 23:17:53,846 INFO ipython === bench console session ===
2025-09-22 23:17:53,846 INFO ipython # Check if there are any remaining wiki references in the database
frappe.db.sql("SELECT * FROM `tabModule Def` WHERE app_name = 'wiki'")
2025-09-22 23:17:53,846 INFO ipython # Check installed applications table
frappe.db.sql("SELECT * FROM `tabInstalled Application` WHERE app_name = 'wiki'")
2025-09-22 23:17:53,847 INFO ipython # Clean up any remaining wiki references
frappe.db.delete("Module Def", {"app_name": "wiki"})
frappe.db.delete("Installed Application", {"app_name": "wiki"})
frappe.db.commit()
2025-09-22 23:17:53,847 INFO ipython # Clear all caches
frappe.clear_cache()
2025-09-22 23:17:53,847 INFO ipython # Exit the console
exit()
2025-09-22 23:17:53,847 INFO ipython === session end ===
2025-09-22 23:22:38,687 INFO ipython === bench console session ===
2025-09-22 23:22:38,687 INFO ipython # Get the actual installed apps and update the configuration
installed_apps = ['frappe', 'westside']

# Update site config to ensure only these apps are recognized
site_config = frappe.get_site_config()
site_config['installed_apps'] = installed_apps

# Save the configuration
frappe.get_site_config(save=True)

# Force clear all caches
import frappe.sessions
frappe.sessions.clear_all()
frappe.clear_cache()

# Check what apps are now recognized
print("Apps after cleanup:", frappe.get_installed_apps())

exit()
2025-09-22 23:22:38,687 INFO ipython # Get the actual installed apps
installed_apps = ['frappe', 'westside']

# Update site config properly
import frappe
frappe.conf.installed_apps = installed_apps

# Update the site_config.json file directly
import json
import os

site_config_path = frappe.get_site_path('site_config.json')
with open(site_config_path, 'r') as f:
    site_config = json.load(f)

site_config['installed_apps'] = installed_apps

with open(site_config_path, 'w') as f:
    json.dump(site_config, f, indent=1)

print("Updated site config with apps:", installed_apps)

# Force clear all caches
import frappe.sessions
frappe.sessions.clear_all()
frappe.clear_cache()

# Check what apps are now recognized
print("Apps after cleanup:", frappe.get_installed_apps())
2025-09-22 23:22:38,687 INFO ipython # Completely reset the installed apps in the database
frappe.db.sql("DELETE FROM `tabInstalled Application`")
frappe.db.sql("DELETE FROM `tabModule Def` WHERE app_name NOT IN ('frappe', 'westside')")

# Reinstall the core apps
frappe.db.sql("INSERT INTO `tabInstalled Application` (name, app_name) VALUES ('frappe', 'frappe')")
frappe.db.sql("INSERT INTO `tabInstalled Application` (name, app_name) VALUES ('westside', 'westside')")

frappe.db.commit()
frappe.clear_cache()

print("Reset complete. Current apps:", frappe.get_installed_apps())

exit()
2025-09-22 23:22:38,687 INFO ipython === session end ===
2025-09-22 23:24:39,367 INFO ipython === bench console session ===
2025-09-22 23:24:39,368 INFO ipython # Check the actual database content
print("Database Installed Applications:")
print(frappe.db.sql("SELECT * FROM `tabInstalled Application`", as_dict=True))

print("\nDatabase Module Def:")  
print(frappe.db.sql("SELECT * FROM `tabModule Def`", as_dict=True))

# Check the site config file content
import json
site_config_path = frappe.get_site_path('site_config.json')
with open(site_config_path, 'r') as f:
    site_config = json.load(f)
print("\nSite config installed_apps:", site_config.get('installed_apps'))

# Check if there's a cached version
print("\nFrappe conf apps:", getattr(frappe.conf, 'installed_apps', 'Not set'))
2025-09-22 23:24:39,368 INFO ipython # Check what get_installed_apps() actually returns
print("get_installed_apps() returns:", frappe.get_installed_apps())

# Let's trace where this is coming from
import frappe
print("frappe.local.conf:", getattr(frappe.local, 'conf', {}))

# Check if there's a bench-level apps.txt
import os
bench_path = frappe.utils.get_bench_path()
print("Bench path:", bench_path)

# Check for apps.txt at different levels
apps_txt_paths = [
    os.path.join(bench_path, 'sites', 'apps.txt'),
    os.path.join(bench_path, 'sites', 'common_site_config.json'),
]

for path in apps_txt_paths:
    if os.path.exists(path):
        print(f"\nFound {path}:")
        with open(path, 'r') as f:
            print(f.read())
    else:
        print(f"\n{path} does not exist")

exit()
2025-09-22 23:24:39,368 INFO ipython === session end ===
2025-09-22 23:30:55,224 INFO ipython === bench console session ===
2025-09-22 23:30:55,224 INFO ipython import sys
# Check if wiki is in Python's module cache
wiki_modules = [mod for mod in sys.modules.keys() if 'wiki' in mod.lower()]
print("Wiki-related modules in cache:", wiki_modules)

# Remove any wiki modules from cache
for mod in wiki_modules:
    if mod in sys.modules:
        del sys.modules[mod]
        print(f"Removed {mod} from cache")

# Also check frappe's internal caches
if hasattr(frappe.local, 'installed_apps'):
    print("Local installed_apps:", frappe.local.installed_apps)

# Clear frappe's local cache
for attr in ['installed_apps', 'app_modules']:
    if hasattr(frappe.local, attr):
        delattr(frappe.local, attr)

exit()
2025-09-22 23:30:55,224 INFO ipython === session end ===
2025-09-22 23:32:09,662 INFO ipython === bench console session ===
2025-09-22 23:32:09,662 INFO ipython # Let's trace exactly what apps the migration system thinks should be processed
import frappe.migrate

# Check what the SiteMigration class thinks the apps are
migrator = frappe.migrate.SiteMigration('westside.local')
print("Apps from migrator:", getattr(migrator, 'apps', 'Not set'))

# Check the migrate.py logic - it gets apps from somewhere
# Let's see what get_all_apps() returns
try:
    all_apps = frappe.get_all_apps()
    print("get_all_apps():", all_apps)
except:
    print("get_all_apps() failed")

# Check what the migration process is actually using
import inspect
migrate_file = '/home/<USER>/westside-bench/apps/frappe/frappe/migrate.py'
with open(migrate_file, 'r') as f:
    content = f.read()
    # Look for where apps are determined
    lines = content.split('\n')
    for i, line in enumerate(lines):
        if 'app_name=app' in line or 'get_all_apps' in line:
            print(f"Line {i+1}: {line.strip()}")

exit()
2025-09-22 23:32:09,662 INFO ipython === session end ===
2025-09-22 23:33:18,371 INFO ipython === bench console session ===
2025-09-22 23:33:18,372 INFO ipython # Let's see exactly what get_installed_apps() is returning step by step
print("Step 1 - Direct call:", frappe.get_installed_apps())

# Check if there are any hooks or overrides affecting this
import frappe
print("Step 2 - From database directly:")
apps_from_db = frappe.db.sql("SELECT app_name FROM `tabInstalled Application`", pluck=True)
print("Database apps:", apps_from_db)

# Check if doppio is somehow adding wiki as a dependency
print("Step 3 - Check doppio dependencies:")
try:
    import doppio
    doppio_path = doppio.__file__
    print(f"Doppio path: {doppio_path}")
except:
    print("Could not import doppio")

# Let's also check what happens when we try to load doppio hooks
print("Step 4 - Check doppio hooks:")
try:
    doppio_hooks = frappe.get_hooks(app_name="doppio")
    print("Doppio hooks loaded successfully")
except Exception as e:
    print(f"Error loading doppio hooks: {e}")

exit()
2025-09-22 23:33:18,372 INFO ipython === session end ===
2025-09-22 23:38:07,033 INFO ipython === bench console session ===
2025-09-22 23:38:07,033 INFO ipython frappe.db.delete("Installed Application", {"app_name": "wiki"})
frappe.db.commit()
2025-09-22 23:38:07,033 INFO ipython === session end ===
2025-09-23 12:05:09,510 INFO ipython === bench console session ===
2025-09-23 12:05:09,512 INFO ipython frappe.db.sql("""
    SELECT pi.partner_name, COUNT(*) AS count
    FROM `tabPartner Information` pi
    INNER JOIN `tabBill of Lading` bol ON pi.parent = bol.name
    WHERE pi.partner_role = 'Consignee'
    GROUP BY pi.partner_name
    ORDER BY count DESC
""", as_dict=True)
2025-09-23 12:05:09,512 INFO ipython === session end ===
2025-09-24 10:12:51,297 INFO ipython === bench console session ===
2025-09-24 10:12:51,304 INFO ipython frappe.db.sql("""
    SELECT pi.partner_name, COUNT(*) AS count
    FROM `tabPartner Information` pi
    INNER JOIN `tabBill of Lading` bol ON pi.parent = bol.name
    WHERE pi.partner_role = 'Consignee'
    GROUP BY pi.partner_name
    ORDER BY count DESC
""", as_dict=True)
2025-09-24 10:12:51,304 INFO ipython frappe.db.sql("""
    SELECT pi.partner_name,pi.name COUNT(*) AS count
    FROM `tabPartner Information` pi
    INNER JOIN `tabBill of Lading` bol ON pi.parent = bol.name
    WHERE pi.partner_role = 'Consignee'
    GROUP BY pi.partner_name
    ORDER BY count DESC
""", as_dict=True)
2025-09-24 10:12:51,305 INFO ipython frappe.db.sql("""
    SELECT pi.partner_name,pi.name, COUNT(*) AS count
    FROM `tabPartner Information` pi
    INNER JOIN `tabBill of Lading` bol ON pi.parent = bol.name
    WHERE pi.partner_role = 'Consignee'
    GROUP BY pi.partner_name
    ORDER BY count DESC
""", as_dict=True)
2025-09-24 10:12:51,305 INFO ipython frappe.db.sql("""
    SELECT 
        pi.partner_name,
        pi.name AS partner_info_id,
        bol.customer_id AS customer_id,
        COUNT(*) AS count
    FROM `tabPartner Information` pi
    INNER JOIN `tabBill of Lading` bol 
        ON pi.parent = bol.name
    INNER JOIN `tabCustomer DB` cust 
        ON bol.customer_id = cust.name
    WHERE pi.partner_role = 'Consignee'
    GROUP BY pi.partner_name, pi.name, bol.customer_id
    ORDER BY count DESC
""", as_dict=True)
2025-09-24 10:12:51,305 INFO ipython frappe.db.sql("""
    SELECT 
        pi.partner_name,          -- this should be Customer DB PK if linked
        pi.name AS partner_info_id,
        COUNT(*) AS count
    FROM `tabPartner Information` pi
    INNER JOIN `tabBill of Lading` bol 
        ON bol.partner_table_name = pi.name
    WHERE pi.partner_role = 'Consignee'
    GROUP BY pi.partner_name, pi.name
    ORDER BY count DESC
""", as_dict=True)
2025-09-24 10:12:51,305 INFO ipython === session end ===
2025-10-03 13:26:05,472 INFO ipython === bench console session ===
2025-10-03 13:26:05,480 INFO ipython import frappe

email_account = frappe.get_doc("Email Account", "Westside")
email_account.db_set("password", "")
frappe.db.commit()
2025-10-03 13:26:05,480 INFO ipython === session end ===
